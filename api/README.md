# API 接口文档

## 概述

本项目的 API 接口层已根据 `json/enhanced_apis.json` 和 `json/API_Documentation.md` 的规范进行了全面优化和重构。所有接口文件遵循统一的代码规范和架构模式。

## 目录结构

```
api/
├── index.js          # 统一导出文件，提供所有API的导入入口
├── login.js          # 用户登录和认证相关API
├── product.js        # 商品管理相关API
├── cart.js           # 购物车管理相关API
├── order.js          # 订单管理相关API
├── afterSales.js     # 售后服务管理相关API
├── invoice.js        # 发票管理相关API
├── address.js        # 地址管理相关API
├── coupon.js         # 优购码管理相关API
└── README.md         # 本文档
```

## 使用方式

### 1. 按模块导入

```javascript
// 导入特定模块的API
import { getProductList, getProductInfo } from '@/api/product.js'
import { addToCart, getCartList } from '@/api/cart.js'
import { placeOrder, getOrderList } from '@/api/order.js'
```

### 2. 统一导入

```javascript
// 从统一入口导入
import { 
  getProductList, 
  addToCart, 
  placeOrder,
  login 
} from '@/api/index.js'
```

### 3. 按分类导入

```javascript
// 导入整个模块
import { ProductAPI, CartAPI, OrderAPI } from '@/api/index.js'

// 使用
const products = await ProductAPI.getProductList()
const cartItems = await CartAPI.getCartList({ userId: 123 })
```

## API 模块说明

### 1. 用户登录模块 (login.js)

**主要功能：**
- 微信登录
- 手机号授权
- 用户信息获取
- 退出登录

**核心API：**
- `login({ code })` - 微信登录
- `getPhoneNumber({ openId, code })` - 获取手机号
- `getUserInfo()` - 获取用户信息
- `logout()` - 退出登录
- `phoneAuthLogin({ code, phoneCode })` - 完整登录流程

### 2. 商品管理模块 (product.js)

**主要功能：**
- 商品列表查询
- 商品详情获取
- 商品分类管理
- 商品搜索

**核心API：**
- `getProductList(params)` - 查询商品列表
- `getProductInfo({ productId })` - 获取商品详情
- `getProductCategories()` - 获取商品分类
- `searchProducts(params)` - 商品搜索
- `getNewProducts(params)` - 获取新品
- `getHotProducts(params)` - 获取热卖商品

### 3. 购物车管理模块 (cart.js)

**主要功能：**
- 购物车商品管理
- 购物车统计

**核心API：**
- `getCartList({ userId })` - 获取购物车列表
- `addToCart({ productId, productAttrValueId, num })` - 添加到购物车
- `removeFromCart({ cartIds })` - 删除购物车商品
- `getCartCount({ userId })` - 获取购物车统计

### 4. 订单管理模块 (order.js)

**主要功能：**
- 订单创建和管理
- 订单状态查询
- 订单操作（取消、删除等）

**核心API：**
- `placeOrder(orderData)` - 下订单
- `getOrderList({ userId, orderStatus, pageNum, pageSize })` - 获取订单列表
- `getOrderInfo({ orderId })` - 获取订单详情
- `cancelOrder({ orderIds })` - 取消订单
- `removeOrder({ orderIds })` - 删除订单

### 5. 售后服务模块 (afterSales.js)

**主要功能：**
- 售后申请管理
- 售后状态跟踪
- 快递信息填写

**核心API：**
- `getAfterSalesList({ searchValue, status })` - 获取售后列表
- `applyAfterSales(serviceData)` - 申请售后
- `getAfterSalesInfo({ asServiceId })` - 获取售后详情
- `writeDeliveryInfo({ asServiceId, deliveryName, deliveryNo })` - 填写快递信息

### 6. 发票管理模块 (invoice.js)

**主要功能：**
- 发票申请
- 发票信息查询
- 发票数据验证

**核心API：**
- `applyInvoice(invoiceData)` - 申请发票
- `getInvoiceInfo({ orderId })` - 获取发票信息
- `checkInvoiceEligibility({ orderId })` - 检查开票资格
- `validateInvoiceData(invoiceData)` - 验证发票数据

### 7. 地址管理模块 (address.js)

**主要功能：**
- 收货地址管理
- 城市数据获取
- 地址数据验证

**核心API：**
- `getAddressList()` - 获取地址列表
- `addAddress(addressData)` - 新增地址
- `editAddress(addressData)` - 修改地址
- `deleteAddress({ userAddressIds })` - 删除地址
- `getCityList()` - 获取城市列表
- `setDefaultAddress({ userAddressId })` - 设置默认地址

### 8. 优购码管理模块 (coupon.js)

**主要功能：**
- 优购码查询
- 优购码状态管理
- 优购码可用性检查

**核心API：**
- `getCouponList(params)` - 获取优购码列表
- `getUnusedCoupons()` - 获取未使用优购码
- `checkCouponAvailability({ couponCode, orderAmount })` - 检查优购码可用性
- `getCouponStatistics()` - 获取优购码统计

## 代码规范

### 1. 函数命名规范

- 使用驼峰命名法
- 动词开头，语义明确
- 统一前缀：`get`（查询）、`add`（新增）、`update`（更新）、`remove`（删除）

### 2. 参数规范

- 使用对象解构传参
- 必填参数直接解构，可选参数提供默认值
- 复杂参数使用完整对象传递

### 3. 返回值规范

- 所有函数返回 Promise
- 保持原始 API 响应格式
- 错误统一通过 Promise.reject 抛出

### 4. 文档规范

- 使用 JSDoc 格式注释
- 详细描述参数类型和含义
- 提供返回值说明

## 错误处理

所有 API 调用的错误处理由 `utils/request.js` 统一管理，包括：

- 网络超时重试
- Token 失效处理
- 统一错误提示
- 响应数据验证

## 配置说明

### 请求头配置

部分 API 需要使用 `application/json` 内容类型，已在 `utils/request.js` 中配置：

```javascript
const URL_JSON_HEADER_API = [
  "/store/order/placeOrder",
  "/store/apply/applyOpenBlueInvoice", 
  "/store/asService/apply",
  "/store/asService/writeDeliveryNo",
  "/system/address/add",
  "/system/address/edit"
]
```

### 环境配置

API 基础地址通过 `utils/env.js` 配置，支持开发、测试、生产环境切换。

## 使用示例

### 完整的商品购买流程

```javascript
import { 
  login, 
  getPhoneNumber,
  getProductList, 
  addToCart, 
  placeOrder 
} from '@/api/index.js'

// 1. 用户登录
const loginResult = await login({ code: 'wx_code' })
const phoneResult = await getPhoneNumber({ 
  openId: loginResult.data.openId, 
  code: 'phone_code' 
})

// 2. 浏览商品
const products = await getProductList({ isNew: '1', pageSize: 10 })

// 3. 添加到购物车
await addToCart({ 
  productId: 123, 
  productAttrValueId: 456, 
  num: 2 
})

// 4. 下订单
const orderResult = await placeOrder({
  // 订单数据
})
```

## 注意事项

1. **认证要求**：除登录接口外，其他接口都需要有效的 Authorization token
2. **参数验证**：建议在调用 API 前进行参数验证，部分模块提供了验证函数
3. **错误处理**：合理处理 API 调用异常，提供用户友好的错误提示
4. **性能优化**：合理使用缓存，避免重复请求相同数据
5. **版本兼容**：API 接口保持向后兼容，新增功能通过可选参数实现

---

*文档最后更新时间: 2025-07-24*

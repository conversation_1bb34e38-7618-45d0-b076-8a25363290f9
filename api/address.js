import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  address: BASE_URL
}

/**
 * 查询地址列表
 * @returns {Promise<Object>} 地址列表数据
 */
export const getAddressList = async (data) => {
  return await request({
    url: `${API.address}/system/address/list`,
    method: "GET",
		data
  })
}

/**
 * 查询地址信息
 * @param {Object} params - 查询参数
 * @param {number} params.userAddressId - 用户地址ID
 * @returns {Promise<Object>} 地址详细信息
 */
export const getAddressInfo = async (data) => {
  return await request({
    url: `${API.address}/system/address/getInfo`,
    method: "GET",
		data
  })
}

/**
 * 新增地址
 * @param {Object} addressData - 地址数据
 * @param {string} addressData.receiverName - 收货人姓名
 * @param {string} addressData.receiverPhone - 收货人电话
 * @param {string} addressData.province - 省份
 * @param {string} addressData.city - 城市
 * @param {string} addressData.district - 区县
 * @param {string} addressData.detailAddress - 详细地址
 * @param {boolean} [addressData.isDefault] - 是否默认地址
 * @returns {Promise<Object>} 新增结果
 */
export const addAddress = async (addressData) => {
  return await request({
    url: `${API.address}/system/address/add`,
    method: "POST",
    data: addressData
  })
}

/**
 * 修改地址
 * @param {Object} addressData - 地址数据
 * @param {number} addressData.userAddressId - 用户地址ID
 * @param {string} addressData.receiverName - 收货人姓名
 * @param {string} addressData.receiverPhone - 收货人电话
 * @param {string} addressData.province - 省份
 * @param {string} addressData.city - 城市
 * @param {string} addressData.district - 区县
 * @param {string} addressData.detailAddress - 详细地址
 * @param {boolean} [addressData.isDefault] - 是否默认地址
 * @returns {Promise<Object>} 修改结果
 */
export const editAddress = async (addressData) => {
  return await request({
    url: `${API.address}/system/address/edit`,
    method: "POST",
    data: addressData
  })
}

/**
 * 删除地址
 * @param {Object} params - 删除参数
 * @param {number|number[]} params.userAddressIds - 用户地址ID数组
 * @returns {Promise<Object>} 删除结果
 */
export const deleteAddress = async (data) => {
  return await request({
    url: `${API.address}/system/address/remove`,
    method: "POST",
		data
  })
}

/**
 * 城市列表
 * @returns {Promise<Object>} 城市列表数据
 */
export const getCityList = async () => {
  return await request({
    url: `${API.address}/system/city/list`,
    method: "GET"
  })
}

/**
 * 城市树数据
 * @returns {Promise<Object>} 城市树形结构数据
 */
export const getCityTreeList = async () => {
  return await request({
    url: `${API.address}/system/city/treeList`,
    method: "GET"
  })
}

/**
 * 设置默认地址
 * @param {Object} params - 设置参数
 * @param {number} params.userAddressId - 用户地址ID
 * @returns {Promise<Object>} 设置结果
 */
export const setDefaultAddress = async ({ userAddressId }) => {
  // 先获取地址信息，然后更新为默认地址
  const addressInfo = await getAddressInfo({ userAddressId })

  if (addressInfo && addressInfo.data) {
    return await editAddress({
      ...addressInfo.data,
      isDefault: true
    })
  }

  throw new Error("地址信息不存在")
}

/**
 * 获取默认地址
 * @returns {Promise<Object>} 默认地址信息
 */
export const getDefaultAddress = async () => {
  const result = await getAddressList()

  if (result && result.data && result.data.length > 0) {
    const defaultAddress = result.data.find((address) => address.isDefault)

    if (defaultAddress) {
      return {
        code: 200,
        msg: "获取成功",
        data: defaultAddress
      }
    }

    // 如果没有默认地址，返回第一个地址
    return {
      code: 200,
      msg: "获取成功",
      data: result.data[0]
    }
  }

  return {
    code: 200,
    msg: "暂无地址",
    data: null
  }
}

/**
 * 批量删除地址
 * @param {Object} params - 删除参数
 * @param {number[]} params.userAddressIds - 用户地址ID数组
 * @returns {Promise<Object>} 删除结果
 */
export const batchDeleteAddress = async ({ userAddressIds }) => {
  return await deleteAddress({ userAddressIds })
}

/**
 * 验证地址数据
 * @param {Object} addressData - 地址数据
 * @returns {Object} 验证结果
 */
export const validateAddressData = (addressData) => {
  const errors = []

  // 必填字段验证
  if (!addressData.receiverName || addressData.receiverName.trim() === "") {
    errors.push("收货人姓名不能为空")
  }

  if (!addressData.receiverPhone || addressData.receiverPhone.trim() === "") {
    errors.push("收货人电话不能为空")
  }

  if (!addressData.province || addressData.province.trim() === "") {
    errors.push("省份不能为空")
  }

  if (!addressData.city || addressData.city.trim() === "") {
    errors.push("城市不能为空")
  }

  if (!addressData.district || addressData.district.trim() === "") {
    errors.push("区县不能为空")
  }

  if (!addressData.detailAddress || addressData.detailAddress.trim() === "") {
    errors.push("详细地址不能为空")
  }

  // 手机号格式验证
  const phoneRegex = /^1[3-9]\d{9}$/
  if (addressData.receiverPhone && !phoneRegex.test(addressData.receiverPhone)) {
    errors.push("收货人电话格式不正确")
  }

  // 姓名长度验证
  if (addressData.receiverName && addressData.receiverName.length > 20) {
    errors.push("收货人姓名不能超过20个字符")
  }

  // 详细地址长度验证
  if (addressData.detailAddress && addressData.detailAddress.length > 100) {
    errors.push("详细地址不能超过100个字符")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  afterSales: BASE_URL
}

/**
 * 查询APP端售后服务列表
 * @param {Object} params - 查询参数
 * @param {string} params.searchValue - 搜索值，用于筛选相关售后服务
 * @param {string} params.status - 售后服务状态，用于筛选对应状态的售后服务
 * @returns {Promise<Object>} 售后服务列表
 */
export const getAfterSalesList = async ({ searchValue, status }) => {
  return await request({
    url: `${API.afterSales}/store/asService/appList`,
    method: "GET",
    queryData: { searchValue, status }
  })
}

/**
 * 申请售后服务
 * @param {Object} serviceData - 售后服务申请数据
 * @returns {Promise<Object>} 申请结果
 */
export const applyAfterSales = async (serviceData) => {
  return await request({
    url: `${API.afterSales}/store/asService/apply`,
    method: "POST",
    data: serviceData
  })
}

/**
 * 取消售后服务
 * @param {Object} params - 取消参数
 * @param {number} params.asServiceId - 售后服务ID
 * @returns {Promise<Object>} 取消结果
 */
export const cancelAfterSales = async ({ asServiceId }) => {
  return await request({
    url: `${API.afterSales}/store/asService/cancle`,
    method: "POST",
    queryData: { asServiceId }
  })
}

/**
 * 获取售后服务详细信息
 * @param {Object} params - 查询参数
 * @param {number} params.asServiceId - 售后服务ID
 * @returns {Promise<Object>} 售后服务详细信息
 */
export const getAfterSalesInfo = async ({ asServiceId }) => {
  return await request({
    url: `${API.afterSales}/store/asService/getInfo`,
    method: "GET",
    queryData: { asServiceId }
  })
}

/**
 * 删除售后服务
 * @param {Object} params - 删除参数
 * @param {number} params.asServiceId - 售后服务ID
 * @returns {Promise<Object>} 删除结果
 */
export const removeAfterSales = async ({ asServiceId }) => {
  return await request({
    url: `${API.afterSales}/store/asService/remove`,
    method: "POST",
    queryData: { asServiceId }
  })
}

/**
 * 填写快递单号
 * @param {Object} params - 快递信息参数
 * @param {number} params.asServiceId - 售后服务ID
 * @param {string} params.deliveryName - 快递公司名称
 * @param {string} params.deliveryNo - 快递单号
 * @returns {Promise<Object>} 填写结果
 */
export const writeDeliveryInfo = async ({ asServiceId, deliveryName, deliveryNo }) => {
  return await request({
    url: `${API.afterSales}/store/asService/writeDeliveryNo`,
    method: "POST",
    queryData: { asServiceId, deliveryName, deliveryNo },
    data: ""
  })
}

/**
 * 根据状态查询售后申请单
 * @param {Object} params - 查询参数
 * @param {string} params.searchValue - 搜索关键词
 * @param {string} params.status - 售后状态（待处理、处理中、已完成、已取消等）
 * @returns {Promise<Object>} 售后申请单列表
 */
export const getAfterSalesByStatus = async ({ searchValue, status }) => {
  return await getAfterSalesList({ searchValue, status })
}

/**
 * 获取待处理售后申请
 * @param {Object} params - 查询参数
 * @param {string} [params.searchValue=""] - 搜索关键词
 * @returns {Promise<Object>} 待处理售后申请列表
 */
export const getPendingAfterSales = async ({ searchValue = "" }) => {
  return await getAfterSalesByStatus({ searchValue, status: "0" })
}

/**
 * 获取处理中售后申请
 * @param {Object} params - 查询参数
 * @param {string} [params.searchValue=""] - 搜索关键词
 * @returns {Promise<Object>} 处理中售后申请列表
 */
export const getProcessingAfterSales = async ({ searchValue = "" }) => {
  return await getAfterSalesByStatus({ searchValue, status: "1" })
}

/**
 * 获取已完成售后申请
 * @param {Object} params - 查询参数
 * @param {string} [params.searchValue=""] - 搜索关键词
 * @returns {Promise<Object>} 已完成售后申请列表
 */
export const getCompletedAfterSales = async ({ searchValue = "" }) => {
  return await getAfterSalesByStatus({ searchValue, status: "2" })
}

/**
 * 获取已取消售后申请
 * @param {Object} params - 查询参数
 * @param {string} [params.searchValue=""] - 搜索关键词
 * @returns {Promise<Object>} 已取消售后申请列表
 */
export const getCancelledAfterSales = async ({ searchValue = "" }) => {
  return await getAfterSalesByStatus({ searchValue, status: "3" })
}

/**
 * 批量取消售后申请
 * @param {Object} params - 取消参数
 * @param {number[]} params.asServiceIds - 售后服务ID数组
 * @returns {Promise<Object[]>} 批量取消结果
 */
export const batchCancelAfterSales = async ({ asServiceIds }) => {
  const promises = asServiceIds.map((asServiceId) => cancelAfterSales({ asServiceId }))
  return await Promise.all(promises)
}

/**
 * 批量删除售后申请
 * @param {Object} params - 删除参数
 * @param {number[]} params.asServiceIds - 售后服务ID数组
 * @returns {Promise<Object[]>} 批量删除结果
 */
export const batchRemoveAfterSales = async ({ asServiceIds }) => {
  const promises = asServiceIds.map((asServiceId) => removeAfterSales({ asServiceId }))
  return await Promise.all(promises)
}

import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  cart: BASE_URL
}

/**
 * 查询购物车列表
 * @param {Object} params - 查询参数
 * @param {number} [params.userId] - 用户ID，用于筛选对应用户的购物车列表
 * @returns {Promise<Object>} 购物车列表数据
 */
export const getCartList = async (params = {}) => {
  return await request({
    url: `${API.cart}/store/cart/list`,
    method: "GET",
    queryData: params
  })
}

export const updateToCart = async (data) => {
  return await request({
    url: `${API.cart}/store/cart/update`,
    method: "POST",
    data,
  })
}

/**
 * 新增购物车记录
 * @param {Object} params - 新增参数
 * @param {number} params.productId - 商品ID，标识要添加到购物车的商品
 * @param {number} params.productAttrValueId - 商品属性值ID，用于确定商品的具体属性
 * @param {number} params.num - 商品数量，指定添加到购物车的商品数量
 * @returns {Promise<Object>} 新增结果
 */
export const addToCart = async ({ productId, productAttrValueId, num }) => {
  return await request({
    url: `${API.cart}/store/cart/add`,
    method: "POST",
    queryData: { productId, productAttrValueId, num }
  })
}

/**
 * 删除购物车记录
 * @param {Object} params - 删除参数
 * @param {number|number[]} params.cartIds - 购物车ID数组，用于指定要删除的购物车记录
 * @returns {Promise<Object>} 删除结果
 */
export const removeFromCart = async ({ cartIds }) => {
  // 确保cartIds是数组格式
  const ids = Array.isArray(cartIds) ? cartIds : [cartIds]

  return await request({
    url: `${API.cart}/store/cart/remove`,
    method: "POST",
    queryData: { cartIds: ids.join(",") }
  })
}

/**
 * 批量删除购物车记录
 * @param {Object} params - 删除参数
 * @param {number[]} params.cartIds - 购物车ID数组
 * @returns {Promise<Object>} 删除结果
 */
export const batchRemoveFromCart = async ({ cartIds }) => {
  return await removeFromCart({ cartIds })
}

/**
 * 更新购物车商品数量
 * @param {Object} params - 更新参数
 * @param {number} params.productId - 商品ID
 * @param {number} params.productAttrValueId - 商品属性值ID
 * @param {number} params.num - 新的商品数量
 * @returns {Promise<Object>} 更新结果
 */
export const updateCartItemQuantity = async ({ productId, productAttrValueId, num }) => {
  // 先删除原有记录，再添加新记录（根据API设计）
  return await addToCart({ productId, productAttrValueId, num })
}

/**
 * 清空购物车
 * @param {Object} params - 清空参数
 * @param {number} params.userId - 用户ID
 * @returns {Promise<Object>} 清空结果
 */
export const clearCart = async ({ userId }) => {
  // 先获取购物车列表，然后批量删除
  const cartListResult = await getCartList({ userId })

  if (cartListResult && cartListResult.data && cartListResult.data.length > 0) {
    const cartIds = cartListResult.data.map((item) => item.id)
    return await removeFromCart({ cartIds })
  }

  return { code: 200, msg: "购物车已为空", data: null }
}

/**
 * 获取购物车商品数量统计
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @returns {Promise<Object>} 购物车统计信息
 */
export const getCartCount = async ({ userId }) => {
  const result = await getCartList({ userId })

  if (result && result.data) {
    const totalCount = result.data.reduce((sum, item) => sum + (item.num || 0), 0)
    return {
      code: 200,
      msg: "获取成功",
      data: {
        totalItems: result.data.length,
        totalCount: totalCount
      }
    }
  }

  return {
    code: 200,
    msg: "获取成功",
    data: {
      totalItems: 0,
      totalCount: 0
    }
  }
}

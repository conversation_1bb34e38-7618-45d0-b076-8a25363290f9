import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  coupon: BASE_URL
}

/**
 * 优购码查询
 * @param {Object} params - 查询参数
 * @param {string} [params.usedStatus] - 使用状态
 * @returns {Promise<Object>} 优购码列表数据
 */
export const getCouponList = async (params = {}) => {
  return await request({
    url: `${API.coupon}/store/code/appList`,
    method: "GET",
    queryData: params
  })
}

/**
 * 查询优购码App列表
 * @param {Object} params - 查询参数
 * @param {string} [params.usedStatus] - 使用状态（0-未使用，1-已使用，2-已过期）
 * @returns {Promise<Object>} 优购码App列表
 */
export const getCouponAppList = async (params = {}) => {
  return await getCouponList(params)
}

/**
 * 获取未使用的优购码
 * @returns {Promise<Object>} 未使用优购码列表
 */
export const getUnusedCoupons = async () => {
  return await getCouponList({ usedStatus: "0" })
}

/**
 * 获取已使用的优购码
 * @returns {Promise<Object>} 已使用优购码列表
 */
export const getUsedCoupons = async () => {
  return await getCouponList({ usedStatus: "1" })
}

/**
 * 获取已过期的优购码
 * @returns {Promise<Object>} 已过期优购码列表
 */
export const getExpiredCoupons = async () => {
  return await getCouponList({ usedStatus: "2" })
}

/**
 * 获取所有优购码
 * @returns {Promise<Object>} 所有优购码列表
 */
export const getAllCoupons = async () => {
  return await getCouponList()
}

/**
 * 根据状态获取优购码统计
 * @returns {Promise<Object>} 优购码统计信息
 */
export const getCouponStatistics = async () => {
  try {
    const [unusedResult, usedResult, expiredResult] = await Promise.all([
      getUnusedCoupons(),
      getUsedCoupons(),
      getExpiredCoupons()
    ])

    const statistics = {
      total: 0,
      unused: 0,
      used: 0,
      expired: 0
    }

    if (unusedResult && unusedResult.data) {
      statistics.unused = Array.isArray(unusedResult.data) ? unusedResult.data.length : 0
    }

    if (usedResult && usedResult.data) {
      statistics.used = Array.isArray(usedResult.data) ? usedResult.data.length : 0
    }

    if (expiredResult && expiredResult.data) {
      statistics.expired = Array.isArray(expiredResult.data) ? expiredResult.data.length : 0
    }

    statistics.total = statistics.unused + statistics.used + statistics.expired

    return {
      code: 200,
      msg: "获取成功",
      data: statistics
    }
  } catch (error) {
    return {
      code: 500,
      msg: "获取统计信息失败",
      data: {
        total: 0,
        unused: 0,
        used: 0,
        expired: 0
      }
    }
  }
}

/**
 * 检查优购码是否可用
 * @param {Object} params - 检查参数
 * @param {string} params.couponCode - 优购码代码
 * @param {number} [params.orderAmount] - 订单金额
 * @returns {Promise<Object>} 检查结果
 */
export const checkCouponAvailability = async ({ couponCode, orderAmount }) => {
  try {
    // 获取所有未使用的优购码
    const result = await getUnusedCoupons()

    if (result && result.data && Array.isArray(result.data)) {
      const coupon = result.data.find((item) => item.code === couponCode)

      if (!coupon) {
        return {
          code: 400,
          msg: "优购码不存在或已使用",
          data: {
            available: false,
            reason: "优购码不存在或已使用"
          }
        }
      }

      // 检查是否过期
      if (coupon.expireTime && new Date(coupon.expireTime) < new Date()) {
        return {
          code: 400,
          msg: "优购码已过期",
          data: {
            available: false,
            reason: "优购码已过期"
          }
        }
      }

      // 检查最低消费金额
      if (orderAmount && coupon.minAmount && orderAmount < coupon.minAmount) {
        return {
          code: 400,
          msg: `订单金额不满足优购码使用条件，最低需要${coupon.minAmount}元`,
          data: {
            available: false,
            reason: `订单金额不满足使用条件，最低需要${coupon.minAmount}元`
          }
        }
      }

      return {
        code: 200,
        msg: "优购码可用",
        data: {
          available: true,
          coupon: coupon
        }
      }
    }

    return {
      code: 400,
      msg: "优购码不存在",
      data: {
        available: false,
        reason: "优购码不存在"
      }
    }
  } catch (error) {
    return {
      code: 500,
      msg: "检查优购码失败",
      data: {
        available: false,
        reason: "检查优购码失败"
      }
    }
  }
}

/**
 * 获取可用于指定订单金额的优购码
 * @param {Object} params - 查询参数
 * @param {number} params.orderAmount - 订单金额
 * @returns {Promise<Object>} 可用优购码列表
 */
export const getAvailableCouponsForOrder = async ({ orderAmount }) => {
  try {
    const result = await getUnusedCoupons()

    if (result && result.data && Array.isArray(result.data)) {
      const availableCoupons = result.data.filter((coupon) => {
        // 检查是否过期
        if (coupon.expireTime && new Date(coupon.expireTime) < new Date()) {
          return false
        }

        // 检查最低消费金额
        if (coupon.minAmount && orderAmount < coupon.minAmount) {
          return false
        }

        return true
      })

      return {
        code: 200,
        msg: "获取成功",
        data: availableCoupons
      }
    }

    return {
      code: 200,
      msg: "暂无可用优购码",
      data: []
    }
  } catch (error) {
    return {
      code: 500,
      msg: "获取可用优购码失败",
      data: []
    }
  }
}

// 用户登录相关API
export { login, getPhoneNumber, getUserInfo, logout, phoneAuthLogin } from "./login.js"

// 商品相关API
export {
  getProductList,
  getProductInfo,
  getProductCategories,
  getNewProducts,
  getHotProducts,
  searchProducts,
  getProductsByCategory,
  getRecommendedProducts,
  getHotSearchList,
	getProductCateList
} from "./product.js"

// 购物车相关API
export {
  getCartList,
  addToCart,
  removeFromCart,
  batchRemoveFromCart,
  updateCartItemQuantity,
  clearCart,
  getCartCount
} from "./cart.js"

// 订单相关API
export {
  getOrderList,
  placeOrder,
  cancelOrder,
  getOrderInfo,
  updateOrderAddress,
  removeOrder,
  getAfterSalesOrderList,
  batchCancelOrder,
  batchRemoveOrder,
  getOrdersByStatus,
  getPendingPaymentOrders,
  getPendingShipmentOrders,
  getPendingReceiptOrders,
  getCompletedOrders
} from "./order.js"

// 售后服务相关API
export {
  getAfterSalesList,
  applyAfterSales,
  cancelAfterSales,
  getAfterSalesInfo,
  removeAfterSales,
  writeDeliveryInfo,
  getAfterSalesByStatus,
  getPendingAfterSales,
  getProcessingAfterSales,
  getCompletedAfterSales,
  getCancelledAfterSales,
  batchCancelAfterSales,
  batchRemoveAfterSales
} from "./afterSales.js"

// 发票相关API
export {
  applyInvoice,
  getInvoiceInfo,
  applyBlueInvoice,
  getBlueInvoiceInfo,
  checkInvoiceEligibility,
  getInvoiceHistory,
  validateInvoiceData
} from "./invoice.js"

// 地址相关API
export {
  getAddressList,
  getAddressInfo,
  addAddress,
  editAddress,
  deleteAddress,
  getCityList,
  getCityTreeList,
  setDefaultAddress,
  getDefaultAddress,
  batchDeleteAddress,
  validateAddressData
} from "./address.js"

// 优购码相关API
export {
  getCouponList,
  getCouponAppList,
  getUnusedCoupons,
  getUsedCoupons,
  getExpiredCoupons,
  getAllCoupons,
  getCouponStatistics,
  checkCouponAvailability,
  getAvailableCouponsForOrder
} from "./coupon.js"

// 系统相关API
export {
  getSearchHistory,
  saveSearchHistory,
  clearSearchHistory,
  getGuessYouLikeProducts,
  getSystemConfig,
  getBannerList,
  getNoticeList,
  getVersionInfo,
  submitFeedback,
  getHelpDocList,
  getHelpDocDetail,
  getFaqList,
  getContactInfo,
  getAboutInfo,
  getPrivacyPolicy,
  getUserAgreement,
  uploadFile,
  getRegionData
} from "./system.js"

// API分类导出，便于按模块使用
export const LoginAPI = {
  login,
  getPhoneNumber,
  getUserInfo,
  logout,
  phoneAuthLogin
}

export const ProductAPI = {
	getProductCateList,
  getProductList,
  getProductInfo,
  getProductCategories,
  getNewProducts,
  getHotProducts,
  searchProducts,
  getProductsByCategory,
  getRecommendedProducts
}

export const CartAPI = {
  getCartList,
  addToCart,
  removeFromCart,
  batchRemoveFromCart,
  updateCartItemQuantity,
  clearCart,
  getCartCount
}

export const OrderAPI = {
  getOrderList,
  placeOrder,
  cancelOrder,
  getOrderInfo,
  updateOrderAddress,
  removeOrder,
  getAfterSalesOrderList,
  batchCancelOrder,
  batchRemoveOrder,
  getOrdersByStatus,
  getPendingPaymentOrders,
  getPendingShipmentOrders,
  getPendingReceiptOrders,
  getCompletedOrders
}

export const AfterSalesAPI = {
  getAfterSalesList,
  applyAfterSales,
  cancelAfterSales,
  getAfterSalesInfo,
  removeAfterSales,
  writeDeliveryInfo,
  getAfterSalesByStatus,
  getPendingAfterSales,
  getProcessingAfterSales,
  getCompletedAfterSales,
  getCancelledAfterSales,
  batchCancelAfterSales,
  batchRemoveAfterSales
}

export const InvoiceAPI = {
  applyInvoice,
  getInvoiceInfo,
  applyBlueInvoice,
  getBlueInvoiceInfo,
  checkInvoiceEligibility,
  getInvoiceHistory,
  validateInvoiceData
}

export const AddressAPI = {
  getAddressList,
  getAddressInfo,
  addAddress,
  editAddress,
  deleteAddress,
  getCityList,
  getCityTreeList,
  setDefaultAddress,
  getDefaultAddress,
  batchDeleteAddress,
  validateAddressData
}

export const CouponAPI = {
  getCouponList,
  getCouponAppList,
  getUnusedCoupons,
  getUsedCoupons,
  getExpiredCoupons,
  getAllCoupons,
  getCouponStatistics,
  checkCouponAvailability,
  getAvailableCouponsForOrder
}

export const SystemAPI = {
  getSearchHistory,
  saveSearchHistory,
  clearSearchHistory,
  getGuessYouLikeProducts,
  getSystemConfig,
  getBannerList,
  getNoticeList,
  getVersionInfo,
  submitFeedback,
  getHelpDocList,
  getHelpDocDetail,
  getFaqList,
  getContactInfo,
  getAboutInfo,
  getPrivacyPolicy,
  getUserAgreement,
  uploadFile,
  getRegionData
}

// 导入声明（用于IDE智能提示）
import { login, getPhoneNumber, getUserInfo, logout, phoneAuthLogin } from "./login.js"

import {
  getProductList,
  getProductInfo,
  getProductCategories,
  getNewProducts,
  getHotProducts,
  searchProducts,
  getProductsByCategory,
  getRecommendedProducts,
  getHotSearchList,
	getProductCateList
} from "./product.js"

import {
  getCartList,
  addToCart,
  removeFromCart,
  batchRemoveFromCart,
  updateCartItemQuantity,
  clearCart,
  getCartCount
} from "./cart.js"

import {
  getOrderList,
  placeOrder,
  cancelOrder,
  getOrderInfo,
  updateOrderAddress,
  removeOrder,
  getAfterSalesOrderList,
  batchCancelOrder,
  batchRemoveOrder,
  getOrdersByStatus,
  getPendingPaymentOrders,
  getPendingShipmentOrders,
  getPendingReceiptOrders,
  getCompletedOrders
} from "./order.js"

import {
  getAfterSalesList,
  applyAfterSales,
  cancelAfterSales,
  getAfterSalesInfo,
  removeAfterSales,
  writeDeliveryInfo,
  getAfterSalesByStatus,
  getPendingAfterSales,
  getProcessingAfterSales,
  getCompletedAfterSales,
  getCancelledAfterSales,
  batchCancelAfterSales,
  batchRemoveAfterSales
} from "./afterSales.js"

import {
  applyInvoice,
  getInvoiceInfo,
  applyBlueInvoice,
  getBlueInvoiceInfo,
  checkInvoiceEligibility,
  getInvoiceHistory,
  validateInvoiceData
} from "./invoice.js"

import {
  getAddressList,
  getAddressInfo,
  addAddress,
  editAddress,
  deleteAddress,
  getCityList,
  getCityTreeList,
  setDefaultAddress,
  getDefaultAddress,
  batchDeleteAddress,
  validateAddressData
} from "./address.js"

import {
  getCouponList,
  getCouponAppList,
  getUnusedCoupons,
  getUsedCoupons,
  getExpiredCoupons,
  getAllCoupons,
  getCouponStatistics,
  checkCouponAvailability,
  getAvailableCouponsForOrder
} from "./coupon.js"

import {
  getSearchHistory,
  saveSearchHistory,
  clearSearchHistory,
  getGuessYouLikeProducts,
  getSystemConfig,
  getBannerList,
  getNoticeList,
  getVersionInfo,
  submitFeedback,
  getHelpDocList,
  getHelpDocDetail,
  getFaqList,
  getContactInfo,
  getAboutInfo,
  getPrivacyPolicy,
  getUserAgreement,
  uploadFile,
  getRegionData
} from "./system.js"

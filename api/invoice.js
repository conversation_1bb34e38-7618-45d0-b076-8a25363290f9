import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  invoice: BASE_URL
}

/**
 * 申请开票
 * @param {Object} params - 发票申请参数
 * @param {number} [params.orderId] - 订单ID
 * @param {Object} [params.invoiceInfo] - 发票信息
 * @param {Object} [params.invoiceData] - 发票数据（兼容字段）
 * @returns {Promise<Object>} 申请结果
 */
export const applyInvoice = async (params = {}) => {
  let requestData = params

  // 兼容处理：如果传入了invoiceInfo或invoiceData，合并到请求数据中
  if (params.invoiceInfo) {
    requestData = { ...params.invoiceInfo, orderId: params.orderId }
  } else if (params.invoiceData) {
    requestData = { ...params.invoiceData, orderId: params.orderId }
  }

  return await request({
    url: `${API.invoice}/store/apply/applyOpenBlueInvoice`,
    method: "POST",
    data: requestData
  })
}

/**
 * 发票信息查询
 * @param {Object} params - 查询参数
 * @param {number} params.orderId - 订单ID
 * @returns {Promise<Object>} 发票信息
 */
export const getInvoiceInfo = async ({ orderId }) => {
  return await request({
    url: `${API.invoice}/store/apply/getBlueInvoiceInfoByOrderId`,
    method: "GET",
    queryData: { orderId }
  })
}

/**
 * 新增蓝字发票申请
 * @param {Object} invoiceData - 发票申请数据
 * @param {number} invoiceData.orderId - 订单ID
 * @param {string} invoiceData.invoiceType - 发票类型（个人/企业）
 * @param {string} invoiceData.invoiceTitle - 发票抬头
 * @param {string} [invoiceData.taxNumber] - 纳税人识别号（企业发票必填）
 * @param {string} [invoiceData.registerAddress] - 注册地址
 * @param {string} [invoiceData.registerPhone] - 注册电话
 * @param {string} [invoiceData.bankName] - 开户银行
 * @param {string} [invoiceData.bankAccount] - 银行账号
 * @param {string} invoiceData.receiverName - 收票人姓名
 * @param {string} invoiceData.receiverPhone - 收票人电话
 * @param {string} invoiceData.receiverEmail - 收票人邮箱
 * @returns {Promise<Object>} 申请结果
 */
export const applyBlueInvoice = async (invoiceData) => {
  return await applyInvoice(invoiceData)
}

/**
 * 根据订单ID获取蓝字发票信息
 * @param {Object} params - 查询参数
 * @param {number} params.orderId - 订单ID
 * @returns {Promise<Object>} 蓝字发票信息
 */
export const getBlueInvoiceInfo = async ({ orderId }) => {
  return await getInvoiceInfo({ orderId })
}

/**
 * 检查订单是否可以开票
 * @param {Object} params - 查询参数
 * @param {number} params.orderId - 订单ID
 * @returns {Promise<Object>} 检查结果
 */
export const checkInvoiceEligibility = async ({ orderId }) => {
  try {
    const result = await getInvoiceInfo({ orderId })

    // 如果已经有发票信息，说明已经申请过
    if (result && result.data) {
      return {
        code: 200,
        msg: "该订单已申请过发票",
        data: {
          canApply: false,
          reason: "已申请过发票",
          invoiceInfo: result.data
        }
      }
    }

    // 没有发票信息，可以申请
    return {
      code: 200,
      msg: "可以申请发票",
      data: {
        canApply: true,
        reason: "可以申请发票"
      }
    }
  } catch (error) {
    // 如果查询失败，可能是没有发票记录，也可以申请
    return {
      code: 200,
      msg: "可以申请发票",
      data: {
        canApply: true,
        reason: "可以申请发票"
      }
    }
  }
}

/**
 * 获取发票申请历史
 * @param {Object} params - 查询参数
 * @param {number[]} params.orderIds - 订单ID数组
 * @returns {Promise<Object[]>} 发票申请历史列表
 */
export const getInvoiceHistory = async ({ orderIds }) => {
  const promises = orderIds.map((orderId) =>
    getInvoiceInfo({ orderId }).catch((error) => ({
      orderId,
      error: error.message || "查询失败"
    }))
  )

  const results = await Promise.all(promises)

  return {
    code: 200,
    msg: "查询成功",
    data: results.filter((result) => !result.error)
  }
}

/**
 * 验证发票申请数据
 * @param {Object} invoiceData - 发票申请数据
 * @returns {Object} 验证结果
 */
export const validateInvoiceData = (invoiceData) => {
  const errors = []

  // 必填字段验证
  if (!invoiceData.orderId) {
    errors.push("订单ID不能为空")
  }

  if (!invoiceData.invoiceType) {
    errors.push("发票类型不能为空")
  }

  if (!invoiceData.invoiceTitle) {
    errors.push("发票抬头不能为空")
  }

  if (!invoiceData.receiverName) {
    errors.push("收票人姓名不能为空")
  }

  if (!invoiceData.receiverPhone) {
    errors.push("收票人电话不能为空")
  }

  if (!invoiceData.receiverEmail) {
    errors.push("收票人邮箱不能为空")
  }

  // 企业发票特殊验证
  if (invoiceData.invoiceType === "企业" && !invoiceData.taxNumber) {
    errors.push("企业发票必须填写纳税人识别号")
  }

  // 邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (invoiceData.receiverEmail && !emailRegex.test(invoiceData.receiverEmail)) {
    errors.push("收票人邮箱格式不正确")
  }

  // 手机号格式验证
  const phoneRegex = /^1[3-9]\d{9}$/
  if (invoiceData.receiverPhone && !phoneRegex.test(invoiceData.receiverPhone)) {
    errors.push("收票人电话格式不正确")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  wechat: BASE_URL
}

export const getInfo = async () =>
  await request({
    url: `${API.wechat}/getInfo`,
    method: "GET"
  })

/**
 * 微信登录接口
 * @param {Object} params - 登录参数
 * @param {string} params.code - 微信登录凭证
 * @returns {Promise<Object>} 登录结果，包含openid等信息
 */
export const login = async ({ code }) =>
  await request({
    url: `${API.wechat}/weixin/login`,
    method: "POST",
    data: { code }
  })

/**
 * 获取微信手机号接口
 * @param {Object} data - 请求参数
 * @param {string} data.openId - 用户openId
 * @param {string} data.code - 手机号授权code
 * @returns {Promise<Object>} 包含token等信息的响应
 */
export const getPhoneNumber = async (data) => {
  return await request({
    url: `${API.wechat}/weixin/phone`,
    method: "POST",
    data
  })
}

/**
 * 获取用户信息接口
 * @returns {Promise<Object>} 用户信息
 */
export const getUserInfo = async () => {
  return await request({
    url: `${API.wechat}/weixin/info`,
    method: "POST"
  })
}

/**
 * 退出登录接口
 * @returns {Promise<Object>} 退出登录结果
 */
export const logout = async () => {
  return await request({
    url: `${API.wechat}/logout`,
    method: "POST"
  })
}

/**
 * 手机号授权登录接口（完整流程）
 * @param {Object} params - 登录参数
 * @param {string} params.code - 微信登录凭证
 * @param {string} params.phoneCode - 手机号授权code
 * @returns {Promise<Object>} 完整登录结果，包含token等信息
 */
export const phoneAuthLogin = async ({ code, phoneCode }) => {
  try {
    // 第一步：微信登录获取openId
    const loginResult = await login({ code })

    if (loginResult && loginResult.data && loginResult.data.openId) {
      // 第二步：获取手机号并完成登录
      const phoneResult = await getPhoneNumber({
        openId: loginResult.data.openId,
        code: phoneCode
      })

      return phoneResult
    }

    throw new Error("微信登录失败")
  } catch (error) {
    throw error
  }
}

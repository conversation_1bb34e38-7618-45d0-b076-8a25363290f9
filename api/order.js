import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  order: BASE_URL
}

/**
 * 查询订单列表（APP端）
 * @param {Object} params - 查询参数
 * @param {number} [params.userId] - 用户ID
 * @param {string} [params.orderStatus] - 订单状态
 * @param {string} [params.status] - 订单状态（兼容字段）
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {number} [params.page] - 页码（兼容字段）
 * @param {number} [params.size] - 每页数量（兼容字段）
 * @returns {Promise<Object>} 订单列表数据
 */
export const getOrderList = async (params = {}) => {
  // 兼容处理
  const queryData = { ...params }
  if (params.status && !params.orderStatus) {
    queryData.orderStatus = params.status
  }
  if (params.page && !params.pageNum) {
    queryData.pageNum = params.page
  }
  if (params.size && !params.pageSize) {
    queryData.pageSize = params.size
  }

  return await request({
    url: `${API.order}/store/order/appList`,
    method: "GET",
    queryData
  })
}

/**
 * 下订单
 * @param {Object} orderData - 订单数据
 * @returns {Promise<Object>} 下单结果
 */
export const placeOrder = async (orderData) => {
  return await request({
    url: `${API.order}/store/order/placeOrder`,
    method: "POST",
    data: orderData
  })
}

export const afterSale_cancle = async (data) => {
  return await request({
    url: `${API.order}/store/afterSale/cancle`,
    method: "POST",
    data
  })
}
export const afterSale_writeDeliveryNo = async (data) => {
  return await request({
    url: `${API.order}/store/afterSale/writeDeliveryNo`,
    method: "POST",
    data
  })
}


export const afterSaleApply = async (data) => {
  return await request({
    url: `${API.order}/store/afterSale/apply`,
    method: "POST",
    data
  })
}

export const simulationPaymentSuccessful = async (orderData) => {
  return await request({
    url: `${API.order}/store/order/simulationPaymentSuccessful`,
    method: "POST",
    data: orderData
  })
}

export const placeWeixinOrder = async (orderData) => {
  return await request({
    url: `${API.order}/store/order/placeWeixinOrder`,
    method: "POST",
    data: orderData
  })
}

/**
 * 取消订单
 * @param {Object} params - 取消参数
 * @param {number|number[]} [params.orderIds] - 订单ID数组
 * @param {number} [params.orderId] - 单个订单ID（兼容字段）
 * @param {string} [params.reason] - 取消原因
 * @returns {Promise<Object>} 取消结果
 */
export const cancelOrder = async (params = {}) => {
  let orderIds = params.orderIds

  // 兼容处理：如果传入单个orderId，转换为数组
  if (params.orderId && !orderIds) {
    orderIds = [params.orderId]
  }

  // 确保orderIds是数组格式
  const ids = Array.isArray(orderIds) ? orderIds : [orderIds]

  const queryData = { orderIds: ids.join(",") }
  if (params.reason) {
    queryData.cancelReason = params.reason
  }

  return await request({
    url: `${API.order}/store/order/cancle`,
    method: "POST",
    queryData
  })
}

/**
 * 获取订单详细信息
 * @param {Object} params - 查询参数
 * @param {number} params.orderId - 订单ID
 * @returns {Promise<Object>} 订单详细信息
 */
export const getOrderInfo = async ({ orderId }) => {
  return await request({
    url: `${API.order}/store/order/getInfo`,
    method: "GET",
    queryData: { orderId }
  })
}

/**
 * 修改收货地址
 * @param {Object} params - 修改参数
 * @param {number} params.orderId - 订单ID
 * @param {number} params.userAddressId - 用户地址ID
 * @returns {Promise<Object>} 修改结果
 */
export const updateOrderAddress = async ({ orderId, userAddressId }) => {
  return await request({
    url: `${API.order}/store/order/updateUserAddress`,
    method: "POST",
    queryData: { orderId, userAddressId }
  })
}

/**
 * 删除订单
 * @param {Object} params - 删除参数
 * @param {number|number[]} params.orderIds - 订单ID数组
 * @returns {Promise<Object>} 删除结果
 */
export const removeOrder = async ({ orderIds }) => {
  // 确保orderIds是数组格式
  const ids = Array.isArray(orderIds) ? orderIds : [orderIds]

  return await request({
    url: `${API.order}/store/order/remove`,
    method: "POST",
    queryData: { orderIds: ids.join(",") }
  })
}

export const orderConfirmOrderReceivedt = async (data) => {
  return await request({
    url: `${API.order}/store/order/confirmOrderReceived`,
    method: "POST",
    data
  })
}

export const applyOpenBlueInvoice = async (data) => {
  return await request({
    url: `${API.order}/store/apply/applyOpenBlueInvoice`,
    method: "POST",
    data
  })
}

/**
 * 售后服务订单列表
 * @param {Object} params - 查询参数
 * @param {string} params.searchValue - 搜索值
 * @returns {Promise<Object>} 可售后订单列表
 */
export const getAfterSalesOrderList = async ({ searchValue }) => {
  return await request({
    url: `${API.order}/store/order/asServiceList`,
    method: "GET",
    queryData: { searchValue }
  })
}

export const getAfterSalesAppList = async (queryData) => {
  return await request({
    url: `${API.order}/store/afterSale/appList`,
    method: "GET",
    queryData
  })
}

/**
 * 批量取消订单
 * @param {Object} params - 取消参数
 * @param {number[]} params.orderIds - 订单ID数组
 * @returns {Promise<Object>} 取消结果
 */
export const batchCancelOrder = async ({ orderIds }) => {
  return await cancelOrder({ orderIds })
}

/**
 * 批量删除订单
 * @param {Object} params - 删除参数
 * @param {number[]} params.orderIds - 订单ID数组
 * @returns {Promise<Object>} 删除结果
 */
export const batchRemoveOrder = async ({ orderIds }) => {
  return await removeOrder({ orderIds })
}

/**
 * 根据状态获取订单列表
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {string} params.status - 订单状态（待付款、待发货、待收货、已完成等）
 * @param {number} [params.pageNum=1] - 页码
 * @param {number} [params.pageSize=10] - 每页数量
 * @returns {Promise<Object>} 订单列表
 */
export const getOrdersByStatus = async ({ userId, status, pageNum = 1, pageSize = 10 }) => {
  return await getOrderList({ userId, orderStatus: status, pageNum, pageSize })
}

/**
 * 获取待付款订单
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} [params.pageNum=1] - 页码
 * @param {number} [params.pageSize=10] - 每页数量
 * @returns {Promise<Object>} 待付款订单列表
 */
export const getPendingPaymentOrders = async ({ userId, pageNum = 1, pageSize = 10 }) => {
  return await getOrdersByStatus({ userId, status: "0", pageNum, pageSize })
}

/**
 * 获取待发货订单
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} [params.pageNum=1] - 页码
 * @param {number} [params.pageSize=10] - 每页数量
 * @returns {Promise<Object>} 待发货订单列表
 */
export const getPendingShipmentOrders = async ({ userId, pageNum = 1, pageSize = 10 }) => {
  return await getOrdersByStatus({ userId, status: "1", pageNum, pageSize })
}

/**
 * 获取待收货订单
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} [params.pageNum=1] - 页码
 * @param {number} [params.pageSize=10] - 每页数量
 * @returns {Promise<Object>} 待收货订单列表
 */
export const getPendingReceiptOrders = async ({ userId, pageNum = 1, pageSize = 10 }) => {
  return await getOrdersByStatus({ userId, status: "2", pageNum, pageSize })
}

/**
 * 获取已完成订单
 * @param {Object} params - 查询参数
 * @param {number} params.userId - 用户ID
 * @param {number} [params.pageNum=1] - 页码
 * @param {number} [params.pageSize=10] - 每页数量
 * @returns {Promise<Object>} 已完成订单列表
 */
export const getCompletedOrders = async ({ userId, pageNum = 1, pageSize = 10 }) => {
  return await getOrdersByStatus({ userId, status: "3", pageNum, pageSize })
}

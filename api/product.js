import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  product: BASE_URL
}

/**
 * 查询商品分类列表
 */
export const getProductCateList = async () => {
  return await request({
    url: `${API.product}/store/productCate/list`,
    method: "GET"
  })
}

export const getProductBrand = async () => {
  return await request({
    url: `${API.product}/dropDown/productBrand`,
    method: "GET"
  })
}

/**
 * 查询商品APP列表
 * @param {Object} params - 查询参数
 * @param {string} [params.ascOrDesc] - 排序方式，asc为升序，desc为降序
 * @param {number} [params.brandId] - 品牌ID，用于筛选指定品牌的商品
 * @param {string} [params.cateId] - 分类ID，用于筛选指定分类的商品
 * @param {string} [params.isHot] - 是否热卖商品，1为热卖，0为非热卖
 * @param {string} [params.isNew] - 是否新品，1为新品，0为非新品
 * @param {number} [params.maxPrice] - 最高价格，用于价格区间筛选
 * @param {number} [params.minPrice] - 最低价格，用于价格区间筛选
 * @param {string} [params.searchValue] - 搜索关键词，用于商品名称或描述的模糊搜索
 * @param {string} [params.sortField] - 排序字段，如price（价格）、createTime（创建时间）等
 * @returns {Promise<Object>} 商品列表数据
 */
export const getProductList = async (params = {}) => {
  return await request({
    url: `${API.product}/store/product/appList`,
    method: "GET",
    queryData: params
  })
}

/**
 * 获取商品详细信息
 * @param {Object} params - 查询参数
 * @param {number} params.productId - 商品ID
 * @returns {Promise<Object>} 商品详细信息
 */
export const getProductInfo = async ({ productId }) => {
  return await request({
    url: `${API.product}/store/product/getInfo`,
    method: "GET",
    queryData: { productId }
  })
}

/**
 * 查询商品分类列表
 * @returns {Promise<Object>} 商品分类列表
 */
export const getProductCategories = async () => {
  return await request({
    url: `${API.product}/store/productCate/treeList`,
    method: "GET",
		queryData: {
			pid: 0
		}
  })
}

/**
 * 查询上新商品列表
 * @param {Object} params - 查询参数
 * @param {string} [params.ascOrDesc] - 排序方式，asc为升序，desc为降序
 * @param {number} [params.brandId] - 品牌ID，用于筛选指定品牌的商品
 * @param {string} [params.cateId] - 分类ID，用于筛选指定分类的商品
 * @param {number} [params.maxPrice] - 最高价格，用于价格区间筛选
 * @param {number} [params.minPrice] - 最低价格，用于价格区间筛选
 * @param {string} [params.searchValue] - 搜索关键词，用于商品名称或描述的模糊搜索
 * @param {string} [params.sortField] - 排序字段，如price（价格）、createTime（创建时间）等
 * @returns {Promise<Object>} 上新商品列表
 */
export const getNewProducts = async (params = {}) => {
  return await request({
    url: `${API.product}/store/product/newList`,
    method: "GET",
    queryData: { ...params }
  })
}

/**
 * 查询热卖商品列表
 * @param {Object} params - 查询参数
 * @param {string} [params.ascOrDesc] - 排序方式，asc为升序，desc为降序
 * @param {number} [params.brandId] - 品牌ID，用于筛选指定品牌
 * @param {string} [params.cateId] - 分类ID，用于筛选指定分类
 * @param {number} [params.maxPrice] - 最高价格，用于价格区间筛选
 * @param {number} [params.minPrice] - 最低价格，用于价格区间筛选
 * @param {string} [params.searchValue] - 搜索关键词，用于模糊查询
 * @param {string} [params.sortField] - 排序字段名称
 * @returns {Promise<Object>} 热卖商品列表
 */
export const getHotProducts = async (params = {}) => {
  return await request({
    url: `${API.product}/store/product/goodList`,
    method: "GET",
    queryData: { ...params }
  })
}

/**
 * 商品信息查询（全文检索）
 * @param {Object} params - 查询参数
 * @param {string} [params.keyword] - 搜索关键词
 * @param {string} [params.searchValue] - 搜索关键词（兼容字段）
 * @param {string} [params.ascOrDesc] - 排序方式，asc为升序，desc为降序
 * @param {number} [params.brandId] - 品牌ID，用于筛选指定品牌
 * @param {string} [params.cateId] - 分类ID，用于筛选指定分类
 * @param {string} [params.isHot] - 是否热卖，1为热卖，0为非热卖
 * @param {string} [params.isNew] - 是否新品，1为新品，0为非新品
 * @param {number} [params.maxPrice] - 最高价格，用于价格区间筛选
 * @param {number} [params.minPrice] - 最低价格，用于价格区间筛选
 * @param {string} [params.sortField] - 排序字段名称
 * @param {number} [params.page] - 页码
 * @param {number} [params.size] - 每页数量
 * @returns {Promise<Object>} 搜索结果商品列表
 */
export const searchProducts = async (params = {}) => {
  // 兼容处理：如果传入keyword，转换为searchValue
  if (params.keyword && !params.searchValue) {
    params.searchValue = params.keyword
  }

  return await request({
    url: `${API.product}/store/product/appList`,
    method: "GET",
    queryData: params
  })
}

/**
 * 商品信息查询（按分类）
 * @param {Object} params - 查询参数
 * @param {string} [params.cateId] - 分类ID，用于筛选指定分类
 * @param {string} [params.categoryId] - 分类ID（兼容字段）
 * @param {string} [params.ascOrDesc] - 排序方式，asc为升序，desc为降序
 * @param {number} [params.brandId] - 品牌ID，用于筛选指定品牌
 * @param {string} [params.isHot] - 是否热卖，1为热卖，0为非热卖
 * @param {string} [params.isNew] - 是否新品，1为新品，0为非新品
 * @param {number} [params.maxPrice] - 最高价格，用于价格区间筛选
 * @param {number} [params.minPrice] - 最低价格，用于价格区间筛选
 * @param {string} [params.searchValue] - 搜索关键词，用于模糊查询
 * @param {string} [params.sortField] - 排序字段名称
 * @param {number} [params.page] - 页码
 * @param {number} [params.size] - 每页数量
 * @returns {Promise<Object>} 分类商品列表
 */
export const getProductsByCategory = async (params = {}) => {
  // 兼容处理：如果传入categoryId，转换为cateId
  if (params.categoryId && !params.cateId) {
    params.cateId = params.categoryId
  }

  return await request({
    url: `${API.product}/store/product/appList`,
    method: "GET",
    queryData: params
  })
}

/**
 * 推荐商品查询
 * @param {Object} params - 查询参数
 * @param {string} [params.ascOrDesc] - 排序方式，asc为升序，desc为降序
 * @param {number} [params.brandId] - 品牌ID，用于筛选指定品牌
 * @param {string} [params.cateId] - 分类ID，用于筛选指定分类
 * @param {string} [params.isHot] - 是否热卖，1为热卖，0为非热卖
 * @param {string} [params.isNew] - 是否新品，1为新品，0为非新品
 * @param {number} [params.maxPrice] - 最高价格，用于价格区间筛选
 * @param {number} [params.minPrice] - 最低价格，用于价格区间筛选
 * @param {string} [params.searchValue] - 搜索关键词，用于模糊查询
 * @param {string} [params.sortField] - 排序字段名称
 * @returns {Promise<Object>} 推荐商品列表
 */
export const getRecommendedProducts = async (params = {}) => {
  return await request({
    url: `${API.product}/store/product/appList`,
    method: "GET",
    queryData: params
  })
}

/**
 * 获取热搜榜单
 * @param {Object} params - 查询参数
 * @param {number} [params.limit] - 限制返回数量
 * @returns {Promise<Object>} 热搜榜单
 */
export const getHotSearchList = async (params = {}) => {
  return await request({
    url: `${API.product}/store/product/hotSearch`,
    method: "GET",
    queryData: params
  })
}

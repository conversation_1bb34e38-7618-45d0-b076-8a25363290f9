import { request } from "@/utils/request"
import { BASE_URL } from "@/utils/env"

const API = {
  system: BASE_URL
}

/**
 * 获取搜索历史
 * @param {Object} params - 查询参数
 * @param {number} [params.limit] - 限制返回数量
 * @returns {Promise<Object>} 搜索历史列表
 */
export const getSearchHistory = async (params = {}) => {
  return await request({
    url: `${API.system}/store/history/listByUserId`,
    method: "GET",
    queryData: {
			userId: uni.getStorageSync("userId")
		}
  })
}

/**
 * 保存搜索历史
 * @param {Object} params - 保存参数
 * @param {string} params.keyword - 搜索关键词
 * @returns {Promise<Object>} 保存结果
 */
export const saveSearchHistory = async ({ keyword }) => {
  return await request({
    url: `${API.system}/system/searchHistory/save`,
    method: "POST",
    queryData: { keyword }
  })
}

/**
 * 清除搜索历史
 * @returns {Promise<Object>} 清除结果
 */
export const clearSearchHistory = async () => {
  return await request({
    url: `${API.system}/system/searchHistory/clear`,
    method: "POST"
  })
}

/**
 * 获取猜你喜欢商品
 * @param {Object} params - 查询参数
 * @param {number} [params.page] - 页码
 * @param {number} [params.size] - 每页数量
 * @returns {Promise<Object>} 猜你喜欢商品列表
 */
export const getGuessYouLikeProducts = async (params = {}) => {
  return await request({
    url: `${API.system}/system/recommend/guessYouLike`,
    method: "GET",
    queryData: params
  })
}

/**
 * 获取系统配置
 * @param {Object} params - 查询参数
 * @param {string} [params.configKey] - 配置键名
 * @returns {Promise<Object>} 系统配置
 */
export const getSystemConfig = async (params = {}) => {
  return await request({
    url: `${API.system}/system/config/get`,
    method: "GET",
    queryData: params
  })
}

/**
 * 获取轮播图列表
 * @param {Object} params - 查询参数
 * @param {string} [params.position] - 轮播图位置（home、category等）
 * @returns {Promise<Object>} 轮播图列表
 */
export const getBannerList = async (params = {}) => {
  return await request({
    url: `${API.system}/system/banner/list`,
    method: "GET",
    queryData: params
  })
}

/**
 * 获取公告列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page] - 页码
 * @param {number} [params.size] - 每页数量
 * @returns {Promise<Object>} 公告列表
 */
export const getNoticeList = async (params = {}) => {
  return await request({
    url: `${API.system}/system/notice/list`,
    method: "GET",
    queryData: params
  })
}

/**
 * 获取版本信息
 * @returns {Promise<Object>} 版本信息
 */
export const getVersionInfo = async () => {
  return await request({
    url: `${API.system}/system/version/info`,
    method: "GET"
  })
}

/**
 * 意见反馈
 * @param {Object} feedbackData - 反馈数据
 * @param {string} feedbackData.content - 反馈内容
 * @param {string} [feedbackData.contact] - 联系方式
 * @param {string} [feedbackData.type] - 反馈类型
 * @returns {Promise<Object>} 提交结果
 */
export const submitFeedback = async (feedbackData) => {
  return await request({
    url: `${API.system}/system/feedback/submit`,
    method: "POST",
    data: feedbackData
  })
}

/**
 * 获取帮助文档列表
 * @param {Object} params - 查询参数
 * @param {string} [params.category] - 文档分类
 * @returns {Promise<Object>} 帮助文档列表
 */
export const getHelpDocList = async (params = {}) => {
  return await request({
    url: `${API.system}/system/help/list`,
    method: "GET",
    queryData: params
  })
}

/**
 * 获取帮助文档详情
 * @param {Object} params - 查询参数
 * @param {number} params.docId - 文档ID
 * @returns {Promise<Object>} 帮助文档详情
 */
export const getHelpDocDetail = async ({ docId }) => {
  return await request({
    url: `${API.system}/system/help/detail`,
    method: "GET",
    queryData: { docId }
  })
}

/**
 * 获取常见问题列表
 * @param {Object} params - 查询参数
 * @param {string} [params.category] - 问题分类
 * @returns {Promise<Object>} 常见问题列表
 */
export const getFaqList = async (params = {}) => {
  return await request({
    url: `${API.system}/system/faq/list`,
    method: "GET",
    queryData: params
  })
}

/**
 * 获取联系我们信息
 * @returns {Promise<Object>} 联系信息
 */
export const getContactInfo = async () => {
  return await request({
    url: `${API.system}/system/contact/info`,
    method: "GET"
  })
}

/**
 * 获取关于我们信息
 * @returns {Promise<Object>} 关于我们信息
 */
export const getAboutInfo = async () => {
  return await request({
    url: `${API.system}/system/about/info`,
    method: "GET"
  })
}

/**
 * 获取隐私政策
 * @returns {Promise<Object>} 隐私政策内容
 */
export const getPrivacyPolicy = async () => {
  return await request({
    url: `${API.system}/system/privacy/policy`,
    method: "GET"
  })
}

/**
 * 获取用户协议
 * @returns {Promise<Object>} 用户协议内容
 */
export const getUserAgreement = async () => {
  return await request({
    url: `${API.system}/system/user/agreement`,
    method: "GET"
  })
}

/**
 * 上传文件
 * @param {Object} params - 上传参数
 * @param {File} params.file - 文件对象
 * @param {string} [params.type] - 文件类型
 * @returns {Promise<Object>} 上传结果
 */
export const uploadFile = async ({ file, type = "image" }) => {
  return await request({
    url: `${API.system}/system/upload/file`,
    method: "POST",
    data: { file, type }
  })
}

/**
 * 获取地区数据
 * @param {Object} params - 查询参数
 * @param {string} [params.parentId] - 父级地区ID
 * @returns {Promise<Object>} 地区数据
 */
export const getRegionData = async (params = {}) => {
  return await request({
    url: `${API.system}/system/region/list`,
    method: "GET",
    queryData: params
  })
}

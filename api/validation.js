/**
 * API 数据验证工具函数
 * 提供统一的数据验证逻辑，确保API调用参数的正确性
 */

/**
 * 验证必填字段
 * @param {Object} data - 要验证的数据对象
 * @param {string[]} requiredFields - 必填字段数组
 * @returns {Object} 验证结果
 */
export const validateRequiredFields = (data, requiredFields) => {
  const errors = []

  requiredFields.forEach((field) => {
    if (!data || data[field] === undefined || data[field] === null || data[field] === "") {
      errors.push(`${field} 是必填字段`)
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 是否有效
 */
export const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否有效
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证身份证号格式
 * @param {string} idCard - 身份证号
 * @returns {boolean} 是否有效
 */
export const validateIdCard = (idCard) => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * 验证价格格式
 * @param {number|string} price - 价格
 * @returns {boolean} 是否有效
 */
export const validatePrice = (price) => {
  const priceNum = parseFloat(price)
  return !isNaN(priceNum) && priceNum >= 0
}

/**
 * 验证商品数量
 * @param {number|string} quantity - 数量
 * @returns {boolean} 是否有效
 */
export const validateQuantity = (quantity) => {
  const quantityNum = parseInt(quantity)
  return !isNaN(quantityNum) && quantityNum > 0
}

/**
 * 验证用户ID
 * @param {number|string} userId - 用户ID
 * @returns {boolean} 是否有效
 */
export const validateUserId = (userId) => {
  const userIdNum = parseInt(userId)
  return !isNaN(userIdNum) && userIdNum > 0
}

/**
 * 验证商品ID
 * @param {number|string} productId - 商品ID
 * @returns {boolean} 是否有效
 */
export const validateProductId = (productId) => {
  const productIdNum = parseInt(productId)
  return !isNaN(productIdNum) && productIdNum > 0
}

/**
 * 验证订单ID
 * @param {number|string} orderId - 订单ID
 * @returns {boolean} 是否有效
 */
export const validateOrderId = (orderId) => {
  const orderIdNum = parseInt(orderId)
  return !isNaN(orderIdNum) && orderIdNum > 0
}

/**
 * 验证分页参数
 * @param {Object} params - 分页参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Object} 验证结果
 */
export const validatePagination = ({ pageNum, pageSize }) => {
  const errors = []

  const pageNumInt = parseInt(pageNum)
  const pageSizeInt = parseInt(pageSize)

  if (isNaN(pageNumInt) || pageNumInt < 1) {
    errors.push("页码必须是大于0的整数")
  }

  if (isNaN(pageSizeInt) || pageSizeInt < 1 || pageSizeInt > 100) {
    errors.push("每页数量必须是1-100之间的整数")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 验证价格区间
 * @param {Object} params - 价格区间参数
 * @param {number} params.minPrice - 最低价格
 * @param {number} params.maxPrice - 最高价格
 * @returns {Object} 验证结果
 */
export const validatePriceRange = ({ minPrice, maxPrice }) => {
  const errors = []

  if (minPrice !== undefined && !validatePrice(minPrice)) {
    errors.push("最低价格格式不正确")
  }

  if (maxPrice !== undefined && !validatePrice(maxPrice)) {
    errors.push("最高价格格式不正确")
  }

  if (minPrice !== undefined && maxPrice !== undefined) {
    const min = parseFloat(minPrice)
    const max = parseFloat(maxPrice)

    if (min > max) {
      errors.push("最低价格不能大于最高价格")
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 验证购物车添加参数
 * @param {Object} params - 购物车参数
 * @param {number} params.productId - 商品ID
 * @param {number} params.productAttrValueId - 商品属性值ID
 * @param {number} params.num - 数量
 * @returns {Object} 验证结果
 */
export const validateCartParams = ({ productId, productAttrValueId, num }) => {
  const errors = []

  if (!validateProductId(productId)) {
    errors.push("商品ID无效")
  }

  if (!validateProductId(productAttrValueId)) {
    errors.push("商品属性值ID无效")
  }

  if (!validateQuantity(num)) {
    errors.push("商品数量必须是大于0的整数")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 验证订单查询参数
 * @param {Object} params - 订单查询参数
 * @param {number} params.userId - 用户ID
 * @param {string} params.orderStatus - 订单状态
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Object} 验证结果
 */
export const validateOrderQueryParams = ({ userId, orderStatus, pageNum, pageSize }) => {
  const errors = []

  if (!validateUserId(userId)) {
    errors.push("用户ID无效")
  }

  if (!orderStatus || orderStatus.trim() === "") {
    errors.push("订单状态不能为空")
  }

  const paginationResult = validatePagination({ pageNum, pageSize })
  if (!paginationResult.isValid) {
    errors.push(...paginationResult.errors)
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 验证售后申请参数
 * @param {Object} params - 售后申请参数
 * @param {string} params.searchValue - 搜索值
 * @param {string} params.status - 状态
 * @returns {Object} 验证结果
 */
export const validateAfterSalesParams = ({ searchValue, status }) => {
  const errors = []

  if (!searchValue || searchValue.trim() === "") {
    errors.push("搜索值不能为空")
  }

  if (!status || status.trim() === "") {
    errors.push("状态不能为空")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 验证快递信息参数
 * @param {Object} params - 快递信息参数
 * @param {number} params.asServiceId - 售后服务ID
 * @param {string} params.deliveryName - 快递公司名称
 * @param {string} params.deliveryNo - 快递单号
 * @returns {Object} 验证结果
 */
export const validateDeliveryParams = ({ asServiceId, deliveryName, deliveryNo }) => {
  const errors = []

  if (!validateProductId(asServiceId)) {
    errors.push("售后服务ID无效")
  }

  if (!deliveryName || deliveryName.trim() === "") {
    errors.push("快递公司名称不能为空")
  }

  if (!deliveryNo || deliveryNo.trim() === "") {
    errors.push("快递单号不能为空")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 通用验证函数
 * @param {Object} data - 要验证的数据
 * @param {Object} rules - 验证规则
 * @returns {Object} 验证结果
 */
export const validate = (data, rules) => {
  const errors = []

  Object.keys(rules).forEach((field) => {
    const rule = rules[field]
    const value = data[field]

    // 必填验证
    if (rule.required && (value === undefined || value === null || value === "")) {
      errors.push(`${rule.label || field} 是必填字段`)
      return
    }

    // 如果值为空且非必填，跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === "")) {
      return
    }

    // 类型验证
    if (rule.type) {
      switch (rule.type) {
        case "phone":
          if (!validatePhone(value)) {
            errors.push(`${rule.label || field} 格式不正确`)
          }
          break
        case "email":
          if (!validateEmail(value)) {
            errors.push(`${rule.label || field} 格式不正确`)
          }
          break
        case "price":
          if (!validatePrice(value)) {
            errors.push(`${rule.label || field} 必须是有效的价格`)
          }
          break
        case "quantity":
          if (!validateQuantity(value)) {
            errors.push(`${rule.label || field} 必须是大于0的整数`)
          }
          break
      }
    }

    // 长度验证
    if (rule.maxLength && value.length > rule.maxLength) {
      errors.push(`${rule.label || field} 长度不能超过${rule.maxLength}个字符`)
    }

    if (rule.minLength && value.length < rule.minLength) {
      errors.push(`${rule.label || field} 长度不能少于${rule.minLength}个字符`)
    }

    // 自定义验证函数
    if (rule.validator && typeof rule.validator === "function") {
      const customResult = rule.validator(value)
      if (customResult !== true) {
        errors.push(customResult || `${rule.label || field} 验证失败`)
      }
    }
  })

  return {
    isValid: errors.length === 0,
    errors
  }
}

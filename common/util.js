function formatTime(time) {
  if (typeof time !== "number" || time < 0) {
    return time
  }

  var hour = parseInt(time / 3600)
  time = time % 3600
  var minute = parseInt(time / 60)
  time = time % 60
  var second = time

  return [hour, minute, second]
    .map(function (n) {
      n = n.toString()
      return n[1] ? n : "0" + n
    })
    .join(":")
}

/**
 * 格式化日期时间字符串
 * @param {string} dateTimeStr - 日期时间字符串，例如 "2024-11-16 14:43:25"
 * @param {boolean} showYear - 是否显示年份
 * @returns {string} - 格式化后的日期时间，例如 "11/16 14:43"
 */
function formatDateTime(dateTimeStr, showYear = false) {
  // 将日期时间字符串解析为 Date 对象
  const date = new Date(dateTimeStr)

  // 如果解析失败，返回原字符串
  if (isNaN(date.getTime())) {
    // throw new Error("Invalid date string");
    return dateTimeStr
  }

  // 提取月份、日期、小时和分钟
  const month = date.getMonth() + 1 // 月份从 0 开始，需要加 1
  const day = date.getDate()
  const hours = date.getHours()
  const minutes = date.getMinutes()

  if (showYear) {
    // 格式化为 YYYY/MM/DD HH:mm
    return `${date.getFullYear()}/${month.toString().padStart(2, "0")}/${day.toString().padStart(2, "0")} ${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`
  }

  // 格式化为 MM/DD HH:mm
  return `${month}/${day} ${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`
}

/**
 * rpx转换为px
 * @param rpx
 * @returns {number}
 */
function rpx2px(rpx) {
  const systemInfo = wx.getSystemInfoSync()
  const screenWidth = systemInfo.windowWidth // 获取当前设备的屏幕宽度
  return (rpx * screenWidth) / 750 // 转换为px
}

/**
 * px转换为rpx
 * @param px
 * @returns {number}
 */
function px2rpx(px) {
  const systemInfo = wx.getSystemInfoSync()
  const screenWidth = systemInfo.windowWidth // 获取当前设备的屏幕宽度
  return (px * 750) / screenWidth // 转换为rpx
}

function isEmptyValue(value, type) {
  if (value === undefined || value === null) {
    return true
  }

  if (typeof value === "string" && !value) {
    return true
  }

  if (Array.isArray(value) && !value.length) {
    return true
  }

  return type === "object" && !Object.keys(value).length
}

function isCurrentTimeInRange(startTime, endTime) {
  const now = new Date().getTime()
  const start = new Date(startTime).getTime()
  const end = new Date(endTime).getTime()
  if (now < start) {
    return "未开始"
  } else if (now > end) {
    return "已过期"
  } else {
    return "在范围内"
  }
}

function formatLocation(longitude, latitude) {
  if (typeof longitude === "string" && typeof latitude === "string") {
    longitude = parseFloat(longitude)
    latitude = parseFloat(latitude)
  }

  longitude = longitude.toFixed(2)
  latitude = latitude.toFixed(2)

  return {
    longitude: longitude.toString().split("."),
    latitude: latitude.toString().split(".")
  }
}

var dateUtils = {
  UNITS: {
    年: 31557600000,
    月: 2629800000,
    天: 86400000,
    小时: 3600000,
    分钟: 60000,
    秒: 1000
  },
  humanize: function (milliseconds) {
    var humanize = ""
    for (var key in this.UNITS) {
      if (milliseconds >= this.UNITS[key]) {
        humanize = Math.floor(milliseconds / this.UNITS[key]) + key + "前"
        break
      }
    }
    return humanize || "刚刚"
  },
  format: function (dateStr) {
    var date = this.parse(dateStr)
    var diff = Date.now() - date.getTime()
    if (diff < this.UNITS["天"]) {
      return this.humanize(diff)
    }
    var _format = function (number) {
      return number < 10 ? "0" + number : number
    }
    return (
      date.getFullYear() +
      "/" +
      _format(date.getMonth() + 1) +
      "/" +
      _format(date.getDate()) +
      "-" +
      _format(date.getHours()) +
      ":" +
      _format(date.getMinutes())
    )
  },
  parse: function (str) {
    //将"yyyy-mm-dd HH:MM:ss"格式的字符串，转化为一个Date对象
    var a = str.split(/[^0-9]/)
    return new Date(a[0], a[1] - 1, a[2], a[3], a[4], a[5])
  }
}

export { rpx2px, px2rpx, isEmptyValue, formatTime, formatLocation, formatDateTime, isCurrentTimeInRange, dateUtils }

<template>
  <view class="address-bar">
    <view class="address-bar__block">
      <view class="address-bar__icon" v-if="showIcon">
        <wd-icon :color="iconConfig.color" :size="iconConfig.size" :name="iconConfig.name" />
      </view>
      <view class="address-bar__content">
        <view class="address-bar__text_name">{{ addressInfo.name }} {{ addressInfo.phone }}</view>
        <view class="address-bar__text_address">{{ addressInfo.address }}</view>
      </view>
      <view class="address-bar__edit" @click="$emit('edit')">
        <wd-icon name="edit" color="#a2d2ff" size="24px" />
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "AddressBar",
    emits: ["edit"],
    props: {
      showIcon: {
        type: Boolean,
        default: false
      },
      addressInfo: {
        type: Object,
        default: () => ({
          name: "",
          phone: "",
          address: ""
        })
      },
      iconConfig: {
        type: Object,
        default: () => ({
          color: "#a2d2ff",
          size: "30px",
          name: "location"
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .address-bar {
    display: flex;
    flex-direction: column;

    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 30rpx;

    &__block {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
      }

      &__content {
        flex: 1;
        flex-direction: column;
        display: flex;
        justify-content: center;
        margin: 0 10rpx;
        font-size: 16px;
        color: #333;

        &__text_name {
          font-weight: bold;
          margin-bottom: 4px;
        }

        &__text_address {
          color: #666;
          font-size: 14px;
        }
      }

      &__edit {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
      }
    }
  }
</style>

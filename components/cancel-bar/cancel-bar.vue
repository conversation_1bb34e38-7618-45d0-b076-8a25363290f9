<template>
  <view class="cancel-bar">
    <view class="cancel-bar__block">
      <wd-img :width="40" :height="40" mode="aspectFit" src="/static/order/ic-bill.png" />
      <view class="content">
        <view class="text_name">取消/退款进度</view>
        <view class="text_cancel">您的订单已取消，可查看退款进度详情</view>
      </view>
      <view class="edit" @click="onShow">
        <wd-icon name="arrow-right" size="24px" />
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "CancelBar",
    emits: ["show"],
    methods: {
      onShow() {
        this.$emit("show")
      }
    }
  }
</script>

<style scoped lang="scss">
  .cancel-bar {
    display: flex;
    align-items: center;
    flex-direction: column;

    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 30rpx;

    &__block {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .content {
        flex: 1;
        flex-direction: column;
        display: flex;
        justify-content: center;
        margin: 0 20rpx;

        .text_name {
          font-weight: bold;
          margin-bottom: 4px;
          font-size: 36rpx;
          color: #333;
        }

        .text_cancel {
          color: #666;
          font-size: 22rpx;
        }
      }

      .edit {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
      }
    }
  }
</style>

<template>
	<view class="coupon-item">
		<view class="corner-label" :style="{background: returnBg(coupon.usedStatus)}">
			{{ returnCouName(coupon.usedStatus) }}</view>
		<view>优惠码编号: {{ coupon.discountCodeNo }}</view>
		<view>优惠金额: {{ coupon.discountAmount }}</view>
		<view>有效期: {{ coupon.endTime }}</view>
		<view>优惠人姓名: {{ coupon.realName }}</view>
		<view>优惠人手机号: {{ coupon.userPhone }}</view>
		<view>备注: {{ coupon.remark || '无' }}</view>
		<view>创建日期: {{ coupon.createTime }}</view>
	</view>
</template>

<script>
	export default {
		name: "CouponItem",
		props: {
			coupon: {
				type: Object,
				required: true,
				default: () => ({
					status: "",
					code: "",
					amount: "",
					expiryDate: "",
					name: "",
					phone: "",
					remark: "",
					creationDate: ""
				})
			}
		},
		methods: {
			returnBg(type) {
				let str = ''
				switch (type) {
					case '0':
						return '#C6A671'
						break
					case '1':
						return '#DE868F'
						break
					case '2':
						return '#666666'
						break
				}
			},
			returnCouName(type) {
				let str = ''
				switch (type) {
					case '0':
						return '未使用'
						break
					case '1':
						return '已使用'
						break
					case '2':
						return '已过期'
						break
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.coupon-item {
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: hidden;
		border: 1px solid #ccc;
		padding: 16px;
		margin: 16px;
		border-radius: 8px;
		background-color: #ffffff;

		.corner-label {
			position: absolute;
			top: 10px;
			right: -30px;
			// background: #e54d42;
			color: #fff;
			padding: 3px 35px;
			transform: rotate(45deg);
			font-size: 12px;
			text-align: center;
			z-index: 1;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		}
	}
</style>
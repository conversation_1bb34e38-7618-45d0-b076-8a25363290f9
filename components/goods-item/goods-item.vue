<template>
  <view class="home-goods-list-view" v-if="data">
    <view class="card" v-for="(item, index) in data" :key="index" @tap="goInfo(item)">
      <view class="product-image">
        <image v-if="item.image === ''" src="/static/logo.png" mode="aspectFill"></image>
        <image v-else :src="item.image" mode="aspectFill"></image>
      </view>
      <view class="info">
        <text class="title">{{ item.product_name }}</text>
        <text class="description">{{ item.sku }}</text>
        <view class="price">
          <text class="new-price">¥{{ item.insidePrice }}</text>
          <text class="old-price">¥{{ item.externalPrice }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "goods-item",
    props: {
      data: {
        type: Array,
        default: [
          {
            image: "",
            price: null,
            product_attr_value_id: "",
            product_id: "",
            product_name: "",
            sku: ""
          }
        ]
      }
    },
    methods: {
      goInfo(item) {
        uni.navigateTo({
          url: `/packages/shop/info?id=${item.product_id}`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .home-goods-list-view {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 10rpx;
  }

  .card {
    width: 49%;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    background: white;
    border-radius: 20rpx;

    .product-image {
      width: 100%;
      height: 300rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background: white;

      > image {
        max-height: 260rpx;
        width: 80%;
        object-fit: cover;
      }
    }

    .info {
      padding: 10rpx;
			
			.title {
			  font-size: 26rpx;
			  font-weight: bold;
			  height: 80rpx;
			  line-height: 40rpx;
			}

      .title,
      .description {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
      }

      .description {
        font-size: 22rpx;
        color: #666;
        height: 60rpx;
        line-height: 30rpx;
      }

      .price {
        margin-top: 20rpx;
        margin-bottom: 30rpx;
        font-size: 32rpx;

        .new-price {
          font-weight: bold;
        }

        .old-price {
          font-size: 26rpx;
          margin-left: 30rpx;
          text-decoration: line-through;
          color: #b1b1b1 100%;
        }
      }
    }
  }
</style>

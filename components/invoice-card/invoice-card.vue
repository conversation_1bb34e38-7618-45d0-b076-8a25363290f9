<template>
  <view class="invoice-card">
    <view v-for="item in iInfo" :key="item.label" class="order-item">
      <text class="cell-label">{{ item.label }}:</text>
      <text v-if="item.type === 'text'" class="cell-value">{{ item.value }}</text>
      <view v-if="item.type === 'image'" class="cell-box" @tap="previewImage(item.value)">
        <image class="cell-image" :src="item.value" mode="aspectFit" />
        <view class="cell-float">点击预览</view>
      </view>
    </view>
  </view>
</template>

<script>
  const ITEM_NAME = {
    type: "发票类型",
    content: "发票内容",
    headerType: "抬头类型",
    headerName: "抬头名称",
    price: "发票金额",
    phone: "收票人手机号",
    email: "收票人邮箱",
    applyTime: "申请时间",
    createTime: "开票时间",
    img: "发票预览"
  }

  export function formatOrderInfo(orderInfo) {
    return Object.keys(orderInfo).map((key) => ({
      label: ITEM_NAME[key] || key,
      value: orderInfo[key],
      type: key === "img" ? "image" : "text"
    }))
  }

  export default {
    name: "InvoiceCard",
    props: {
      invoiceInfo: {
        type: Object,
        default: () => ({
          type: "电子发票",
          content: "商品明细",
          headerType: "个人",
          headerName: "张三",
          price: "100.00",
          phone: "13800000000",
          email: "<EMAIL>",
          applyTime: "2024-09-04 10:10:10",
          createTime: "2024-09-04 10:10:10",
          img: "https://api.autodl.com/docs/assets/2024-01-02-18-19-46-image.png"
        })
      }
    },
    data() {
      return {
        // 可以在这里定义一些数据
        orderName: ITEM_NAME,
        iInfo: formatOrderInfo(this.invoiceInfo)
      }
    },
    methods: {
      // 可以在这里定义一些方法
      previewImage(imageUrl) {
        if (!imageUrl) return
        uni.previewImage({
          urls: [imageUrl],
          current: imageUrl
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .invoice-card {
    display: flex;
    flex-direction: column;
    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 30rpx;

    .order-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      padding: 5rpx 0;

      color: #333333;
      font-size: 28rpx;
      font-weight: 400;

      .cell-label {
        margin-right: 10rpx;
        min-width: 180rpx; /* 固定宽度，确保对齐 */
      }

      .cell-box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 250rpx;
        height: 200rpx;
        border-radius: 8rpx;
        position: relative;

        .cell-image {
          width: 250rpx;
          height: 200rpx;
          border-radius: 8rpx;
        }

        .cell-float {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 50rpx;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 24rpx;
          border-bottom-left-radius: 8rpx;
          border-bottom-right-radius: 8rpx;
        }
      }
    }
  }
</style>

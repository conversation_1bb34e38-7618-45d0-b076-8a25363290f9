<template>
  <view class="order-card">
    <view class="order-title">订单信息</view>
    <view v-for="item in iInfo" :key="item.label" class="order-item">
      <text class="cell-label">{{ item.label }}:</text>
      <text class="cell-value">{{ item.value }}</text>
    </view>
  </view>
</template>

<script>
  const ITEM_NAME = {
    orderType: "订单状态",
    orderNo: "订单编号",
    createTime: "下单时间"
  }

  export function formatOrderInfo(orderInfo) {
    return Object.keys(orderInfo).map((key) => ({
      label: ITEM_NAME[key] || key,
      value: orderInfo[key]
    }))
  }

  export default {
    name: "InvoiceStatus",
    props: {
      invoiceStatus: {
        type: Object,
        default: () => ({
          orderType: "已完成",
          orderNo: "35126666778899",
          createTime: "2024-09-04 10:10:10"
        })
      }
    },
    data() {
      return {
        iInfo: formatOrderInfo(this.invoiceStatus)
      }
    }
  }
</script>

<style scoped lang="scss">
  .order-card {
    display: flex;
    flex-direction: column;
    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 30rpx;

    .order-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 24rpx;
    }

    .order-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      color: #666666;
      font-size: 28rpx;

      .cell-label {
        margin-right: 10rpx;
        min-width: 120rpx; /* 固定宽度，确保对齐 */
      }
    }
  }
</style>

<template>
  <view class="invoice-step-wrapper">
    <wd-steps :active="2" align-center>
      <wd-step
        v-for="(step, index) in steps"
        :key="index"
        icon="check-circle-filled"
        :title="step"
        :style="{ width: `27vw` }"
      />
    </wd-steps>
  </view>
</template>
<script>
  export default {
    name: "InvoiceStep",
    props: {
      steps: {
        type: Array,
        default: () => ["申请开票", "开票中", "开票完成"]
      }
    }
  }
</script>

<style lang="scss">
  .invoice-step-wrapper {
    width: 100%;
    padding: 30rpx 15rpx;
    background-color: #ffffff;
    border-radius: 8px;
    box-sizing: border-box;
  }

  .invoice-step-wrapper .wd-steps {
    /* 确保容器是 flex 布局 */
    display: flex !important;
    width: 100% !important;
  }

  .invoice-step-wrapper .wd-step {
    /* 让每个步骤弹性增长并占据均等空间 */
    flex: 1 !important;
    /* 某些UI库可能需要重置最小宽度 */
    min-width: 0 !important;
  }

  .invoice-step-wrapper .wd-step__icon {
    background: #4fc16e !important;
    color: #fff !important;
  }

  .invoice-step-wrapper .wd-step__title {
    color: #222;
    font-size: 26rpx;
    font-weight: 500;
    margin-top: 10rpx;
    white-space: normal;
    text-align: center;
    line-height: 1.2;
  }

  .invoice-step-wrapper .wd-step__line {
    background-color: #f0f0f0 !important;
    height: 1px !important;
  }
</style>

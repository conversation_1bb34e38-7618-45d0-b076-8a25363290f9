<template>
  <view class="logistics-bar">
    <wd-steps :active="0" vertical>
      <wd-step>
        <template #icon>
          <view class="icon-box truck">
            <wd-img src="/static/order/ic-car.png" :width="24" :height="24" mode="aspectFit" />
          </view>
        </template>
        <template #title>
          <view class="title-block" @tap="onShow">
            <view class="step-title">{{ lastInfo.title }}</view>
            <wd-icon name="arrow-right" size="20" color="#333" class="arrow-icon" />
          </view>
        </template>
        <template #description>
          <view class="step-desc">
            <view>{{ lastInfo.desc }}</view>
            <view class="step-time">{{ lastInfo.time }}</view>
          </view>
        </template>
      </wd-step>
      <wd-step>
        <template #icon>
          <view class="icon-box location">
            <wd-icon name="location" size="24" />
          </view>
        </template>
        <template #title>
          <view class="title-block">
            <view class="step-title">{{ logisticsInfo.name }} {{ logisticsInfo.phone }}</view>
          </view>
        </template>
        <template #description>
          <view class="step-desc"> {{ logisticsInfo.address }} </view>
        </template>
      </wd-step>
    </wd-steps>
  </view>
</template>

<script>
  export default {
    name: "LogisticsBar",
    emits: ["onShow"],
    props: {
      lastInfo: {
        type: Object,
        default: () => ({
          title: "运输中",
          desc: "当前位置在【XX市】XX物流集散中心，即将发往XX分拨中心",
          time: "2024-9-8 10:00:00"
        })
      },
      logisticsInfo: {
        type: Object,
        default: () => ({
          name: "张三",
          phone: "132****0350",
          address: "北京市朝阳区XXX街道XX小区001号110"
        })
      }
    },
    methods: {
      onShow() {
        this.$emit("onShow")
      }
    }
  }
</script>

<style scoped lang="scss">
  .logistics-bar {
    display: flex;
    align-items: center;
    flex-direction: column;

    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 8px;
    margin-bottom: 30rpx;
  }
  .icon-box {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    &.truck {
      background: #bca789;
    }
    &.location {
      background: #eaf3ff;
    }
  }
  .title-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;

    .step-title {
      font-weight: bold;
      font-size: 30rpx;
      color: #222;
      margin-left: 10rpx;
    }
  }

  .step-desc {
    color: #666;
    font-size: 26rpx;
    margin-top: 8rpx;
    margin-left: 10rpx;
  }
  .step-time {
    color: #888;
    font-size: 24rpx;
    margin-top: 4rpx;
  }
</style>

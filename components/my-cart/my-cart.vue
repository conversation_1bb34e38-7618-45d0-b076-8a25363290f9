<template>
  <view class="wrapper">
    <wd-popup
      v-model="showPopup"
      position="bottom"
      custom-style="height: 55vh; background-color: white;"
      :safe-area-inset-bottom="true"
      transition="fade"
      closable
      @close="onClose"
    >
      <view class="shop-pop-view">
        <view class="sp-header">
          <view class="sph-left">
            <image src="../../static/logo.png" mode="widthFix"></image>
          </view>
          <view class="sph-right">
            <view class="sphr-price">¥4199</view>
            <view class="sphr-title">HUAWEI MatePad Pro 13.2英寸</view>
            <view class="sphr-selected">已选：耀金黑 12GB+512GB x1</view>
          </view>
        </view>
        <view class="sp-options">
          <text class="sp-item-name">版本</text>
          <view class="sp-item-right">
            <text class="sir-version sir-version-checked">8G+128GB</text>
            <text class="sir-version">8G+128GB</text>
            <text class="sir-version">8G+128GB</text>
          </view>
        </view>

        <view class="sp-options">
          <text class="sp-item-name">颜色</text>
          <view class="sp-item-right">
            <view class="sir-color sir-color-select">
              <view class="sirc-img">
                <image src="/static/logo.png" mode="widthFix"></image>
              </view>
              <view class="sirc-txt">耀金黑</view>
            </view>
            <view class="sir-color">
              <view class="sirc-img">
                <image src="/static/logo.png" mode="widthFix"></image>
              </view>
              <view class="sirc-txt">耀金黑</view>
            </view>
            <view class="sir-color">
              <view class="sirc-img">
                <image src="/static/logo.png" mode="widthFix"></image>
              </view>
              <view class="sirc-txt">耀金黑</view>
            </view>
          </view>
        </view>

        <view class="sp-options">
          <text class="sp-item-name">赠品</text>
          <view class="sp-item-right">
            <view class="sir-gift">
              <view>
                <image src="/static/logo.png" mode="widthFix"></image>
                <text>平板保护壳赠送</text>
              </view>
              <view>x1</view>
            </view>
          </view>
        </view>
        <view class="sp-promise">
          <image src="/static/logo.png" mode="widthFix"></image>
          <view>品牌授权官方授权发货，48小时发货，7天无理由退换</view>
        </view>

        <view class="sp-footer">
          <view class="spf-sure-btn" @tap="onClose">确定</view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script>
  export default {
    name: "my-cart",
    emits: ["update:show"],

    props: {
      data: {
        type: Object,
        default: {}
      },
      show: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      showPopup: {
        get() {
          return this.show
        },
        set(value) {
          this.$emit("update:show", value)
        }
      }
    },
    methods: {
      onClose() {
        this.$emit("update:show", false)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .wrapper {
    position: relative;
    width: 100%;
    height: 100vh;
    z-index: 100;

    .shop-pop-view {
      padding: 30rpx;

      .sp-header {
        display: flex;
        align-items: center;
        margin-top: 40rpx;

        .sph-left {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 130rpx;
          height: 130rpx;
          padding: 30rpx;
          background-color: #fafafa;
          box-shadow: 1rpx 4rpx 10rpx #dddddd;
          border-radius: 20rpx;

          > image {
            width: 100%;
            max-height: 100%;
          }
        }

        .sph-right {
          flex: 1;
          overflow: hidden;
          margin-left: 20rpx;

          > view {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .sphr-price {
            font-weight: bold;
            font-size: 38rpx;
            color: #bd3124;
          }

          .sphr-title {
            font-size: 32rpx;
            color: rgba(51, 51, 51, 1);
            margin-top: 20rpx;
            font-weight: bold;
          }

          .sphr-selected {
            font-size: 32rpx;
            color: rgba(91, 91, 91, 1);
            margin-top: 20rpx;
          }
        }
      }

      .sp-options {
        margin-top: 30rpx;
        display: flex;
        align-content: center;

        .sp-item-name {
          width: 100rpx;
          color: #ccc;
          font-size: 28rpx;
        }

        .sp-item-right {
          flex: 1;
          display: flex;
          align-items: center;
        }

        .sir-version {
          display: inline-block;
          height: 50rpx;
          line-height: 50rpx;
          background: #fafafa 100%;
          border: 1rpx solid #fafafa;
          margin-right: 10rpx;
          margin-bottom: 10rpx;
          border-radius: 20rpx;
          padding: 0 20rpx;
          font-size: 24rpx;
        }

        .sir-version-checked {
          background: #c6a670 100% !important;
          border: 1rpx solid #b28d57 !important;
          color: white;
        }

        .sir-color-select {
          border: 1rpx solid #b28d57 !important;

          .sirc-txt {
            background: #c6a670 100% !important;
            color: white;
          }
        }

        .sir-color {
          width: 100rpx;
          background-color: white;
          border: 1rpx solid #ccc;
          border-radius: 15rpx;
          margin-right: 10rpx;

          .sirc-img {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100rpx;
            height: 90rpx;
            overflow: hidden;

            image {
              width: 60rpx;
              max-height: 60rpx;
            }
          }

          .sirc-txt {
            background: #fafafa;
            height: 35rpx;
            line-height: 35rpx;
            font-size: 20rpx;
            text-align: center;
          }
        }

        .sir-gift {
          display: flex;
          align-items: center;
          width: 100%;

          > view:nth-child(1) {
            flex: 1;
            display: flex;
            align-items: center;

            > image {
              width: 20rpx;
              height: 20rpx;
            }

            > text {
              font-size: 18rpx;
              margin-left: 20rpx;
            }
          }

          > view:nth-child(2) {
            text-align: right;
            width: 80rpx;
            font-size: 22rpx;
          }
        }
      }

      .sp-promise {
        display: flex;
        align-items: center;
        margin-top: 30rpx;

        > image {
          width: 30rpx;
          height: 30rpx;
          margin-left: 10rpx;
        }

        > view {
          flex: 1;
          margin-left: 20rpx;
          font-size: 20rpx;
          color: #c6a670;
        }
      }

      .sp-footer {
        margin-top: 100rpx;
        text-align: center;

        .spf-sure-btn {
          width: 500rpx;
          height: 80rpx;
          line-height: 80rpx;
          color: white;
          margin: 0 auto;
          background: #c6a670;
          border-radius: 50rpx;
        }
      }
    }
  }
</style>

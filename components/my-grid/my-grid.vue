<template>
  <view class="container">
    <view v-if="title !== ''" class="title-bar">
      <view class="title">{{ title }}</view>
      <view class="more" v-if="moreTips !== ''" @tap="onShowMore"
        >{{ moreTips }}
        <wd-icon name="arrow-right" size="22px"></wd-icon>
      </view>
    </view>
    <!--    使用grid布局样式，根据 maxColumn 值控制一行4个或者5个-->
    <view class="grid-view" :style="{ 'grid-template-columns': `repeat(${maxColumn}, 1fr)` }">
      <view class="grid-item" v-for="(item, index) in gridList" :key="index" @tap="onItemClick(item)">
        <view class="grid-item-content" v-if="!item.openType">
          <image :src="item.icon" mode="aspectFill" />
          <view class="grid-item-title">
            {{ item.title }}
          </view>
        </view>
        <view class="grid-item-content" v-if="item.openType === 'contact'">
          <button class="contact" open-type="contact" @tap="onContact">
            <image :src="item.icon" mode="aspectFill" class="icon" />
            <view class="grid-item-title">
              {{ item.title }}
            </view>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
  export default {
    name: "my-grid",
    emits: ["onShowMore", "onItemClick"],
    props: {
      title: {
        type: String,
        default: ""
      },
      moreTips: {
        type: String,
        default: ""
      },
      maxColumn: {
        type: Number,
        default: 4
      },
      gridType: {
        type: String,
        default: ""
      },
      gridList: {
        type: Array,
        default() {
          return []
        }
      }
    },

    methods: {
      onItemClick(item) {
        this.$emit("onItemClick", item)
      },
      onShowMore() {
        this.$emit("onShowMore", this.gridType)
      },
      onContact() {
        console.log("onContact")
      }
    }
  }
</script>

<style lang="scss">
  .container {
    display: flex;
    flex-direction: column;
    padding: 20rpx;
    background: white;
    border-radius: 20rpx;
    margin: 20rpx;

    .title-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 32rpx;
        font-weight: bold;
      }

      .more {
        font-size: 28rpx;
        color: #999;
      }
    }

    .grid-view {
      display: grid;
      grid-gap: 20rpx;
      margin-top: 20rpx;

      .grid-item {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 20rpx;

        .grid-item-content {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding: 10rpx;

          image {
            width: 56rpx;
            height: 56rpx;
          }

          .grid-item-title {
            margin-top: 10rpx;
            font-size: 28rpx;
            font-weight: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .contact {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border: 0 !important;
            border-radius: 0 !important;
            outline: none !important;
            box-shadow: none !important;
            line-height: revert;
            background: transparent;
            padding: 0;

            image {
              width: 56rpx;
              height: 56rpx;
            }

            .grid-item-title {
              font-size: 28rpx;
              font-weight: normal;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .contact::after {
            border: none !important;
          }
        }
      }
    }
  }
</style>

<template>
  <view class="nav-box" :style="{ height: height + 'px', background: bgColor }">
    <view class="status_bar" :style="{ height: statusBarHeight + 'px' }">
      <!-- uni-ui这里是状态栏 -->
    </view>
    <view
      class="nav-main flex align-center justify-center w-full"
      :style="{ height: navBarHeight + 'px', lineHeight: navBarHeight + 'px', zIndex: 99 }"
    >
      <view class="nav-main-back" style="width: 100rpx" @tap="backAction" v-if="backIcon || homeIcon">
        <wd-icon v-if="localBackIcon" name="thin-arrow-left" :color="backIconColor" size="18px" />
        <wd-icon v-if="localHomeIcon" name="home" :color="homeIconColor" size="18px" />
      </view>
      <view class="nav-main-back" v-else></view>
      <view class="nav-main-content flex align-center justify-center">
        <wd-icon v-if="showIcon" :name="iconConfig.name" :size="iconConfig.size" :color="iconConfig.color" />
        <view v-if="title" class="nav-main-title" :style="{ color: textColor }">{{ title }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "my-nav",
    emits: ["onBack"],

    props: {
      bgColor: {
        type: String,
        default: "#F5F5F5"
      },
      backIcon: {
        type: Boolean,
        default: false
      },
      backIconColor: {
        type: String,
        default: "#000"
      },
      homeIcon: {
        type: Boolean,
        default: false
      },
      homeIconColor: {
        type: String,
        default: "white"
      },
      title: {
        type: String,
        default: ""
      },
      textColor: {
        type: String,
        default: "#000"
      },
      showIcon: {
        type: Boolean,
        default: false
      },
      iconConfig: {
        type: Object,
        default: () => ({
          color: "#333333",
          size: "22px",
          name: "close-circle"
        })
      }
    },
    data() {
      return {
        //总高度
        height: 0,
        //胶囊位置信息
        menuButtonRect: {},
        //状态栏的高度
        statusBarHeight: 0,
        //导航栏的高度
        navBarHeight: 0,
        // 本地控制图标显示
        localBackIcon: this.backIcon,
        localHomeIcon: this.homeIcon
      }
    },
    created() {
      this.getHeight()
    },

    watch: {
      homeIcon: {
        handler(val) {
          if (val) {
            console.log("homeIcon", val)
            this.localBackIcon = false
          }
          this.localHomeIcon = val
        },
        immediate: true
      },
      backIcon: {
        handler(val) {
          if (val) {
            this.localHomeIcon = false
          }
          this.localBackIcon = val
        },
        immediate: true
      }
    },

    methods: {
      getHeight() {
        if (wx.canIUse("getMenuButtonBoundingClientRect")) {
          let sysInfo = uni.getWindowInfo() //状态栏的高度
          this.statusBarHeight = sysInfo.statusBarHeight
          // 胶囊位置信息
          let rect = uni.getMenuButtonBoundingClientRect()
          this.menuButtonRect = JSON.parse(JSON.stringify(rect))
          // 导航栏高度
          let navBarHeight = (rect.top - sysInfo.statusBarHeight) * 2 + rect.height
          this.navBarHeight = navBarHeight
          // 自定义导航栏的高度
          this.height = sysInfo.statusBarHeight + navBarHeight
          uni.setStorageSync("navHeight", this.height)
        } else {
          uni.showToast({
            title: "您的微信版本过低，界面可能会显示不正常",
            icon: "none",
            duration: 4000
          })
        }
      },

      //返回
      backAction() {
        if (this.localHomeIcon) {
          uni.redirectTo({
            url: "/pages/home/<USER>"
          })
          return
        }
        uni.navigateBack({
          delta: 1
        })
      }
    }
  }
</script>

<style lang="scss">
  .nav-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    align-items: center;
    justify-content: center;
    //position: fixed;
    //top: 0;
    background: white;

    .img-info {
      width: 166rpx;
      height: 58rpx;
      margin-left: 24rpx;
      margin-top: 12rpx;
    }
  }

  .status_bar {
    // height: var(--status-bar-height);
    width: 100%;
    // background:#ff0;
  }

  .nav-main {
    position: relative;

    // background:#f00;
    .nav-main-back {
      position: absolute;
      left: 10rpx;
      font-size: 40rpx;
    }

    .nav-main-home {
      position: absolute;
      left: 30rpx;
      font-size: 40rpx;
      color: white;
    }

    .nav-main-content {
      flex: 1;
      gap: 8rpx;
    }

    .nav-main-title {
      font-family:
        PingFangSC,
        PingFang SC,
        sans-serif;
      font-weight: 500;
      font-size: 34rpx;
      color: #1c3c7b;
      line-height: 48rpx;
      text-align: center;
      font-style: normal;
			width: 280rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
    }
  }

  .flex {
    display: flex;
  }

  .align-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .w-full {
    width: 100%;
  }
</style>

<template>
  <view class="my-page-wrapper" :style="wrapperStyle">
    <!-- 顶部自定义导航栏 -->
    <view class="my-page__nav" v-if="$slots.nav">
      <slot name="nav"></slot>
    </view>

    <!--自定义背景-->
    <view class="my-page__background-block" v-if="showBackground" :style="backGroundStyle"></view>

    <!-- 固定头部区域 (新增) -->
    <view class="my-page__fixed-header" v-if="showFixedHeader && $slots.fixedHeader" :style="fixedHeaderStyle">
      <slot name="fixedHeader"></slot>
    </view>

    <!-- 固定操作栏 -->
    <view class="my-page__fixed-bar" v-if="showFixedBar && $slots.fixedBar" :style="{ top: navHeight + 'px' }">
      <slot name="fixedBar"></slot>
    </view>

    <!-- 可选Tab区 -->
    <view class="my-page__tabs" v-if="showTabs && $slots.tabs" :style="{ top: navHeight + 'px' }">
      <slot name="tabs"></slot>
    </view>

    <!-- 滚动内容区 -->
    <scroll-view
      class="my-page__scroll"
      id="my-page__scroll"
      scroll-y
      scroll-with-animation
      :scroll-top="scrollTopValue"
      :refresher-enabled="refresherConfig.enabled"
      :refresher-threshold="refresherConfig.threshold"
      :refresher-default-style="refresherConfig.defaultStyle"
      :refresher-background="refresherConfig.background"
      :refresher-triggered="refreshing"
      :lower-threshold="loadMoreConfig.threshold"
      :upper-threshold="scrollConfig.upperThreshold"
      :style="computedContentStyle"
      @refresherrefresh="$emit('refresh')"
      @refresherrestore="$emit('restore')"
      @refresherabort="$emit('abort')"
      @scrolltolower="handleScrollToLower"
      @scroll="handleScroll"
    >
      <slot></slot>
      <view v-if="showBottomEmptyBlock" class="my-page__scroll__back-top_empty-block"></view>
      <!-- 上拉加载状态 -->
      <view v-if="loadMoreConfig.enabled" class="my-page__loading">
        <template v-if="loading">
          <view class="loading">
            <text class="loading__text">{{ loadMoreConfig.loadingText }}</text>
            <text class="loading__dots"></text>
          </view>
        </template>
        <text v-else-if="finished" class="finished">
          {{ loadMoreConfig.finishedText }}
        </text>
      </view>
    </scroll-view>

    <view v-if="showBackToTop && showBackTopButton" class="my-page__back-top" @click="handleBackTop">
      <wd-icon name="arrow-up" color="#ffffff"></wd-icon>
    </view>

    <!-- 底部操作栏 -->
    <view v-if="showFooter && $slots.footer" class="my-page__footer">
      <slot name="footer"></slot>
    </view>

    <!-- 安全区 -->
    <view class="my-page__safe-area"></view>
  </view>
</template>

<script>
  export default {
    name: "my-page",
    props: {
      autoHeight: { type: Boolean, default: true }, // 自动计算高度
      customStyle: { type: Object, default: () => ({}) }, // 自定义样式
      // 基础配置
      navHeight: { type: Number, default: uni.getStorageSync("navHeight") },
      fixedBarHeight: { type: Number, default: 0 },
      fixedHeaderHeight: { type: Number, default: 0 },
      tabsHeight: { type: Number, default: 0 },
      footerHeight: { type: Number, default: 0 },
      showTabs: { type: Boolean, default: false },
      showFixedBar: { type: Boolean, default: false },
      showFixedHeader: { type: Boolean, default: false },
      showFooter: { type: Boolean, default: false },
      showBackToTop: { type: Boolean, default: false },
      showBackground: { type: Boolean, default: false },
      showBottomEmptyBlock: { type: Boolean, default: true },
      backGroundStyle: {
        type: Object,
        default: () => ({
          height: "100%",
          background: "linear-gradient(180deg, rgba(194, 152, 92, 1) 0%, rgba(194, 152, 92, 0.4) 100%)"
        })
      },
      // 刷新配置
      refresherConfig: {
        type: Object,
        default: () => ({
          enabled: true,
          threshold: 100,
          defaultStyle: "black",
          background: "transparent"
        })
      },
      // 加载配置
      loadMoreConfig: {
        type: Object,
        default: () => ({
          enabled: true,
          threshold: 50,
          loadingText: "正在加载更多",
          finishedText: "没有更多了"
        })
      },
      // 滚动配置
      scrollConfig: {
        type: Object,
        default: () => ({
          top: 0,
          upperThreshold: 50,
          lowerThreshold: 50
        })
      },
      loading: { type: Boolean, default: false },
      finished: { type: Boolean, default: false },
      refreshing: { type: Boolean, default: false },
      background: {
        type: String,
        default: "#f8f8f8" // 默认背景色
      },
      hasScrollToTop: { type: Boolean, default: true },
      scrollToTop: { type: Number, default: 0 }
    },
    emits: ["refresh", "restore", "abort", "load-more", "scroll"],
    data() {
      return {
        pageHeight: 0,
        contentStyle: {},
        currentScrollTop: 0,
        scrollTopValue: 0,
        isScrolling: false, // 新增滚动状态
        scrollEndTimer: null
      }
    },
    computed: {
      // 计算内容区样式
      computedContentStyle() {
        return {
          height: `${this.pageHeight * 2}rpx`,
          flex: !this.showFixedBar && !this.showFooter && !this.showTabs && this.autoHeight ? 1 : "none",
          marginTop: this.showFixedHeader ? `${this.fixedHeaderHeight}px` : 0,
          ...this.contentStyle
        }
      },

      wrapperStyle() {
        return {
          background: this.background,
          ...this.customStyle
        }
      },

      showBackTopButton() {
        // 滚动超过 400rpx 时显示
        return this.currentScrollTop > uni.upx2px(400)
      },

      // 固定头部样式
      fixedHeaderStyle() {
        return {
          top: this.navHeight + "px",
          zIndex: 1
        }
      }
    },

    watch: {
      scrollToTop: {
        handler(val) {
          if (this.hasScrollToTop && typeof val === "number") {
            this.isScrolling = true
            this.scrollTopValue = this.currentScrollTop
            this.$nextTick(() => {
              this.scrollTopValue = val
            })
          }
        },
        immediate: true
      }
    },
    mounted() {
      this.initPage()
    },

    onShow() {},

    beforeDestroy() {
      if (this._debouncedCalc) {
        uni.offWindowResize(this._debouncedCalc)
      }
    },

    methods: {
      debounce(fn, delay = 200) {
        let timer = null
        return function (...args) {
          if (timer) clearTimeout(timer)
          timer = setTimeout(() => fn.apply(this, args), delay)
        }
      },

      // 初始化页面
      async initPage() {
        await this.$nextTick()
        this.calcPageHeight()

        // 使用防抖处理resize
        const debouncedCalc = this.debounce(this.calcPageHeight, 200)
        uni.onWindowResize(debouncedCalc)
        this._debouncedCalc = debouncedCalc // 存储引用以便清理
      },

      // 计算页面高度
      calcPageHeight() {
        try {
          const {
            safeArea: { height: safeAreaHeight }
          } = uni.getWindowInfo()

          const _tabHeight = this.showTabs ? uni.upx2px(this.tabsHeight) : 0
          const _footerHeight = this.showFooter ? uni.upx2px(this.footerHeight) : 0
          const _fixedBarHeight = this.showFixedBar ? uni.upx2px(this.fixedBarHeight) : 0
          const _fixedHeaderHeight = this.showFixedHeader ? this.fixedHeaderHeight : 0

          const offsetHeight = [this.navHeight, _tabHeight, _footerHeight, _fixedBarHeight, _fixedHeaderHeight]
            .filter(Boolean)
            .reduce((sum, h) => sum + h, 0)

          const { platform, screenHeight, statusBarHeight, safeArea } = uni.getSystemInfoSync()
          if (platform === "ios") {
            this.pageHeight = safeAreaHeight - offsetHeight
          } else {
            this.pageHeight = safeAreaHeight - offsetHeight - 18
          }
        } catch (err) {
          this.pageHeight = 0
        }
      },

      // 滚动处理
      handleScroll(event) {
        this.currentScrollTop = event.detail.scrollTop
        this.$emit("scroll", {
          scrollTop: event.detail.scrollTop,
          scrollHeight: event.detail.scrollHeight,
          ...event.detail
        })
        if (this.isScrolling) {
          // 如果是代码触发的滚动，设置一个定时器来检测滚动是否结束
          clearTimeout(this.scrollEndTimer)
          this.scrollEndTimer = setTimeout(() => {
            this.isScrolling = false
          }, 100) // 100ms 内无滚动事件，视为滚动结束
        }
      },

      handleBackTop() {
        this.scrollTopValue = this.currentScrollTop
        this.$nextTick(() => {
          this.scrollTopValue = 0
        })
      },

      // 触底加载
      handleScrollToLower() {
        if (!this.loadMoreConfig.enabled || this.loading || this.finished) return
        this.$emit("load-more")
      }
    }
  }
</script>

<style lang="scss" scoped>
  .my-page__footer {
    // min-height: 100rpx;
  }
  // 先定义所有混合器
  @mixin loading-animation {
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;

      &__text {
        margin-right: 10rpx;
      }

      &__dots {
        &::after {
          content: ".";
          display: inline-block;
          width: 20rpx;
          animation: dotAnimation 1.5s infinite;
        }
      }
    }

    .finished {
      color: var(--text-color-light, #999);
    }

    @keyframes dotAnimation {
      0% {
        content: ".";
      }
      33% {
        content: "..";
      }
      66% {
        content: "...";
      }
      100% {
        content: ".";
      }
    }
  }

  @mixin loading-style {
    &__loading {
      padding: 20rpx;
      text-align: center;
      color: var(--text-color-gray, #999);
      font-size: var(--font-size-sm, 24rpx);

      @include loading-animation;
    }
  }

  @mixin safe-area {
    &__safe-area {
      height: env(safe-area-inset-bottom);
      background: transparent;
    }
  }

  .my-page__back-top {
    position: fixed;
    right: 30rpx;
    bottom: calc(env(safe-area-inset-bottom) + 120rpx);
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: #c6a772;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }

  // 根元素样式
  .my-page-wrapper {
    $zindex-nav: 100;
    $zindex-tabs: 90;
    $zindex-content: 80;

    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--my-page-bg, #f8f8f8);
    --my-page-nav-height: var(--nav-height, 44px);
    --my-page-loading-color: var(--color-text-secondary, #999);

    .my-page__nav,
    .my-page__tabs,
    .my-page__footer {
      position: relative;
      background: var(--component-bg, #fff);
    }

    .my-page__nav {
      z-index: $zindex-nav;
    }

    .my-page__background-block {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }

    .my-page__fixed-header {
      position: fixed;
      left: 0;
      right: 0;
      width: 100%;
      z-index: 1;
    }

    .my-page__fixed-bar {
      position: sticky;
      z-index: $zindex-tabs;
      width: 100%;
    }

    .my-page__tabs {
      z-index: $zindex-tabs;
      position: sticky;
      top: 0;
    }

    .my-page__scroll {
      flex: 1;
      position: relative;
      z-index: $zindex-content;

      &::-webkit-scrollbar {
        display: none;
      }

      &__back-top_empty-block {
        height: 100rpx; // 确保有足够的空间显示回到顶部按钮
      }
    }

    @include loading-style;
    @include safe-area;
  }
</style>

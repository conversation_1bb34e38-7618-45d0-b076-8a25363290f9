<template>
  <wd-popup
    v-model="showPopup"
    :custom-style="popupStyle"
    :position="position"
    :z-index="zIndex"
    :mask-close="maskClose"
    @close="handleClose"
    @open="handleOpen"
  >
    <view class="my-popup__header" id="my-popup__header" ref="header" :style="headerStyle" v-if="showHeader">
      <text class="my-popup__header-title">{{ title }}</text>
      <wd-icon v-if="showClose" :name="closeIconName" size="20" color="#333" @click="handleClose" />
    </view>

    <view class="my-popup__topbar" id="my-popup__topbar" v-if="showTopBar && $slots.topBar" ref="topbar">
      <slot name="topBar"></slot>
    </view>

    <scroll-view
      class="my-popup__content"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-with-animation="scrollWithAnimation"
      :refresher-enabled="refresherEnabled"
      :style="computedContentStyle"
      ref="content"
      @scroll="handleScroll"
    >
      <slot name="content"></slot>
    </scroll-view>

    <view
      class="my-popup__footer"
      id="my-popup__footer"
      v-if="showFooter && $slots.footer"
      ref="footer"
      :style="footerStyle"
    >
      <slot name="footer"></slot>
    </view>

    <view class="my-popup__safe-area" id="my-popup__safe-area" v-if="useSafeArea"></view>
  </wd-popup>
</template>

<script>
  export default {
    name: "my-popup",
    props: {
      visible: { type: Boolean, default: false },
      title: { type: String, default: "标题" },
      showHeader: { type: Boolean, default: true },
      showClose: { type: Boolean, default: true },
      closeIconName: { type: String, default: "close" },
      showTopBar: { type: Boolean, default: false },
      showFooter: { type: Boolean, default: false },
      headerStyle: { type: Object, default: () => ({}) },
      contentStyle: { type: Object, default: () => ({}) },
      footerStyle: { type: Object, default: () => ({}) },
      popupStyle: { type: [String, Object], default: "border-radius:32rpx;height:70vh;" },
      scrollWithAnimation: { type: Boolean, default: true },
      refresherEnabled: { type: Boolean, default: false },
      useSafeArea: { type: Boolean, default: true },
      position: { type: String, default: "bottom" },
      zIndex: { type: Number, default: 100 },
      maxHeight: { type: [String, Number], default: null },
      minHeight: { type: [String, Number], default: null },
      maskClose: { type: Boolean, default: false },
      scrollTop: { type: Number, default: 0 }
    },

    emits: ["close", "open", "update:visible", "scroll", "before-close", "after-close"],

    data() {
      return {
        headerHeight: 0,
        topbarHeight: 0,
        footerHeight: 0,
        safeAreaHeight: 0
      }
    },

    computed: {
      computedContentStyle() {
        let style = {
          ...this.contentStyle
        }

        // 动态计算内容区高度
        if (this.maxHeight) {
          style.maxHeight = typeof this.maxHeight === "number" ? `${this.maxHeight}px` : this.maxHeight
        } else {
          style.height = `calc(70vh - ${this.headerHeight}px - ${this.topbarHeight}px - ${this.footerHeight}px - ${this.safeAreaHeight}px)`
        }

        if (this.minHeight) {
          style.minHeight = typeof this.minHeight === "number" ? `${this.minHeight}px` : this.minHeight
        }

        // console.log("Computed Content Style:", style)
        return style
      },

      showPopup: {
        get() {
          return this.visible
        },
        set(value) {
          this.$emit("update:visible", value)
        }
      }
    },

    watch: {
      visible(val) {
        if (val) {
          // 弹窗打开后延迟计算高度
          setTimeout(() => {
            this.updateElementHeights()
          }, 300) // 300ms等待弹窗动画完成
        }
      }
    },

    mounted() {
      if (this.visible) {
        this.$nextTick(() => {
          this.updateElementHeights()
        })
      }

      // 使用防抖处理resize
      const debouncedCalc = this.debounce(this.updateElementHeights, 200)
      uni.onWindowResize(debouncedCalc)
      this._debouncedCalc = debouncedCalc
    },

    beforeDestroy() {
      if (this._debouncedCalc) {
        uni.offWindowResize(this._debouncedCalc)
      }
    },

    methods: {
      handleClose() {
        this.$emit("before-close")
        this.$emit("close")
        this.$emit("update:visible", false)

        // 通过setTimeout模拟关闭动画结束后触发事件
        setTimeout(() => {
          this.$emit("after-close")
        }, 300)
      },

      handleOpen() {
        this.$emit("open")
      },

      handleScroll(e) {
        this.$emit("scroll", e)
      },

      debounce(fn, delay = 200) {
        let timer = null
        return function (...args) {
          if (timer) clearTimeout(timer)
          timer = setTimeout(() => fn.apply(this, args), delay)
        }
      },

      updateElementHeights() {
        // 添加延时，确保弹窗动画完成且元素渲染完毕
        setTimeout(() => {
          const query = uni.createSelectorQuery().in(this)

          // 依次查询各元素高度
          Promise.all([
            this.getElementHeight(query, "#my-popup__header", this.showHeader),
            this.getElementHeight(query, "#my-popup__topbar", this.showTopBar && this.$slots.topBar),
            this.getElementHeight(query, "#my-popup__footer", this.showFooter && this.$slots.footer)
          ]).then(([headerHeight, topbarHeight, footerHeight]) => {
            this.headerHeight = headerHeight
            this.topbarHeight = topbarHeight
            this.footerHeight = footerHeight

            // 安全区域高度计算
            if (this.useSafeArea) {
              this.safeAreaHeight = uni.getSystemInfoSync().safeAreaInsets?.bottom || 0
            } else {
              this.safeAreaHeight = 0
            }

            /*    console.log("计算完成的高度:", {
              headerHeight: this.headerHeight,
              topbarHeight: this.topbarHeight,
              footerHeight: this.footerHeight,
              safeAreaHeight: this.safeAreaHeight
            })*/
          })
        }, 100) // 100ms 延迟，可根据实际情况调整
      },

      // 辅助方法：获取单个元素高度
      getElementHeight(query, selector, condition) {
        return new Promise((resolve) => {
          if (!condition) {
            resolve(0)
            return
          }

          query.select(selector).boundingClientRect((data) => {
            const height = data ? data.height : 0
            // console.log(`${selector} height:`, height)
            resolve(height)
          })

          query.exec()
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .my-popup {
    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      background-color: #fff;
    }

    &__header-title {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }

    &__topbar {
      padding: 0 30rpx;
      font-size: 24rpx;
      color: #696969;
    }

    &__content {
      flex-grow: 1;
      background-color: #fff;
    }

    &__footer {
      padding: 10rpx 30rpx;
    }

    &__safe-area {
      height: var(--safe-area-inset-bottom);
      background-color: #fff;
    }
  }
</style>

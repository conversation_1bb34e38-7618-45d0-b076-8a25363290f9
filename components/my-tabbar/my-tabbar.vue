<template>
  <wd-tabbar
    v-model="tName"
    @change="onTabChange"
    active-color="#C6A670"
    inactive-color="#666666"
    fixed
    safeAreaInsetBottom
  >
    <wd-tabbar-item
      v-for="(item, index) in tabBar"
      :key="index"
      :name="item.name"
      activeColor="#C6A670"
      inactiveColor="#666666"
      :icon="item.name"
      :title="item.text"
    ></wd-tabbar-item>
  </wd-tabbar>
</template>

<script>
  export default {
    name: "my-tabbar",
    props: {
      tabName: {
        type: String,
        default: "home"
      },
      tabBar: {
        type: Array,
        default() {
          return [
            {
              text: "首页",
              name: "home",
              path: "/pages/home/<USER>"
            },
            {
              text: "产品",
              name: "goods",
              path: "/pages/product/index"
            },
            {
              text: "购物车",
              name: "cart",
              path: "/pages/shoppingCart/index"
            },
            {
              text: "个人中心",
              name: "user",
              path: "/pages/personalCenter/index"
            }
          ]
        }
      }
    },
    emits: ["update:tabName"],
    created() {
      uni.hideTabBar({})
    },
    mounted() {
      // 延迟获取高度，确保样式已完全应用
      this.$nextTick(() => {
        this.initSize()
      })
    },
    computed: {
      tName: {
        get() {
          return this.tabName
        },
        set(value) {
          this.$emit("update:tabName", value)
        }
      }
    },
    methods: {
      initSize() {
        const _safeAreaBottom = uni.getSystemInfoSync().safeAreaInsets?.bottom || 0
        const _safeAreaTop = uni.getSystemInfoSync().safeAreaInsets?.top || 0
        const _tabbarHeight = 50 + _safeAreaBottom
        uni.setStorageSync("tabBarHeight", _tabbarHeight)
        uni.setStorageSync("safeAreaBottom", _safeAreaBottom)
        uni.setStorageSync("safeAreaTop", _safeAreaTop)
      },
      /**
       * 监听 tab 切换事件并执行跳转
       */
      onTabChange({ value }) {
        const targetTab = this.tabBar.find((item) => item.name === value)
        // 确保目标 tab 存在且路径有效
        if (targetTab && targetTab.path) {
          uni.switchTab({
            url: targetTab.path
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped></style>

<template>
  <view class="order-card">
    <view class="order-title">订单信息</view>
    <view v-for="item in oInfo" :key="item.label" class="order-item">
      <text class="cell-label">{{ item.label }}:</text>
      <text class="cell-value">{{ item.value }}</text>
      <view class="copy-block" v-if="item.label === '订单号'" @tap="() => copyOrderNo(item.value)">复制</view>
    </view>
  </view>
</template>

<script>
  const ITEM_NAME = {
    orderNo: "订单号",
    orderTime: "下单时间",
    deliveryType: "配送方式",
    remark: "备注",
    payType: "支付方式"
  }

  export function formatOrderInfo(orderInfo) {
    return Object.keys(orderInfo).map((key) => ({
      label: ITEM_NAME[key] || key,
      value: orderInfo[key]
    }))
  }

  export default {
    name: "OrderCard",
    props: {
      orderInfo: {
        type: Object,
        default: () => ({
          orderNo: "35126666778899",
          orderTime: "2024-09-04 10:10:10",
          deliveryType: "快递运输",
          remark: "发货时请选择顺丰",
          payType: "微信支付"
        })
      }
    },
    data() {
      return {
        orderName: ITEM_NAME,
        oInfo: formatOrderInfo(this.orderInfo)
      }
    },
    methods: {
      copyOrderNo(orderNo) {
        if (!orderNo) return
        uni.setClipboardData({
          data: orderNo,
          success: () => {
            uni.showToast({
              title: "复制成功",
              icon: "none"
            })
          }
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .order-card {
    display: flex;
    flex-direction: column;
    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 30rpx;

    .order-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 24rpx;
    }

    .order-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      color: #666666;
      font-size: 28rpx;

      .cell-label {
        margin-right: 10rpx;
        min-width: 120rpx; /* 固定宽度，确保对齐 */
      }

      .copy-block {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rpx 20rpx;
        background-color: #f0f0f0;
        border-radius: 30rpx;
        border: 1px solid #dcdcdc;
        margin-left: 20rpx;

        font-size: 24rpx;
      }
    }
  }
</style>

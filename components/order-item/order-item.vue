<template>
	<view class="order-card">
		<!-- 店铺信息 -->
		<!-- <view class="store-info" v-if="verifyShowAmount(type)"> -->
		<view class="store-info" v-if="type !== 'applying' && type !== 'processing'">
			<view class="store-title-bar">
				<!-- <image class="icon-store" src="/static/order/ic-order-title.png" /> -->
				<text class="store-name">{{ order.accountUnit || '无' }}</text>
			</view>
			<text class="order-status" v-if="order.orderStatusName">{{ order.orderStatusName }}</text>
		</view>
		<view class="refund-info" v-else>
			<view class="refund-title"> 售后单号:{{ order.afterSaleNo }} </view>
			<view class="refund-mark" v-if="order.afterSaleType === '1'">
				<image src="/static/order/ic-return.png" mode="aspectFill" class="mark-icon" />
				<text class="mark-tips">退货</text>
			</view>

			<view class="refund-mark" v-if="order.afterSaleType === '2'">
				<image src="/static/order/ic-exchange.png" mode="aspectFill" class="mark-icon" />
				<text class="mark-tips mark-exchange">换货</text>
			</view>
		</view>
		
		<view class="product-list" v-if="type === 'applying' || type === 'processing'">
			<view class="product-item">
				<view class="product-content">
					<view class="product-img-block">
						<image :src="order.image" mode="aspectFill" class="product-image"></image>
					</view>
					<view class="product-info">
						<text class="product-name">{{ order.productName }}</text>
						<view class="product-price-block">
							<text class="product-price">¥{{ order.refundPrice }}</text>
							<text class="product-quantity">X {{ order.asNum || 1 }}</text>
						</view>
					</view>
				</view>
				<!-- <view class="gifts" v-if="item.gifts.length">
					<view class="gifts-tips"> 【赠品】 </view>
					<view class="gifts-content">
						<view v-for="gift in item.gifts" :key="gift.name" class="gifts-item">
							<view class="item-content">
								<image src="/static/order/pic-gift-01.png" mode="aspectFill" class="gifts-img"></image>
								<text class="grid-title">{{ gift.name }}</text>
							</view>
							<text class="grid-quantity">X{{ gift.quantity }}</text>
						</view>
					</view>
				</view> -->
			</view>
		
			<view v-if="showBottomTips" class="product-bottom-bar">
				<image src="/static/order/ic-quality.png" mode="aspectFill" class="quality-icon"></image>
				<text class="quality-tips">.品牌授权官方授权货源</text>
				<text class="quality-tips">.48小时发货</text>
				<text class="quality-tips">.7天无理由退货</text>
			</view>
		</view>

		<!-- 商品列表 -->
		<view class="product-list" v-if="type !== 'applying' && type !== 'processing'">
			<view v-for="item in order.items" :key="item.productId" class="product-item" @tap="onShowDetail(item)">
				<view class="product-content">
					<view class="product-img-block">
						<image :src="item.image" mode="aspectFill" class="product-image"></image>
					</view>
					<view class="product-info">
						<text class="product-name">{{ item.productName }}</text>
						<!-- <text class="product-specs">{{ formatSpecs(item.specs) }}</text> -->
						<view class="product-price-block">
							<text class="product-price">¥{{ item?.attrValues[0]?.insidePrice }}</text>
							<text class="product-quantity">X {{ item.quantity || 1 }}</text>
						</view>
					</view>
				</view>
				<view class="gifts" v-if="item.gifts.length">
					<view class="gifts-tips"> 【赠品】 </view>
					<view class="gifts-content">
						<view v-for="gift in item.gifts" :key="gift.name" class="gifts-item">
							<view class="item-content">
								<image src="/static/order/pic-gift-01.png" mode="aspectFill" class="gifts-img"></image>
								<text class="grid-title">{{ gift.name }}</text>
							</view>
							<text class="grid-quantity">X{{ gift.quantity }}</text>
						</view>
					</view>
				</view>
			</view>

			<view v-if="showBottomTips" class="product-bottom-bar">
				<image src="/static/order/ic-quality.png" mode="aspectFill" class="quality-icon"></image>
				<text class="quality-tips">.品牌授权官方授权货源</text>
				<text class="quality-tips">.48小时发货</text>
				<text class="quality-tips">.7天无理由退货</text>
			</view>
		</view>
		
		<view class="product-list" v-if="order.orderDetails">
			<view v-for="item in order.orderDetails" :key="item.productId" class="product-item" @tap="onShowDetail(item)">
				<view class="product-content">
					<view class="product-img-block">
						<image :src="item.image" mode="aspectFill" class="product-image"></image>
					</view>
					<view class="product-info">
						<text class="product-name">{{ item.productName }}</text>
						<view class="product-price-block">
							<text class="product-price">¥{{ item.price }}</text>
							<text class="product-quantity">X {{ item.quantity || 1 }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 订单合计 -->
		<view class="order-summary" v-if="(type !== 'applying' && type !== 'processing') && showAmount && verifyShowAmount(type)">
			<text class="total-num">共{{ order.totalItems || order.asNum }}件商品</text>
			<text class="total-price">{{ totalAmountTips(type) }} ¥{{ order.totalAmount }}</text>
		</view>

		<!-- 代付款 -->
		<view class="actions" v-if="type === 'payment' && showActionBar">
			<view class="btn" @tap="cancelOrder">取消订单</view>
			<view class="btn" @tap="modifyAddress">修改地址</view>
			<view class="btn primary" @tap="payNow">立即支付</view>
		</view>

		<!-- 代发货 -->
		<view class="actions" v-if="type === 'delivery' && showActionBar">
			<view class="btn" @tap="buyAgain">再来一单</view>
			<view class="btn" @tap="applyRefund">申请退款</view>
			<view class="btn" @tap="editAddress">修改地址</view>
		</view>

		<!-- 待收货 -->
		<view class="actions" v-if="type === 'received' && showActionBar">
			<view class="btn" @tap="buyAgain">再来一单</view>
			<!-- <view class="btn" @tap="refund">退款/售后</view> -->
			<view class="btn" @tap="applyRefund">退款/售后</view>
			<view class="btn primary" @tap="confirmGet">确认收货</view>
		</view>

		<!-- 待开票 -->
		<!-- <view class="actions" v-if="type === 'invoice' && showActionBar">
			<view class="btn" @tap="applyInvoice">申请开票</view>
			<view class="btn" @tap="buyAgain">再来一单</view>
			<view class="btn" @tap="refund">退款/售后</view>
		</view> -->
		<view class="actions" v-if="type === 'completed' && order.orderType === '0' && showActionBar">
			<view class="btn" @tap="applyInvoice">申请开票</view>
			<view class="btn" @tap="buyAgain">再来一单</view>
			<view class="btn" @tap="refund">退款/售后</view>
		</view>

		<!-- 已完成 -->
		<view class="actions" v-if="type === 'completed' && order.orderType !== '0'  && showActionBar">
			<view class="btn" @tap="showInvoice">查看发票</view>
			<view class="btn" @tap="buyAgain">再来一单</view>
			<view class="btn" @tap="refund">退款/售后</view>
		</view>

		<!-- 已取消 -->
		<view class="actions" v-if="type === 'cancelled' && showActionBar">
			<view class="btn" @tap="deleteOrder">删除订单</view>
			<view class="btn" @tap="buyAgain">再来一单</view>
		</view>

		<!-- 售后申请  -->
		<view class="actions" v-if="type === 'applying' && showActionBar">
			<!-- <view class="btn" @tap="refund">退款/售后</view> -->
		</view>

		<!-- 处理中 -->
		<view class="actions" v-if="type === 'processing' && showActionBar">
			<view class="btn" @tap="editTracking">填写快递单号</view>
			<view class="btn" @tap="cancelApply">取消申请</view>
		</view>

		<view class="actions" v-if="type === 'record' && showActionBar">
			<view class="btn" @tap="_delete">删除</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "OrderItem",
		emits: [
			"onShowDetail",
			"onCancelOrder",
			"onModifyAddress",
			"onPayNow",
			"onBuyAgain",
			"onDeleteOrder",
			"onApplyRefund",
			"onEditAddress",
			"onRefund",
			"onConfirmGet",
			"onApplyInvoice",
			"onShowInvoice",
			"onEditTracking",
			"onCancelApply",
			"onDelete"
		],
		props: {
			order: {
				type: Object,
				required: true,
				default: () => ({
					seller: {
						name: ""
					},
					status: "",
					orderTime: "",
					orderId: "",
					refundId: "",
					refundType: "",
					items: [],
					totalItems: 1,
					totalAmount: 100
				})
			},
			showStatus: {
				type: Boolean,
				default: true
			},
			showAmount: {
				type: Boolean,
				default: true
			},
			showActionBar: {
				type: Boolean,
				default: true
			},
			showBottomTips: {
				type: Boolean,
				default: false
			},
			type: {
				type: String,
				default: "all"
			}
		},
		data() {
			return {
				orderStatus: {
					payment: "待付款",
					delivery: "待发货",
					received: "待收货",
					invoice: "待开票",
					completed: "已完成",
					cancelled: "已取消",
					applying: "售后申请"
				}
			}
		},

		methods: {
			verifyShowAmount(type) {
				if (type) {
					return Object.keys(this.orderStatus).some((key) => {
						return key === type
					})
				}
			},

			totalAmountTips(type) {
				switch (type) {
					case "payment":
					case "cancelled":
						return "待付款"
					case "delivery":
					case "received":
					case "invoice":
					case "completed":
						return "实付"
					default:
						return ""
				}
			},

			onShowDetail(item) {
				console.log("onShowDetail item >>", item)
				this.$emit("onShowDetail", item)
			},

			formatSpecs(specs) {
				return Object.values(specs).join(" | ")
			},
			cancelOrder() {
				this.$emit("onCancelOrder")
			},
			modifyAddress() {
				this.$emit("onModifyAddress")
			},
			payNow() {
				this.$emit("onPayNow")
			},
			buyAgain() {
				this.$emit("onBuyAgain")
			},
			deleteOrder() {
				this.$emit("onDeleteOrder")
			},
			applyRefund() {
				this.$emit("onApplyRefund")
			},
			editAddress() {
				this.$emit("onEditAddress")
			},
			refund() {
				this.$emit("onRefund")
			},
			confirmGet() {
				this.$emit("onConfirmGet")
			},
			applyInvoice() {
				this.$emit("onApplyInvoice")
			},
			showInvoice() {
				this.$emit("onShowInvoice")
			},
			editTracking() {
				this.$emit("onEditTracking")
			},
			cancelApply() {
				this.$emit("onCancelApply")
			},
			_delete() {
				this.$emit("onDelete")
			}
		}
	}
</script>

<style lang="scss" scoped>
	$order-border-color: #f0f0f0;
	$order-btn-color: #c6a670;

	.order-card {
		background: #fff;
		border-radius: 20rpx;
		padding: 20rpx 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	// 店铺信息
	.store-info {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 24rpx;

		.store-title-bar {
			display: flex;
			align-items: center;
			font-size: 32rpx;
			font-weight: bold;
			flex: 1;

			.icon-store {
				margin-right: 16rpx;
				width: 40rpx;
				height: 40rpx;
			}

			.store-name {
				color: #333;
			}
		}

		.order-status {
			margin-left: auto;
			font-size: 28rpx;
		}
	}

	.refund-info {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.refund-title {
			display: flex;
			align-items: center;
			font-size: 32rpx;
			color: #333333;
			flex: 1;
		}

		.refund-mark {
			display: flex;
			align-items: center;
			justify-content: flex-end;

			.mark-icon {
				width: 40rpx;
				height: 40rpx;
			}

			.mark-tips {
				font-size: 28rpx;
				color: #e02d2d;
				margin-left: 10rpx;
			}

			.mark-exchange {
				color: $order-btn-color;
			}
		}
	}

	// 商品列表
	.product-list {
		display: flex;
		flex-direction: column;
		padding-top: 10rpx;

		.product-bottom-bar {
			display: flex;
			align-items: center;
			justify-content: space-around;
			margin-top: 10rpx;

			.quality-icon {
				width: 36rpx;
				height: 36rpx;
			}

			.quality-tips {
				font-size: 22rpx;
				color: #c6a772;
				margin: 0 5rpx;
			}
		}
	}

	.product-item {
		display: flex;
		flex-direction: column;
		padding: 20rpx 0;

		.product-content {
			display: flex;

			.product-img-block {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 220rpx;
				height: 220rpx;
				border-radius: 20rpx;
				margin-right: 20rpx;
				background-color: rgba(250, 250, 250, 1);
				box-shadow: 0 4rpx 12rpx 0 rgba(206, 206, 206, 1);

				.product-image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 20rpx;
				}
			}

			.product-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.product-name {
					font-size: 28rpx;
					font-weight: bold;
				}

				.product-specs {
					font-size: 24rpx;
					color: #666;
					margin-top: 10rpx;
				}

				.product-price-block {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.product-price,
					.product-quantity {
						font-size: 24rpx;
						color: #666;
						margin-top: 10rpx;
					}

					.product-price {
						color: #e02d2d;
						font-weight: bold;
					}
				}
			}
		}

		.gifts {
			display: flex;
			margin: 40rpx 0 20rpx;
			border-bottom-color: $order-border-color;
			border-bottom-width: 1rpx;

			.gifts-tips {
				font-size: 24rpx;
				color: #666;
			}

			.gifts-content {
				display: flex;
				flex-direction: column;
				flex: 1;

				.gifts-item {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.item-content {
						display: flex;
						align-items: center;
						flex: 1;

						.gifts-img {
							width: 40rpx;
							height: 40rpx;
							border-radius: 20rpx;
							margin-right: 20rpx;
						}

						.grid-title {
							font-size: 24rpx;
							color: #666;
						}
					}

					.grid-quantity {
						font-size: 24rpx;
						color: #666;
					}
				}
			}
		}
	}

	// 订单合计
	.order-summary {
		display: flex;
		justify-content: flex-end;
		padding: 20rpx 0;
		font-size: 28rpx;
		font-weight: bold;

		.total-num {
			color: #666;
			margin-right: 40rpx;
		}

		.total-price {
			color: #e02d2d;
		}
	}

	// 按钮操作
	.actions {
		display: flex;
		justify-content: flex-end;
		gap: 20rpx;
		margin-top: 20rpx;

		.btn {
			padding: 10rpx 20rpx;
			border-radius: 30rpx;
			font-size: 28rpx;
			border: 2rpx solid #ddd;
			background: #fff;
			color: #333;

			&.primary {
				background: $order-btn-color;
				color: #fff;
				border: none;
			}
		}
	}
</style>
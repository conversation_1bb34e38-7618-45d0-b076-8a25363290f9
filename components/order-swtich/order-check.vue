<template>
  <view class="order-card">
    <!-- 店铺信息 -->
    <view class="store-info">
      <view class="store-title-bar">
        <image class="icon-store" src="/static/order/ic-order-title.png" />
        <text class="store-name">{{ order.seller?.name }}</text>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="product-list">
      <view v-for="item in order.items" :key="item.productId" class="product-wrapper">
        <view class="item-check-box">
          <wd-checkbox v-model="item.checked" @change="onCheckChange"></wd-checkbox>
        </view>
        <view class="product-item">
          <view class="product-content">
            <view class="product-img-block">
              <image src="/static/order/pic-product-01.png" mode="aspectFill" class="product-image"></image>
            </view>
            <view class="product-info">
              <text class="product-name">{{ item.name }}</text>
              <text class="product-specs">{{ formatSpecs(item.specs) }}</text>
              <view class="product-price-block">
                <text class="product-price">¥{{ item.price }}</text>
                <text class="product-quantity">X {{ item.quantity }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "OrderSwitch",
    emits: ["refundItemCheck"],
    props: {
      order: {
        type: Object,
        required: true,
        default: () => ({
          seller: {
            name: ""
          },
          status: "",
          orderTime: "",
          orderId: "",
          refundId: "",
          refundType: "",
          items: [
            {
              productId: 1,
              name: "",
              specs: {
                color: "",
                size: ""
              },
              price: 0,
              quantity: 1,
              gifts: []
            }
          ],
          totalItems: 1,
          totalAmount: 100
        })
      }
    },

    methods: {
      formatSpecs(specs) {
        return Object.values(specs).join(" | ")
      },

      onCheckChange({ value }) {
        console.log("refundItemCheck value", value)
        // 处理复选框状态变化
        this.$emit("refundItemCheck", value)
      }
    }
  }
</script>

<style lang="scss" scoped>
  $order-border-color: #f0f0f0;
  $order-btn-color: #c6a670;

  .order-card {
    background: #fff;
    border-radius: 20rpx;
    padding: 20rpx 30rpx;
  }

  // 店铺信息
  .store-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 24rpx;

    .store-title-bar {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      font-weight: bold;
      flex: 1;

      .icon-store {
        margin-right: 16rpx;
        width: 40rpx;
        height: 40rpx;
      }

      .store-name {
        color: #333;
      }
    }

    .order-status {
      margin-left: auto;
      font-size: 28rpx;
    }
  }

  // 商品列表
  .product-list {
    padding-top: 10rpx;
  }

  .product-wrapper {
    display: flex;
    align-items: center;

    .item-check-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
    }

    .product-item {
      display: flex;
      flex-direction: column;
      padding: 20rpx 0;

      .product-content {
        display: flex;

        .product-img-block {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 220rpx;
          height: 220rpx;
          border-radius: 20rpx;
          margin-right: 20rpx;
          background-color: rgba(250, 250, 250, 1);
          box-shadow: 0 4rpx 12rpx 0 rgba(206, 206, 206, 1);

          .product-image {
            width: 160rpx;
            height: 160rpx;
            border-radius: 20rpx;
          }
        }

        .product-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .product-name {
            font-size: 28rpx;
            font-weight: bold;
          }

          .product-specs {
            font-size: 24rpx;
            color: #666;
            margin-top: 10rpx;
          }

          .product-price-block {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .product-price,
            .product-quantity {
              font-size: 24rpx;
              color: #666;
              margin-top: 10rpx;
            }

            .product-price {
              color: #e02d2d;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
</style>

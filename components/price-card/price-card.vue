<template>
  <view class="price-card">
    <view v-for="item in pInfo" :key="item.label" class="price-item">
      <text class="cell-label">{{ item.label }}:</text>
      <text class="cell-value">{{ item.value }}</text>
    </view>
    <view class="price-total">
      <view class="tips">合计:</view>
      <view class="price-value">{{ totalPay }}</view>
    </view>
  </view>
</template>

<script>
  const ITEM_NAME = {
    totalPrice: "商品总价",
    chargesPrice: "运费",
    discountCode: "优惠码",
    discountPrice: "优惠金额"
  }

  export function formatOrderInfo(orderInfo) {
    return Object.keys(orderInfo).map((key) => ({
      label: ITEM_NAME[key] || key,
      value: orderInfo[key]
    }))
  }

  export default {
    name: "PriceCard",
    props: {
      priceInfo: {
        type: Object,
        default: () => ({
          totalPrice: "¥100.00",
          chargesPrice: "¥10.00",
          discountCode: "DISCOUNT2024",
          discountPrice: "¥5.00"
        })
      },
      totalPay: {
        type: String,
        default: "¥105.00"
      }
    },
    data() {
      return {
        itemName: ITEM_NAME,
        pInfo: formatOrderInfo(this.priceInfo)
      }
    }
  }
</script>

<style scoped lang="scss">
  .price-card {
    display: flex;
    flex-direction: column;
    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 30rpx;

    .price-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;

      color: #666666;
      font-size: 28rpx;
    }

    .price-total {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 20rpx;
      height: 100rpx;
      border-top: 1px solid #e0e0e0;

      .tips {
        margin-right: 20rpx;
        color: #333;
        font-size: 28rpx;
        font-weight: bold;
      }

      .price-value {
        width: 120rpx;
        text-align: right;
        font-weight: bold;
        color: #ff5722; /* 使用主题色 */
        font-size: 28rpx;

        @media (min-width: 768px) {
          width: 200rpx; /* 在大屏幕上增加宽度 */
        }
      }
    }
  }
</style>

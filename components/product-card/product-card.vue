<template>
  <view class="product-card" @tap="onAction">
    <image class="product-img" :src="product.image" mode="aspectFit" />
    <view class="product-title">{{ product.name }}</view>
    <view class="product-desc">{{ product.description || '无' }}</view>
    <view class="product-price-row">
      <text class="product-price">¥{{ product.insidePrice }}</text>
      <text v-if="product.externalPrice" class="product-origin-price">¥{{ product.externalPrice }}</text>
    </view>
  </view>
</template>

<script>
  export default {
    name: "ProductCard",
    emits: ["showDetail"],
    props: {
      product: {
        type: Object,
        required: true,
        default: () => ({
          imageUrl: "",
          title: "",
          description: "",
          price: 0,
          originalPrice: null
        })
      }
    },
    data() {
      return {}
    },
    methods: {
      // 可以添加一些方法来处理点击事件等
      onAction() {
        this.$emit("showDetail", this.product)
      }
    }
  }
</script>

<style scoped lang="scss">
  .product-card {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;

    .product-img {
      width: 100%;
      height: 200rpx;
      margin-bottom: 32rpx;
      border-radius: 16rpx;
    }

    .product-title {
      font-weight: bold;
      font-size: 32rpx;
      line-height: 40rpx;
      margin-bottom: 12rpx;
      text-align: left;
      width: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      min-height: 80rpx;
    }

    .product-desc {
      color: #999;
      font-size: 24rpx;
      margin-bottom: 32rpx;
      width: 100%;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .product-price-row {
      display: flex;
      align-items: baseline;
      width: 100%;

      .product-price {
        color: #111;
        font-size: 36rpx;
        font-weight: bold;
        margin-right: 16rpx;
      }

      .product-origin-price {
        color: #bbb;
        font-size: 28rpx;
        text-decoration: line-through;
      }
    }
  }
</style>

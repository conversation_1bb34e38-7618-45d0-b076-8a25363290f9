<template>
  <view class="product-item" @tap="onShowDetail">
    <view class="product-image">
      <image :src="productInfo.image" mode="aspectFill" class="image"></image>
    </view>
    <view class="product-info">
      <view class="product-title">{{ productInfo.productName }}</view>
      <view class="product-spec">{{ productInfo.spec || '' }}</view>
      <view class="product-bottom">
        <text class="quantity">数量 x{{ productInfo.num }}</text>
        <text class="price">¥{{ productInfo.price }}</text>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "ProductItem",
    props: {
      productInfo: {
        type: Object,
        default: () => ({
          image: "",
          title: "",
          spec: "",
          quantity: 1,
          price: 0
        })
      }
    },
    methods: {
      onShowDetail() {
        uni.navigateTo({
          url: `/packages/shop/info?productId=${this.productInfo.id || ""}`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  // 1. 将 mixin 定义移到顶部
  @mixin text-ellipsis($line: 1) {
    @if $line == 1 {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    } @else {
      display: -webkit-box;
      -webkit-line-clamp: $line;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .product-item {
    display: flex;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 20rpx;

    .product-image {
      width: 200rpx;
      height: 160rpx;
      margin-right: 20rpx;
      flex-shrink: 0;

      .image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }
    }

    .product-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      overflow: hidden;

      .product-title {
        font-size: 28rpx;
        color: #333;
        line-height: 1.4;
        // 2. 现在可以正常使用
        @include text-ellipsis(2);
      }

      .product-spec {
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
        @include text-ellipsis(1);
      }

      .product-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;

        .quantity {
          font-size: 24rpx;
          color: #999;
        }

        .price {
          font-size: 32rpx;
          color: #ee0a24;
          font-weight: 500;
        }
      }
    }
  }
</style>

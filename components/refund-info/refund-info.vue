<template>
  <view class="refund-info">
    <view class="refund-info__title_bar">
      {{ title }}
    </view>
    <view class="refund-info__content">
      <view class="refund-info__content__info-item">
        <view class="method">{{ paymentMethod }}:</view>
        <view class="amount">¥{{ amount }}</view>
      </view>
      <view v-if="readyTime" class="refund-info__content__info-description">
        <view class="tips">{{ readyTime }} 平台已退款到您的微信账户中，您可在微信查看，如有疑问可以咨询微信客服。</view>
        <view v-if="bankAccount" class="bank-info"> {{ bankAccount }}: ¥{{ amount }} </view>
      </view>
      <view v-else class="refund-info__content__info-description">
        <view class="tips">{{ estimatedTime }}</view>
      </view>

      <view v-if="status === 'success'" class="success-message">
        <text>您的退款申请已成功处理。</text>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "RefundInfo",
    props: {
      // 状态: processing(处理中), success(成功)
      status: {
        type: String,
        default: "processing",
        validator: (value) => ["processing", "success"].includes(value)
      },
      title: {
        type: String,
        default: "退款信息"
      },
      // 支付方式
      paymentMethod: {
        type: String,
        default: "微信支付"
      },
      // 退款金额
      amount: {
        type: [String, Number],
        default: null,
        required: true
      },
      // 预计到账时间
      estimatedTime: {
        type: String,
        default: "预计1-3个工作日到账"
      },
      readyTime: {
        type: String,
        default: ""
      },
      bankAccount: {
        type: String,
        default: "北京银行借记卡4321"
      }
    },
    methods: {}
  }
</script>

<style lang="scss">
  .refund-info {
    display: flex;
    flex-direction: column;
    margin: 30rpx 0;

    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;

    &__title_bar {
      font-size: 32rpx;
      font-weight: bold;
      padding-bottom: 20rpx;
      color: #333;
      border-bottom: 1px solid #e6e6e6;
    }

    &__content {
      display: flex;
      flex-direction: column;
      padding: 20rpx 0;

      &__info-item {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        font-size: 32rpx;

        .method {
          color: #333;
          margin-right: 10rpx;
        }

        .amount {
          color: #333;
        }
      }

      &__info-description {
        display: flex;
        flex-direction: column;
        margin-top: 12rpx;

        .tips {
          color: #666;
          font-size: 24rpx;
        }

        .bank-info {
          display: flex;
          align-items: center;
          background-color: rgba(0, 0, 0, 0.05);
          padding: 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          color: #333;
          margin-top: 20rpx;
        }

        .refund-amount {
          margin-top: 8rpx;
          font-size: 16px;
        }

        .estimated-time {
          margin-top: 4px;
          font-size: 14px;
          color: #999;
        }
      }
    }
  }
</style>

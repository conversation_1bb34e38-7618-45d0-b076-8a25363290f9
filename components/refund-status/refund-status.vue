<template>
  <view class="refund-status">
    <view class="status-line">
      <view class="status-label">{{ statusLabel }}: </view>
      <view class="status-value">{{ statusMap[statusValue] || "-" }}</view>
    </view>
    <view class="order-line">
      <view class="order-label">订单号: </view>
      <view class="order-value">{{ orderNumber }}</view>
    </view>
  </view>
</template>

<script>
  const STATUS_VALUE = {
    processing: "处理中",
    success: "已完成",
    failed: "已失败",
    cancelled: "已取消"
  }

  export default {
    name: "RefundStatus",
    props: {
      // 状态标签，如"取消进度"
      statusLabel: {
        type: String,
        default: "取消进度"
      },
      // 状态值，如"已完成"
      statusValue: {
        type: String,
        required: true
      },
      // 订单号
      orderNumber: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        // 映射状态值到中文描述
        statusMap: STATUS_VALUE
      }
    }
  }
</script>

<style scoped lang="scss">
  .refund-status {
    display: flex;
    flex-direction: column;

    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
    line-height: 1.5;

    .status-line,
    .order-line {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .status-label,
    .order-label {
      color: #666;
      font-weight: 400;
      margin-right: 8px;
      width: 120rpx; /* 固定宽度，确保对齐 */
    }

    .status-value,
    .order-value {
      font-weight: 500;
    }
  }
</style>

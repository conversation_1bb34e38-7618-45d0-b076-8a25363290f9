<template>
  <view class="steps__wrapper">
    <wd-steps :active="active" vertical dot>
      <wd-step
        v-for="(step, index) in steps"
        :key="index"
        :title="step.title"
        :description="step.description"
        :status="step.status"
      />
    </wd-steps>
  </view>
</template>

<script>
  export default {
    name: "RefundStep",
    props: {
      active: {
        type: Number,
        default: 0
      },
      steps: {
        type: Array,
        default: () => [
          {
            title: "",
            description: "",
            status: ""
          }
        ],
        validator: (value) => {
          return value.every((step) => {
            return typeof step === "object" && "description" in step
          })
        }
      }
    },
    emits: ["update:active"]
  }
</script>

<style scoped lang="scss">
  .steps__wrapper {
    display: flex;
    flex-direction: column;

    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 8px;
  }
</style>

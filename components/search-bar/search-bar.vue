<template>
  <view class="search-bar" @click="search">
    <view class="search-bar__input">
      <wd-icon v-if="icon" :name="icon" size="18px" color="#969696"></wd-icon>
      <text class="search-bar__input-text">{{ placeholder }}</text>
    </view>
    <view v-if="searchTip">
      <text class="search-bar__tip">{{ searchTip }}</text>
    </view>
  </view>
</template>

<script>
  export default {
    name: "search-bar",
    emits: ["search"],
    props: {
      placeholder: {
        type: String,
        default: "请输入搜索内容"
      },
      icon: {
        type: String,
        default: "search"
      },
      searchTip: {
        type: String,
        default: ""
      },
      searchType: {
        type: String,
        default: "goods" // 可选值：goods, order
      }
    },
    methods: {
      search() {
        console.log("搜索类型:", this.searchType)
        // this.$emit("search", this.searchType)
        return uni.navigateTo({
          url: "/packages/search/index?type=" + this.searchType
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .search-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    margin: 20rpx 0;
    min-height: 80rpx;
    background-color: transparent;

    .search-bar__input {
      display: flex;
      align-items: center;
      background-color: rgba(244, 246, 249, 1);
      border-radius: 40rpx;
      padding: 0 20rpx;
      flex: 1;
      height: 100%;

      .search-bar__input-text {
        color: #969696;
        font-size: 28rpx;
        line-height: 80rpx;
        margin-left: 10rpx;
      }
    }

    .search-bar__tip {
      color: #999999;
      font-size: 28rpx;
      line-height: 80rpx;
      margin-left: 20rpx;
    }
  }
</style>

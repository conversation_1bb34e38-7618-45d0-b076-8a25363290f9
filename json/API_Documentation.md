# API 接口文档

## 概述

本文档提供了完整的 API 接口说明，包括商品管理、订单管理、购物车管理、售后服务管理等功能模块的详细接口信息。

## 目录

- [商品管理](#商品管理)
- [订单管理](#订单管理)
- [购物车管理](#购物车管理)
- [售后服务管理](#售后服务管理)
- [用户管理](#用户管理)
- [系统管理](#系统管理)

## 通用说明

### 认证方式
所有接口都需要在请求头中包含 `Authorization` 字段，值为有效的访问令牌。

```http
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
```

### 响应格式
所有接口返回的数据格式为 JSON，基本结构如下：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 分页参数
支持分页的接口通常包含以下参数：
- `pageNum`: 页码，从1开始
- `pageSize`: 每页记录数，默认10

---

## 商品管理

### 1. 查询商品APP列表

**接口地址**: `GET /store/product/appList`

**功能描述**: 根据筛选条件分页获取商品列表，支持按价格、品牌、分类等条件筛选，支持排序

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| ascOrDesc | string | 否 | 排序方式，asc为升序，desc为降序 | desc |
| brandId | integer | 否 | 品牌ID，用于筛选指定品牌的商品 | 1 |
| cateId | string | 否 | 分类ID，用于筛选指定分类的商品 | 1 |
| isHot | string | 否 | 是否热卖商品，1为热卖，0为非热卖 | 1 |
| isNew | string | 否 | 是否新品，1为新品，0为非新品 | 1 |
| maxPrice | number | 否 | 最高价格，用于价格区间筛选 | 999.99 |
| minPrice | number | 否 | 最低价格，用于价格区间筛选 | 10.00 |
| searchValue | string | 否 | 搜索关键词，用于商品名称或描述的模糊搜索 | 手机 |
| sortField | string | 否 | 排序字段，如price（价格）、createTime（创建时间）等 | price |

**请求示例**:
```http
GET /store/product/appList?isNew=1&minPrice=100&maxPrice=500&sortField=price&ascOrDesc=asc
```

**使用场景**:
- 首页展示上新商品
- 热卖商品查询
- 商品搜索功能
- 分类商品展示

### 2. 获取商品详细信息

**接口地址**: `GET /store/product/getInfo`

**功能描述**: 根据商品ID获取商品的详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| productId | integer | 是 | 商品ID，用于标识特定商品 | 1 |

**请求示例**:
```http
GET /store/product/getInfo?productId=123
```

**使用场景**:
- 商品详情页展示
- 购物车商品信息获取

### 3. 查询商品分类列表

**接口地址**: `GET /store/productCate/list`

**功能描述**: 获取商品分类列表

**使用场景**:
- 商品分类页面
- 分类筛选功能

---

## 购物车管理

### 1. 查询购物车列表

**接口地址**: `GET /store/cart/list`

**功能描述**: 根据用户ID分页获取用户的购物车列表

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| userId | integer | 是 | 用户ID，用于标识特定用户 | 1 |

**请求示例**:
```http
GET /store/cart/list?userId=123
```

### 2. 新增购物车记录

**接口地址**: `POST /store/cart/add`

**功能描述**: 将指定商品及其属性、数量添加到用户购物车中

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| productId | integer | 是 | 商品ID，用于标识特定商品 | 1 |
| productAttrValueId | integer | 是 | 商品属性值ID，用于确定商品的具体规格属性 | 1 |
| num | integer | 是 | 数量，指定操作的商品数量 | 1 |

**请求示例**:
```http
POST /store/cart/add?productId=123&productAttrValueId=456&num=2
```

### 3. 删除购物车记录

**接口地址**: `POST /store/cart/remove`

**功能描述**: 根据传入的购物车ID数组，删除对应的购物车记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| cartIds | integer | 是 | 购物车ID数组，用于批量操作购物车记录 | 1 |

**请求示例**:
```http
POST /store/cart/remove?cartIds=123,456,789
```

---

## 订单管理

### 1. 查询订单列表（APP端）

**接口地址**: `GET /store/order/appList`

**功能描述**: 根据状态等条件分页获取APP端的订单列表

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| status | string | 否 | 状态值，用于筛选对应状态的记录 | 1 |

**请求示例**:
```http
GET /store/order/appList?status=1
```

### 2. 下订单

**接口地址**: `POST /store/order/placeOrder`

**功能描述**: 创建新订单

**使用场景**:
- 确认订单页面提交订单

### 3. 取消订单

**接口地址**: `POST /store/order/cancle`

**功能描述**: 取消指定的订单

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| orderId | integer | 是 | 订单ID，用于标识特定订单 | 1 |

### 4. 获取订单详细信息

**接口地址**: `GET /store/order/getInfo`

**功能描述**: 根据订单ID获取订单的详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| orderId | integer | 是 | 订单ID，用于标识特定订单 | 1 |

### 5. 修改收货地址

**接口地址**: `POST /store/order/updateUserAddress`

**功能描述**: 修改订单的收货地址信息

### 6. 删除订单

**接口地址**: `POST /store/order/remove`

**功能描述**: 删除指定的订单记录

### 7. 售后服务订单列表

**接口地址**: `GET /store/order/asServiceList`

**功能描述**: 获取可申请售后服务的订单列表

---

## 售后服务管理

### 1. 查询APP端售后服务列表

**接口地址**: `GET /store/asService/appList`

**功能描述**: 根据搜索值和状态，分页获取APP端的售后服务列表

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| searchValue | string | 是 | 搜索关键词，用于模糊查询 | 搜索关键词 |
| status | string | 是 | 状态值，用于筛选对应状态的记录 | 1 |

### 2. 申请售后服务

**接口地址**: `POST /store/asService/apply`

**功能描述**: 根据传入的售后服务申请信息提交售后服务申请

### 3. 取消售后服务

**接口地址**: `POST /store/asService/cancle`

**功能描述**: 取消指定ID的售后服务

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| asServiceId | integer | 是 | 售后服务ID，用于标识特定售后服务记录 | 1 |

### 4. 获取售后服务详细信息

**接口地址**: `GET /store/asService/getInfo`

**功能描述**: 根据售后服务ID获取对应的详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| asServiceId | integer | 是 | 售后服务ID，用于标识特定售后服务记录 | 1 |

### 5. 删除售后服务

**接口地址**: `POST /store/asService/remove`

**功能描述**: 根据售后服务ID删除对应的售后服务记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| asServiceId | integer | 是 | 售后服务ID，用于标识特定售后服务记录 | 1 |

### 6. 填写快递单号

**接口地址**: `POST /store/asService/writeDeliveryNo`

**功能描述**: 为指定ID的售后服务填写快递单号信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| asServiceId | integer | 是 | 售后服务ID，用于标识特定售后服务记录 | 1 |
| deliveryName | string | 是 | 快递公司名称 | 顺丰快递 |
| deliveryNo | string | 是 | 快递单号 | SF1234567890 |

---

## 发票管理

### 1. 申请开票

**接口地址**: `POST /store/apply/applyOpenBlueInvoice`

**功能描述**: 新增蓝字发票申请

### 2. 发票信息查询

**接口地址**: `GET /store/apply/getBlueInvoiceInfoByOrderId`

**功能描述**: 获取发票申请详细信息

---

## 优购码管理

### 1. 优购码查询

**接口地址**: `GET /store/code/appList`

**功能描述**: 查询优购码App列表

---

## 用户管理

### 1. 手机号授权登录

**接口地址**: `POST /weixin/login` 和 `POST /weixin/phone`

**功能描述**: 微信小程序登录和获取用户手机号

### 2. 退出登录

**接口地址**: `POST /logout`

**功能描述**: 用户退出登录

---

## 地址管理

### 1. 查询地址列表

**接口地址**: `GET /system/address/list`

**功能描述**: 获取用户的收货地址列表

### 2. 查询地址信息

**接口地址**: `GET /system/address/{userAddressId}`

**功能描述**: 根据地址ID获取具体地址信息

### 3. 新增地址

**接口地址**: `POST /system/address/add`

**功能描述**: 添加新的收货地址

### 4. 修改地址

**接口地址**: `POST /system/address/edit`

**功能描述**: 修改现有的收货地址信息

### 5. 删除地址

**接口地址**: `DELETE /system/address/{userAddressIds}`

**功能描述**: 删除指定的收货地址

### 6. 城市列表

**接口地址**: `GET /system/city/list`

**功能描述**: 获取城市列表数据

### 7. 城市树数据

**接口地址**: `GET /system/city/treeList`

**功能描述**: 获取城市的树形结构数据

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 401 | 未授权，需要登录 |
| 403 | 禁止访问，权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 注意事项

1. 所有接口都需要在请求头中包含有效的 Authorization 令牌
2. 分页查询接口建议设置合理的 pageSize，避免一次性查询过多数据
3. 文件上传接口需要使用 multipart/form-data 格式
4. 删除操作请谨慎使用，建议在前端进行二次确认
5. 价格相关字段使用 BigDecimal 类型，保证精度
6. 时间字段统一使用 ISO 8601 格式

---

*文档最后更新时间: 2025-07-24*

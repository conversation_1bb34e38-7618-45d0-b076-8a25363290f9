[{"页面": "首页", "接口": "上新商品查询", "是否完成": "✔", "接口地址": "/store/product/appList", "method": "GET", "summary": "查询商品APP列表", "description": "根据筛选条件分页获取商品列表，支持按价格、品牌、分类等条件筛选，支持排序", "operationId": "appListUsingGET_2", "tags": ["商品管理"], "parameters": [{"name": "ascOrDesc", "type": "query", "description": "排序方式，asc为升序，desc为降序", "required": false, "dataType": "string", "format": "", "example": "desc"}, {"name": "brandId", "type": "query", "description": "品牌ID，用于筛选指定品牌的商品", "required": false, "dataType": "integer", "format": "int64", "example": "1"}, {"name": "cateId", "type": "query", "description": "分类ID，用于筛选指定分类的商品", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "isHot", "type": "query", "description": "是否热卖商品，1为热卖，0为非热卖", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "isNew", "type": "query", "description": "是否新品，1为新品，0为非新品", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "maxPrice", "type": "query", "description": "最高价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "999.99"}, {"name": "minPrice", "type": "query", "description": "最低价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "10.00"}, {"name": "searchValue", "type": "query", "description": "搜索关键词，用于商品名称或描述的模糊搜索", "required": false, "dataType": "string", "format": "", "example": "手机"}, {"name": "sortField", "type": "query", "description": "排序字段，如price（价格）、createTime（创建时间）等", "required": false, "dataType": "string", "format": "", "example": "price"}]}, {"接口": "热卖商品查询", "是否完成": "✔", "接口地址": "/store/product/appList", "method": "GET", "summary": "查询商品APP列表", "description": "根据筛选条件分页获取商品列表", "operationId": "appListUsingGET_2", "tags": ["商品管理"], "parameters": [{"name": "ascOrDesc", "type": "query", "description": "排序方式，asc为升序，desc为降序", "required": false, "dataType": "string", "format": "", "example": "desc"}, {"name": "brandId", "type": "query", "description": "品牌ID，用于筛选指定品牌", "required": false, "dataType": "integer", "format": "int64", "example": ""}, {"name": "cateId", "type": "query", "description": "分类ID，用于筛选指定分类", "required": false, "dataType": "string", "format": "", "example": ""}, {"name": "isHot", "type": "query", "description": "是否热卖，1为热卖，0为非热卖", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "isNew", "type": "query", "description": "是否新品，1为新品，0为非新品", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "maxPrice", "type": "query", "description": "最高价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "minPrice", "type": "query", "description": "最低价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "searchValue", "type": "query", "description": "搜索关键词，用于模糊查询", "required": false, "dataType": "string", "format": "", "example": "搜索关键词"}, {"name": "sortField", "type": "query", "description": "排序字段名称", "required": false, "dataType": "string", "format": "", "example": "createTime"}]}, {"接口": "热卖商品分类查询"}, {"页面": "商品搜索页", "接口": "商品信息查询（全文检索）", "是否完成": "✔", "接口地址": "/store/product/appList", "method": "GET", "summary": "查询商品APP列表", "description": "根据筛选条件分页获取商品列表", "operationId": "appListUsingGET_2", "tags": ["商品管理"], "parameters": [{"name": "ascOrDesc", "type": "query", "description": "排序方式，asc为升序，desc为降序", "required": false, "dataType": "string", "format": "", "example": "desc"}, {"name": "brandId", "type": "query", "description": "品牌ID，用于筛选指定品牌", "required": false, "dataType": "integer", "format": "int64", "example": ""}, {"name": "cateId", "type": "query", "description": "分类ID，用于筛选指定分类", "required": false, "dataType": "string", "format": "", "example": ""}, {"name": "isHot", "type": "query", "description": "是否热卖，1为热卖，0为非热卖", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "isNew", "type": "query", "description": "是否新品，1为新品，0为非新品", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "maxPrice", "type": "query", "description": "最高价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "minPrice", "type": "query", "description": "最低价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "searchValue", "type": "query", "description": "搜索关键词，用于模糊查询", "required": false, "dataType": "string", "format": "", "example": "搜索关键词"}, {"name": "sortField", "type": "query", "description": "排序字段名称", "required": false, "dataType": "string", "format": "", "example": "createTime"}]}, {"接口": "搜索历史查询"}, {"接口": "猜你喜欢查询"}, {"接口": "热搜榜查询", "是否完成": "✔", "接口地址": "/store/product/appList", "method": "GET", "summary": "查询商品APP列表", "description": "根据筛选条件分页获取商品列表", "operationId": "appListUsingGET_2", "tags": ["商品管理"], "parameters": [{"name": "ascOrDesc", "type": "query", "description": "排序方式，asc为升序，desc为降序", "required": false, "dataType": "string", "format": "", "example": "desc"}, {"name": "brandId", "type": "query", "description": "品牌ID，用于筛选指定品牌", "required": false, "dataType": "integer", "format": "int64", "example": ""}, {"name": "cateId", "type": "query", "description": "分类ID，用于筛选指定分类", "required": false, "dataType": "string", "format": "", "example": ""}, {"name": "isHot", "type": "query", "description": "是否热卖，1为热卖，0为非热卖", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "isNew", "type": "query", "description": "是否新品，1为新品，0为非新品", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "maxPrice", "type": "query", "description": "最高价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "minPrice", "type": "query", "description": "最低价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "searchValue", "type": "query", "description": "搜索关键词，用于模糊查询", "required": false, "dataType": "string", "format": "", "example": "搜索关键词"}, {"name": "sortField", "type": "query", "description": "排序字段名称", "required": false, "dataType": "string", "format": "", "example": "createTime"}]}, {"页面": "商品搜索结果页", "接口": "商品信息查询（全文检索）", "是否完成": "✔", "接口地址": "/store/product/appList", "method": "GET", "summary": "查询商品APP列表", "description": "根据筛选条件分页获取商品列表", "operationId": "appListUsingGET_2", "tags": ["商品管理"], "parameters": [{"name": "ascOrDesc", "type": "query", "description": "排序方式，asc为升序，desc为降序", "required": false, "dataType": "string", "format": "", "example": "desc"}, {"name": "brandId", "type": "query", "description": "品牌ID，用于筛选指定品牌", "required": false, "dataType": "integer", "format": "int64", "example": ""}, {"name": "cateId", "type": "query", "description": "分类ID，用于筛选指定分类", "required": false, "dataType": "string", "format": "", "example": ""}, {"name": "isHot", "type": "query", "description": "是否热卖，1为热卖，0为非热卖", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "isNew", "type": "query", "description": "是否新品，1为新品，0为非新品", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "maxPrice", "type": "query", "description": "最高价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "minPrice", "type": "query", "description": "最低价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "searchValue", "type": "query", "description": "搜索关键词，用于模糊查询", "required": false, "dataType": "string", "format": "", "example": "搜索关键词"}, {"name": "sortField", "type": "query", "description": "排序字段名称", "required": false, "dataType": "string", "format": "", "example": "createTime"}]}, {"页面": "商品分类页", "接口": "商品分类查询", "是否完成": "✔", "接口地址": "/store/productCate/list", "method": "GET", "summary": "查询商品分类列表", "description": "根据商品分类信息查询商品分类列表", "operationId": "listUsingGET_7", "tags": ["商品分类管理"], "parameters": []}, {"页面": "商品分类结果页", "接口": "商品信息查询（按分类）", "是否完成": "✔", "接口地址": "/store/product/appList", "method": "GET", "summary": "查询商品APP列表", "description": "根据筛选条件分页获取商品列表", "operationId": "appListUsingGET_2", "tags": ["商品管理"], "parameters": [{"name": "ascOrDesc", "type": "query", "description": "排序方式，asc为升序，desc为降序", "required": false, "dataType": "string", "format": "", "example": "desc"}, {"name": "brandId", "type": "query", "description": "品牌ID，用于筛选指定品牌", "required": false, "dataType": "integer", "format": "int64", "example": ""}, {"name": "cateId", "type": "query", "description": "分类ID，用于筛选指定分类", "required": false, "dataType": "string", "format": "", "example": ""}, {"name": "isHot", "type": "query", "description": "是否热卖，1为热卖，0为非热卖", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "isNew", "type": "query", "description": "是否新品，1为新品，0为非新品", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "maxPrice", "type": "query", "description": "最高价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "minPrice", "type": "query", "description": "最低价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "searchValue", "type": "query", "description": "搜索关键词，用于模糊查询", "required": false, "dataType": "string", "format": "", "example": "搜索关键词"}, {"name": "sortField", "type": "query", "description": "排序字段名称", "required": false, "dataType": "string", "format": "", "example": "createTime"}]}, {"页面": "商品详情页", "接口": "商品详细信息查询", "是否完成": "✔", "接口地址": "/store/product/getInfo", "method": "GET", "summary": "获取商品详细信息", "description": "根据商品 ID 获取对应的商品详细信息", "operationId": "getInfoUsingGET_7", "tags": ["商品管理"], "parameters": [{"name": "productId", "type": "query", "description": "商品 ID", "required": true, "dataType": "integer", "format": "int64", "example": 1939580898571919400}]}, {"页面": "购物车", "接口": "购物车查询", "是否完成": "✔", "接口地址": "/store/cart/list", "method": "GET", "summary": "查询购物车列表", "description": "根据用户 ID 分页获取用户的购物车列表", "operationId": "listUsingGET_1", "tags": ["购物车管理"], "parameters": [{"name": "userId", "type": "query", "description": "用户 ID，用于筛选对应用户的购物车列表", "required": true, "dataType": "integer", "format": "int64", "example": "1"}]}, {"接口": "购物车新增", "是否完成": "✔", "接口地址": "/store/cart/add", "method": "POST", "summary": "新增购物车记录", "description": "将指定商品及其属性、数量添加到用户购物车中", "operationId": "addUsingPOST", "tags": ["购物车管理"], "parameters": [{"name": "productId", "type": "query", "description": "商品 ID，标识要添加到购物车的商品", "required": true, "dataType": "integer", "format": "int64", "example": "1"}, {"name": "productAttrValueId", "type": "query", "description": "商品属性值 ID，用于确定商品的具体属性", "required": true, "dataType": "integer", "format": "int64", "example": ""}, {"name": "num", "type": "query", "description": "商品数量，指定添加到购物车的商品数量", "required": true, "dataType": "integer", "format": "int32", "example": "1"}]}, {"接口": "购物车删除", "是否完成": "✔", "接口地址": "/store/cart/remove", "method": "POST", "summary": "删除购物车记录", "description": "根据传入的购物车 ID 数组，删除对应的购物车记录", "operationId": "removeUsingPOST_1", "tags": ["购物车管理"], "parameters": [{"name": "cartIds", "type": "query", "description": "购物车 ID 数组，用于指定要删除的购物车记录", "required": true, "dataType": "integer", "format": "int64", "example": ""}]}, {"页面": "确认订单页", "接口": "优购码查询", "是否完成": "✔", "接口地址": "/store/code/appList", "method": "GET", "summary": "查询优购码App列表", "description": "根据使用状态分页获取优购码列表", "operationId": "appListUsingGET_1", "tags": ["优购码管理"], "parameters": [{"name": "usedStatus", "type": "query", "description": "使用状态", "required": false, "dataType": "string", "format": "", "example": ""}]}, {"接口": "收货地址查询", "是否完成": "✔", "接口地址": "/system/address/list"}, {"接口": "下订单", "是否完成": "✔", "接口地址": "/store/order/placeOrder", "method": "POST", "summary": "下订单", "description": "根据传入的下单信息创建新订单", "operationId": "placeOrderUsingPOST", "tags": ["订单管理"], "parameters": [], "requestBody": {"description": "Request body required", "contentType": "application/json", "schema": {"application/json": {"schema": {"$ref": "#/components/schemas/PlaceOrderDto"}, "example": ""}}}}, {"接口": "下微信支付订单"}, {"页面": "支付成功页", "接口": "推荐商品查询", "是否完成": "✔", "接口地址": "/store/product/appList", "method": "GET", "summary": "查询商品APP列表", "description": "根据筛选条件分页获取商品列表", "operationId": "appListUsingGET_2", "tags": ["商品管理"], "parameters": [{"name": "ascOrDesc", "type": "query", "description": "排序方式，asc为升序，desc为降序", "required": false, "dataType": "string", "format": "", "example": "desc"}, {"name": "brandId", "type": "query", "description": "品牌ID，用于筛选指定品牌", "required": false, "dataType": "integer", "format": "int64", "example": ""}, {"name": "cateId", "type": "query", "description": "分类ID，用于筛选指定分类", "required": false, "dataType": "string", "format": "", "example": ""}, {"name": "isHot", "type": "query", "description": "是否热卖，1为热卖，0为非热卖", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "isNew", "type": "query", "description": "是否新品，1为新品，0为非新品", "required": false, "dataType": "string", "format": "", "example": "1"}, {"name": "maxPrice", "type": "query", "description": "最高价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "minPrice", "type": "query", "description": "最低价格，用于价格区间筛选", "required": false, "dataType": "number", "format": "bigdecimal", "example": "99.99"}, {"name": "searchValue", "type": "query", "description": "搜索关键词，用于模糊查询", "required": false, "dataType": "string", "format": "", "example": "搜索关键词"}, {"name": "sortField", "type": "query", "description": "排序字段名称", "required": false, "dataType": "string", "format": "", "example": "createTime"}]}, {"页面": "支付失败页", "接口": "下微信支付订单"}, {"页面": "订单列表页", "接口": "订单查询（按状态）", "是否完成": "✔", "接口地址": "/store/order/appList", "method": "GET", "summary": "查询订单列表（APP端）", "description": "根据用户 ID、订单状态、页码和每页数量获取订单列表", "operationId": "listUsingGET_3", "tags": ["订单管理"], "parameters": [{"name": "userId", "type": "query", "description": "用户 ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}, {"name": "orderStatus", "type": "query", "description": "订单状态", "required": true, "dataType": "string", "format": "", "example": ""}, {"name": "pageNum", "type": "query", "description": "页码", "required": true, "dataType": "integer", "format": "int32", "example": ""}, {"name": "pageSize", "type": "query", "description": "每页数量", "required": true, "dataType": "integer", "format": "int32", "example": ""}]}, {"接口": "取消订单", "是否完成": "✔", "接口地址": "/store/order/cancle", "method": "POST", "summary": "取消订单", "description": "根据传入的订单 ID 数组取消对应的订单", "operationId": "cancleUsingPOST_2", "tags": ["订单管理"], "parameters": [{"name": "orderIds", "type": "query", "description": "订单 ID 数组", "required": true, "dataType": "integer", "format": "int64", "example": ""}]}, {"接口": "修改地址", "是否完成": "✔", "接口地址": "/store/order/updateUserAddress", "method": "POST", "summary": "修改收货地址", "description": "根据订单 ID 和用户地址 ID 修改对应订单的收货地址", "operationId": "updateUserAddressUsingPOST", "tags": ["订单管理"], "parameters": [{"name": "orderId", "type": "query", "description": "订单 ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}, {"name": "userAddressId", "type": "query", "description": "用户地址 ID", "required": true, "dataType": "integer", "format": "int64", "example": ""}]}, {"接口": "下微信支付订单"}, {"接口": "申请开票", "是否完成": "✔", "接口地址": "/store/apply/applyOpenBlueInvoice", "method": "POST", "summary": "新增蓝字发票申请", "description": "提交新的发票申请信息", "operationId": "applyOpenBlueInvoiceUsingPOST", "tags": ["发票申请管理"], "parameters": [], "requestBody": {"description": "Request body required", "contentType": "application/json", "schema": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoiceApplyDto"}, "example": ""}}}}, {"接口": "发票信息查询", "是否完成": "✔", "接口地址": "/store/apply/getBlueInvoiceInfoByOrderId", "method": "GET", "summary": "获取发票申请详细信息", "description": "根据订单 ID 获取蓝字发票信息", "operationId": "getBlueInvoiceInfoByOrderIdUsingGET", "tags": ["发票申请管理"], "parameters": [{"name": "orderId", "type": "query", "description": "订单 ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}]}, {"接口": "订单删除", "是否完成": "✔", "接口地址": "/store/order/remove", "method": "POST", "summary": "删除订单", "description": "根据传入的订单 ID 数组删除对应的订单", "operationId": "removeUsingPOST_3", "tags": ["订单管理"], "parameters": [{"name": "orderIds", "type": "query", "description": "订单 ID 数组", "required": true, "dataType": "integer", "format": "int64", "example": ""}]}, {"页面": "订单详情页", "接口": "物流信息查询"}, {"接口": "订单详情查询", "是否完成": "✔", "接口地址": "/store/order/getInfo", "method": "GET", "summary": "获取订单详细信息", "description": "根据订单 ID 获取对应的订单详细信息", "operationId": "getInfoUsingGET_2", "tags": ["订单管理"], "parameters": [{"name": "orderId", "type": "query", "description": "订单 ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}]}, {"接口": "发票信息查询", "是否完成": "✔", "接口地址": "/store/apply/getBlueInvoiceInfoByOrderId", "method": "GET", "summary": "获取发票申请详细信息", "description": "根据订单 ID 获取蓝字发票信息", "operationId": "getBlueInvoiceInfoByOrderIdUsingGET", "tags": ["发票申请管理"], "parameters": [{"name": "orderId", "type": "query", "description": "订单 ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}]}, {"页面": "售后列表页", "接口": "待售后订单查询", "是否完成": "✔", "接口地址": "/store/order/asServiceList", "method": "GET", "summary": "售后服务订单列表", "description": "根据搜索值分页获取可用于售后服务的订单列表", "operationId": "listUsingGET_5", "tags": ["订单管理"], "parameters": [{"name": "searchValue", "type": "query", "description": "搜索值", "required": true, "dataType": "string", "format": "", "example": "搜索关键词"}]}, {"接口": "售后申请单查询（按状态）", "是否完成": "✔", "接口地址": "/store/asService/appList", "method": "GET", "summary": "查询APP端售后服务列表", "description": "根据搜索值和状态，分页获取APP端的售后服务列表", "operationId": "appListUsingGET", "tags": ["售后服务管理"], "parameters": [{"name": "searchValue", "type": "query", "description": "搜索值，用于筛选相关售后服务", "required": true, "dataType": "string", "format": "", "example": "搜索关键词"}, {"name": "status", "type": "query", "description": "售后服务状态，用于筛选对应状态的售后服务", "required": true, "dataType": "string", "format": "", "example": ""}]}, {"接口": "申请售后", "是否完成": "✔", "接口地址": "/store/asService/apply", "method": "POST", "summary": "申请售后服务", "description": "根据传入的售后服务申请信息提交售后服务申请", "operationId": "applyUsingPOST", "tags": ["售后服务管理"], "parameters": [], "requestBody": {"description": "Request body required", "contentType": "application/json", "schema": {"application/json": {"schema": {"$ref": "#/components/schemas/AsServiceDto"}, "example": ""}}}}, {"接口": "取消申请", "是否完成": "✔", "接口地址": "/store/asService/cancle", "method": "POST", "summary": "取消售后服务", "description": "取消指定ID的售后服务", "operationId": "cancleUsingPOST", "tags": ["售后服务管理"], "parameters": [{"name": "asServiceId", "type": "query", "description": "售后服务ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}]}, {"接口": "填写快递单号", "是否完成": "✔", "接口地址": "/store/asService/writeDeliveryNo", "method": "POST", "summary": "填写快递单号", "description": "为指定ID的售后服务填写快递单号信息", "operationId": "writeDeliveryNoUsingPOST", "tags": ["售后服务管理"], "parameters": [{"name": "asServiceId", "type": "query", "description": "售后服务ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}, {"name": "deliveryName", "type": "query", "description": "快递公司名称", "required": true, "dataType": "string", "format": "", "example": "顺丰快递"}, {"name": "deliveryNo", "type": "query", "description": "快递单号", "required": true, "dataType": "string", "format": "", "example": "SF1234567890"}], "requestBody": {"description": "Request body required", "contentType": "application/json", "schema": {"application/json": {"schema": {"type": "string"}, "example": ""}}}}, {"接口": "删除售后申请单", "是否完成": "✔", "接口地址": "/store/asService/remove", "method": "POST", "summary": "删除售后服务", "description": "根据售后服务ID删除对应的售后服务记录", "operationId": "removeUsingPOST", "tags": ["售后服务管理"], "parameters": [{"name": "asServiceId", "type": "query", "description": "售后服务ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}]}, {"接口": "售后详情查询", "是否完成": "✔", "接口地址": "/store/asService/getInfo", "method": "GET", "summary": "获取售后服务详细信息", "description": "根据售后服务ID获取对应的详细信息", "operationId": "getInfoUsingGET", "tags": ["售后服务管理"], "parameters": [{"name": "asServiceId", "type": "query", "description": "售后服务ID", "required": true, "dataType": "integer", "format": "int64", "example": "1"}]}, {"页面": "个人中心", "接口": "优购码查询", "是否完成": "✔", "接口地址": "/store/code/appList", "method": "GET", "summary": "查询优购码App列表", "description": "根据使用状态分页获取优购码列表", "operationId": "appListUsingGET_1", "tags": ["优购码管理"], "parameters": [{"name": "usedStatus", "type": "query", "description": "使用状态", "required": false, "dataType": "string", "format": "", "example": ""}]}, {"接口": "积分查询"}, {"接口": "优惠券查询"}, {"接口": "手机号授权登录", "是否完成": "✔", "接口地址": "/weixin/login,/weixin/phone", "method": "POST", "summary": "微信小程序登录", "description": "根据微信小程序提供的 code 获取 session 信息", "operationId": "loginUsingPOST_1", "tags": ["微信小程序登录管理"], "parameters": [{"name": "code", "type": "query", "description": "微信小程序登录凭证 code", "required": true, "dataType": "string", "format": "", "example": ""}]}, {"接口": "退出登录", "是否完成": "✔", "接口地址": "/logout"}, {"页面": "我的地址", "接口": "查询地址列表", "是否完成": "✔", "接口地址": "/system/address/list"}, {"接口": "查询地址信息", "是否完成": "✔", "接口地址": "/system/address/{userAddressId}"}, {"接口": "新增地址", "是否完成": "✔", "接口地址": "/system/address/add"}, {"接口": "修改地址", "是否完成": "✔", "接口地址": "/system/address/edit"}, {"接口": "城市列表", "是否完成": "✔", "接口地址": "/system/city/list"}, {"接口": "城市树数据", "是否完成": "✔", "接口地址": "/system/city/treeList"}, {"接口": "删除地址", "是否完成": "✔", "接口地址": "/system/address/{userAddressIds}"}]
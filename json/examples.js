/**
 * API 使用示例
 * 展示各个模块的API调用方法和最佳实践
 */

// 导入API函数
import {
  // 登录相关
  login,
  getPhoneNumber,
  getUserInfo,
  logout,
  phoneAuthLogin,

  // 商品相关
  getProductList,
  getProductInfo,
  getProductCategories,
  getNewProducts,
  getHotProducts,
  searchProducts,

  // 购物车相关
  getCartList,
  addToCart,
  removeFromCart,
  getCartCount,

  // 订单相关
  getOrderList,
  placeOrder,
  getOrderInfo,
  cancelOrder,

  // 售后相关
  getAfterSalesList,
  applyAfterSales,
  getAfterSalesInfo,

  // 发票相关
  applyInvoice,
  getInvoiceInfo,

  // 地址相关
  getAddressList,
  addAddress,
  editAddress,

  // 优购码相关
  getCouponList,
  checkCouponAvailability
} from "../api"

/**
 * 用户登录流程示例
 */
export const loginExample = async () => {
  try {
    // 方式1：分步登录
    console.log("=== 分步登录示例 ===")

    // 第一步：微信登录
    const loginResult = await login({ code: "wx_login_code" })
    console.log("微信登录结果:", loginResult)

    // 第二步：获取手机号
    const phoneResult = await getPhoneNumber({
      openId: loginResult.data.openId,
      code: "phone_auth_code"
    })
    console.log("手机号授权结果:", phoneResult)

    // 第三步：获取用户信息
    const userInfo = await getUserInfo()
    console.log("用户信息:", userInfo)

    // 方式2：一键登录
    console.log("=== 一键登录示例 ===")
    const quickLoginResult = await phoneAuthLogin({
      code: "wx_login_code",
      phoneCode: "phone_auth_code"
    })
    console.log("一键登录结果:", quickLoginResult)
  } catch (error) {
    console.error("登录失败:", error)
  }
}

/**
 * 商品浏览流程示例
 */
export const productBrowsingExample = async () => {
  try {
    console.log("=== 商品浏览示例 ===")

    // 获取商品分类
    const categories = await getProductCategories()
    console.log("商品分类:", categories)

    // 获取新品列表
    const newProducts = await getNewProducts({
      pageSize: 10,
      sortField: "createTime",
      ascOrDesc: "desc"
    })
    console.log("新品列表:", newProducts)

    // 获取热卖商品
    const hotProducts = await getHotProducts({
      pageSize: 10,
      minPrice: 100,
      maxPrice: 1000
    })
    console.log("热卖商品:", hotProducts)

    // 搜索商品
    const searchResults = await searchProducts({
      searchValue: "手机",
      cateId: "1",
      minPrice: 500,
      maxPrice: 5000,
      sortField: "price",
      ascOrDesc: "asc"
    })
    console.log("搜索结果:", searchResults)

    // 获取商品详情
    if (searchResults.data && searchResults.data.length > 0) {
      const productDetail = await getProductInfo({
        productId: searchResults.data[0].id
      })
      console.log("商品详情:", productDetail)
    }
  } catch (error) {
    console.error("商品浏览失败:", error)
  }
}

/**
 * 购物车操作示例
 */
export const cartOperationExample = async () => {
  try {
    console.log("=== 购物车操作示例 ===")

    const userId = 123

    // 添加商品到购物车
    const addResult = await addToCart({
      productId: 456,
      productAttrValueId: 789,
      num: 2
    })
    console.log("添加到购物车结果:", addResult)

    // 获取购物车列表
    const cartList = await getCartList({ userId })
    console.log("购物车列表:", cartList)

    // 获取购物车统计
    const cartCount = await getCartCount({ userId })
    console.log("购物车统计:", cartCount)

    // 删除购物车商品
    if (cartList.data && cartList.data.length > 0) {
      const removeResult = await removeFromCart({
        cartIds: [cartList.data[0].id]
      })
      console.log("删除购物车商品结果:", removeResult)
    }
  } catch (error) {
    console.error("购物车操作失败:", error)
  }
}

/**
 * 订单管理示例
 */
export const orderManagementExample = async () => {
  try {
    console.log("=== 订单管理示例 ===")

    const userId = 123

    // 下订单
    const orderData = {
      userId: userId,
      addressId: 1,
      cartIds: [1, 2, 3],
      couponId: null,
      remark: "请尽快发货"
    }

    const placeOrderResult = await placeOrder(orderData)
    console.log("下订单结果:", placeOrderResult)

    // 获取订单列表
    const orderList = await getOrderList({
      userId: userId,
      orderStatus: "0", // 待付款
      pageNum: 1,
      pageSize: 10
    })
    console.log("订单列表:", orderList)

    // 获取订单详情
    if (orderList.data && orderList.data.length > 0) {
      const orderDetail = await getOrderInfo({
        orderId: orderList.data[0].id
      })
      console.log("订单详情:", orderDetail)

      // 取消订单
      const cancelResult = await cancelOrder({
        orderIds: [orderList.data[0].id]
      })
      console.log("取消订单结果:", cancelResult)
    }
  } catch (error) {
    console.error("订单管理失败:", error)
  }
}

/**
 * 售后服务示例
 */
export const afterSalesExample = async () => {
  try {
    console.log("=== 售后服务示例 ===")

    // 获取售后列表
    const afterSalesList = await getAfterSalesList({
      searchValue: "",
      status: "0" // 待处理
    })
    console.log("售后列表:", afterSalesList)

    // 申请售后
    const afterSalesData = {
      orderId: 123,
      productId: 456,
      reason: "商品质量问题",
      description: "收到商品有划痕",
      images: ["image1.jpg", "image2.jpg"]
    }

    const applyResult = await applyAfterSales(afterSalesData)
    console.log("申请售后结果:", applyResult)

    // 获取售后详情
    if (applyResult.data && applyResult.data.id) {
      const afterSalesDetail = await getAfterSalesInfo({
        asServiceId: applyResult.data.id
      })
      console.log("售后详情:", afterSalesDetail)
    }
  } catch (error) {
    console.error("售后服务失败:", error)
  }
}

/**
 * 发票管理示例
 */
export const invoiceExample = async () => {
  try {
    console.log("=== 发票管理示例 ===")

    const orderId = 123

    // 申请发票
    const invoiceData = {
      orderId: orderId,
      invoiceType: "个人",
      invoiceTitle: "张三",
      receiverName: "张三",
      receiverPhone: "13800138000",
      receiverEmail: "<EMAIL>"
    }

    const applyInvoiceResult = await applyInvoice(invoiceData)
    console.log("申请发票结果:", applyInvoiceResult)

    // 查询发票信息
    const invoiceInfo = await getInvoiceInfo({ orderId })
    console.log("发票信息:", invoiceInfo)
  } catch (error) {
    console.error("发票管理失败:", error)
  }
}

/**
 * 地址管理示例
 */
export const addressExample = async () => {
  try {
    console.log("=== 地址管理示例 ===")

    // 获取地址列表
    const addressList = await getAddressList()
    console.log("地址列表:", addressList)

    // 新增地址
    const newAddressData = {
      receiverName: "张三",
      receiverPhone: "13800138000",
      province: "北京市",
      city: "北京市",
      district: "朝阳区",
      detailAddress: "某某街道123号",
      isDefault: false
    }

    const addAddressResult = await addAddress(newAddressData)
    console.log("新增地址结果:", addAddressResult)

    // 修改地址
    if (addAddressResult.data && addAddressResult.data.id) {
      const editAddressData = {
        ...newAddressData,
        userAddressId: addAddressResult.data.id,
        receiverName: "李四",
        isDefault: true
      }

      const editResult = await editAddress(editAddressData)
      console.log("修改地址结果:", editResult)
    }
  } catch (error) {
    console.error("地址管理失败:", error)
  }
}

/**
 * 优购码使用示例
 */
export const couponExample = async () => {
  try {
    console.log("=== 优购码使用示例 ===")

    // 获取优购码列表
    const couponList = await getCouponList()
    console.log("优购码列表:", couponList)

    // 检查优购码可用性
    const checkResult = await checkCouponAvailability({
      couponCode: "COUPON123",
      orderAmount: 500
    })
    console.log("优购码可用性检查:", checkResult)
  } catch (error) {
    console.error("优购码使用失败:", error)
  }
}

/**
 * 完整购物流程示例
 */
export const completeShoppingExample = async () => {
  try {
    console.log("=== 完整购物流程示例 ===")

    // 1. 用户登录
    await loginExample()

    // 2. 浏览商品
    await productBrowsingExample()

    // 3. 购物车操作
    await cartOperationExample()

    // 4. 订单管理
    await orderManagementExample()

    // 5. 售后服务（如需要）
    // await afterSalesExample()

    // 6. 发票申请（如需要）
    // await invoiceExample()

    console.log("完整购物流程演示完成")
  } catch (error) {
    console.error("购物流程失败:", error)
  }
}

// 导出所有示例函数
export default {
  loginExample,
  productBrowsingExample,
  cartOperationExample,
  orderManagementExample,
  afterSalesExample,
  invoiceExample,
  addressExample,
  couponExample,
  completeShoppingExample
}

{"id": "internal-purchase", "name": "internal-purchase", "displayName": "内购小程序", "version": "3.4.4", "description": "伟仕佳杰内购小程序", "scripts": {"dev:mp-weixin": "vue-cli-service uni-mp-weixin --mode development", "prepare": "husky", "lint-staged": "lint-staged", "commitlint": "commitlint -e -V", "cm": "git-cz", "cm:ci": "git sts && git add . && git sts && git-cz && git push origin HEAD -f"}, "repository": "https://gitee.com/mingqun/internal-purchase", "keywords": ["uni-app", "wot-design-uni"], "author": "", "license": "MIT", "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "京东": "y", "钉钉": "y", "快手": "y", "飞书": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@dcloudio/uni-cli-shared": "2.0.2-4070520250711001", "@dcloudio/uni-mp-weixin": "2.0.2-4070520250711001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-3000020210521001", "@vue/cli-service": "^5.0.8", "commitizen": "^4.3.1", "cz-git": "^1.12.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "luxon": "^3.7.1", "prettier": "3.5.3"}, "dependencies": {"@vant/area-data": "^2.0.0", "wot-design-uni": "^1.8.0"}, "config": {"commitizen": {"path": "cz-git"}}, "lint-staged": {"*.{js,ts,tsx,jsx}": ["prettier --write"], "*.json": ["prettier --write"]}}
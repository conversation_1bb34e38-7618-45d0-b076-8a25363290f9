<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-footer="true"
  >
    <template #nav>
      <my-nav :title="title" backIcon />
    </template>

    <view class="content">
      <address-bar :address-info="addressInfo" show-icon />
      <view v-if="orderList.length > 0">
        <view v-for="(item, index) in orderList" :key="index" class="order-item-wrapper">
          <order-item
            :order="item"
            :type="item.type"
            :show-amount="false"
            :show-status="false"
            :show-action-bar="false"
          />
        </view>
      </view>
      <order-card :orderInfo="orderInfo" />
      <price-card :price-info="priceInfo" :total-pay="totalPay" />
    </view>

    <template #footer>
      <view class="footer-bar">
        <view class="btn-cancel" v-if="status === 'completed'" @tap="onAction('showInvoice')">查看发票</view>
        <view class="btn-cancel" v-if="status === 'invoice'" @tap="onAction('applyInvoice')">申请开票</view>
        <view class="btn-cancel" @tap="onAction('buyAgain')">再来一单</view>
        <view class="btn-cancel" @tap="onAction('refund')">退款/售后</view>
      </view>
    </template>
  </my-page>
</template>

<script>
  export default {
    data() {
      return {
        title: "已完成",
        status: "completed",
        addressInfo: {
          name: "张三",
          phone: "13800000000",
          address: "广东省广州市广州市天河路123号"
        },
        orderInfo: {
          orderNo: "35126666778899",
          orderTime: "2024-09-04 10:10:10",
          deliveryType: "快递运输",
          remark: "发货时请选择顺丰",
          payType: "微信支付"
        },
        priceInfo: {
          totalPrice: "100.00",
          chargesPrice: "10.00",
          discountCode: "DISCOUNT2024",
          discountPrice: "5.00"
        },
        totalPay: "90.00",
        orderList: []
      }
    },
    mounted() {},
    onLoad(options = {}) {
      this.title = options.title || "已完成"
      this.status = options.status || "completed"
    },
    async created() {
      this.orderList = await this.generateOrder()
      console.log("orderList", this.orderList)
    },
    methods: {
      onAction(type = "") {
        console.log("onAction type >>", type)
      },

      async generateOrder() {
        console.log("generateOrder")
        // 商品模板
        const products = [
          {
            productId: "matepad-pro-13",
            name: "HUAWEI MatePad Pro 13.2英寸 平板电脑 WiFi连接 支持通话",
            specs: { memory: "12GB", storage: "256GB", color: "曜金黑" },
            price: 3999,
            imageUrl: "https://example.com/matepad-pro-13.jpg",
            gifts: [
              {
                name: "平板电脑保护壳",
                quantity: 1,
                imageUrl: "https://example.com/matepad-pro-13.jpg"
              },
              {
                name: "超薄充电器",
                quantity: 1,
                imageUrl: "https://example.com/matepad-pro-13.jpg"
              }
            ],
            isChecked: false
          },
          {
            productId: "matebook-16s-2023",
            name: "HUAWEI MateBook 16s 2023 13代酷睿i5 32G 1T 16英寸高色准",
            specs: {
              processor: "i5",
              memory: "32GB",
              storage: "1TB",
              color: "深空灰"
            },
            price: 7199,
            imageUrl: "https://example.com/matebook-16s.jpg",
            gifts: [
              {
                name: "笔记本电脑保护套",
                quantity: 1,
                imageUrl: "https://example.com/matebook-16s.jpg"
              },
              {
                name: "超薄充电器",
                quantity: 1,
                imageUrl: "https://example.com/matebook-16s.jpg"
              }
            ],
            isChecked: false
          }
        ]

        return Array(4)
          .fill(null)
          .map(() => {
            // 随机选择1-2个商品
            const itemCount = Math.floor(Math.random() * 2) + 1
            const selectedProducts = []
            while (selectedProducts.length < itemCount) {
              const product = products[Math.floor(Math.random() * products.length)]
              if (!selectedProducts.find((p) => p.productId === product.productId)) {
                selectedProducts.push({
                  ...product,
                  quantity: Math.floor(Math.random() * 2) + 1
                })
              }
            }

            // 计算总金额和总数量
            const totalAmount = selectedProducts.reduce((sum, item) => sum + item.price * item.quantity, 0)
            const totalItems = selectedProducts.reduce((sum, item) => sum + item.quantity, 0)

            return {
              orderId: Math.random().toString(36).substr(2, 9),
              status: "待付款",
              type: "payment",
              totalAmount,
              totalItems,
              seller: { name: "华为品牌官方授权", isOfficial: true },
              items: selectedProducts
            }
          })
      }
    }
  }
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    flex-direction: column;
    padding: 30rpx;

    .order-item-wrapper {
      display: flex;
      flex-direction: column;
      padding: 20rpx 0;
    }
  }

  .footer-bar {
    height: 100rpx;
    background-color: #ffffff;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-end;
    padding: 20rpx 30rpx env(safe-area-inset-bottom);

    .btn-cancel {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx 20rpx;
      background-color: #f0f0f0;
      border-radius: 30rpx;
      border: 1px solid #dcdcdc;
      margin-right: 20rpx;
      font-size: 28rpx;
      color: #666666;
    }

    .btn-pay {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx 20rpx;
      background-color: #ff5722; /* 使用主题色 */
      border-radius: 30rpx;
      color: #ffffff;
      font-size: 28rpx;
      border: none;
    }
  }
</style>

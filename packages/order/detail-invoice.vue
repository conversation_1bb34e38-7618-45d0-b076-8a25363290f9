<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-fixed-bar="true"
    :fixed-bar-height="100"
  >
    <template #nav>
      <my-nav title="发票详情" backIcon />
    </template>

    <template #fixedBar>
      <view class="fixed-bar">
        <invoice-step class="step-bar" />
      </view>
    </template>

    <view class="content">
      <view v-if="orderList.length > 0">
        <view v-for="(item, index) in orderList" :key="index" class="order-item-wrapper">
          <order-item
            :order="item"
            :type="item.type"
            :show-amount="false"
            :show-status="false"
            :show-action-bar="false"
          />
        </view>
      </view>
      <invoice-card :invoice-info="invoiceInfo" />
      <invoice-status :invoice-status="invoiceStatus" />
    </view>
  </my-page>
</template>

<script>
  export default {
    data() {
      return {
        invoiceInfo: {
          type: "电子发票",
          content: "商品明细",
          headerType: "个人",
          headerName: "张三",
          price: "100.00",
          phone: "13800000000",
          email: "<EMAIL>",
          applyTime: "2024-09-04 10:10:10",
          createTime: "2024-09-04 10:10:10",
          img: "https://api.autodl.com/docs/assets/2024-01-02-18-19-46-image.png"
        },
        invoiceStatus: {
          orderType: "已完成",
          orderNo: "35126666778899",
          createTime: "2024-09-04 10:10:10"
        },
        totalPay: "90.00",
        orderList: []
      }
    },
    mounted() {},
    async created() {
      this.orderList = await this.generateOrder()
      console.log("orderList", this.orderList)
    },
    methods: {
      async onAction(type = "") {
        console.log("onAction type >>", type)
        // 模拟支付逻辑
        uni.showToast({
          title: "支付成功",
          icon: "success"
        })
        // 这里可以添加支付成功后的逻辑，比如跳转到订单列表页等
      },

      async generateOrder() {
        console.log("generateOrder")
        // 商品模板
        const products = [
          {
            productId: "matepad-pro-13",
            name: "HUAWEI MatePad Pro 13.2英寸 平板电脑 WiFi连接 支持通话",
            specs: { memory: "12GB", storage: "256GB", color: "曜金黑" },
            price: 3999,
            imageUrl: "https://example.com/matepad-pro-13.jpg",
            gifts: [
              {
                name: "平板电脑保护壳",
                quantity: 1,
                imageUrl: "https://example.com/matepad-pro-13.jpg"
              },
              {
                name: "超薄充电器",
                quantity: 1,
                imageUrl: "https://example.com/matepad-pro-13.jpg"
              }
            ],
            isChecked: false
          },
          {
            productId: "matebook-16s-2023",
            name: "HUAWEI MateBook 16s 2023 13代酷睿i5 32G 1T 16英寸高色准",
            specs: {
              processor: "i5",
              memory: "32GB",
              storage: "1TB",
              color: "深空灰"
            },
            price: 7199,
            imageUrl: "https://example.com/matebook-16s.jpg",
            gifts: [
              {
                name: "笔记本电脑保护套",
                quantity: 1,
                imageUrl: "https://example.com/matebook-16s.jpg"
              },
              {
                name: "超薄充电器",
                quantity: 1,
                imageUrl: "https://example.com/matebook-16s.jpg"
              }
            ],
            isChecked: false
          }
        ]

        return Array(3)
          .fill(null)
          .map(() => {
            // 随机选择1-2个商品
            const itemCount = Math.floor(Math.random() * 2) + 1
            const selectedProducts = []
            while (selectedProducts.length < itemCount) {
              const product = products[Math.floor(Math.random() * products.length)]
              if (!selectedProducts.find((p) => p.productId === product.productId)) {
                selectedProducts.push({
                  ...product,
                  quantity: Math.floor(Math.random() * 2) + 1
                })
              }
            }

            // 计算总金额和总数量
            const totalAmount = selectedProducts.reduce((sum, item) => sum + item.price * item.quantity, 0)
            const totalItems = selectedProducts.reduce((sum, item) => sum + item.quantity, 0)

            return {
              orderId: Math.random().toString(36).substr(2, 9),
              status: "待付款",
              type: "payment",
              totalAmount,
              totalItems,
              seller: { name: "华为品牌官方授权", isOfficial: true },
              items: selectedProducts
            }
          })
      }
    }
  }
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    flex-direction: column;
    padding: 30rpx;

    .order-item-wrapper {
      display: flex;
      flex-direction: column;
      padding: 20rpx 0;
    }
  }

  .fixed-bar {
    display: flex;
    height: 150rpx;
    padding: 20rpx 30rpx;

    .step-bar {
      width: 100%;
    }
  }
</style>

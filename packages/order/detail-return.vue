<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-footer="true"
  >
    <template #nav>
      <my-nav title="退货" backIcon />
    </template>

    <view class="content">
      <view v-for="(item, index) in productInfos" :key="index">
        <product-item :productInfo="item" />
      </view>

      <view class="reason-card">
        <view class="reason-card__bar" @tap="showApplyReasonPopup = true">
          <view class="reason-card__bar__content">
            <text class="require">*</text>
            <text>申请原因</text>
            <text class="placeholder">{{ reason }}</text>
          </view>
          <wd-icon name="arrow-right" size="16" color="#999999" />
        </view>
        <view class="reason-card__bar-action" @tap="showRequestReasonPopup = true">
          <view class="reason-card__bar-action__content">
            <text>问题描述与凭着</text>
          </view>
          <view class="reason-card__bar-action__content">
            <view class="placeholder">{{ requestReason }}</view>
            <wd-icon name="arrow-right" size="16" color="#999999" />
          </view>
        </view>
      </view>

      <view class="back-card">
        <view class="back-item">
          <view class="label">返回方式</view>
          <view class="value">{{ backInfo.returnType }}</view>
        </view>
        <view class="back-item">
          <view class="label">退货地址</view>
          <view class="value">{{ backInfo.returnAddress }}</view>
        </view>
      </view>

      <view class="back-card">
        <view class="back-item">
          <view class="label">退款方式</view>
          <view class="value">{{ returnInfo.returnType }}</view>
        </view>
        <view class="back-item">
          <view class="label">退款金额</view>
          <view class="value price-value">¥{{ returnInfo.returnPrice }}</view>
        </view>
      </view>
    </view>

    <template #footer>
      <view class="footer-bar">
        <wd-button class="btn-action" @tap="onAction('exchange')">提交</wd-button>
      </view>
    </template>
  </my-page>

  <my-popup
    :visible="showApplyReasonPopup"
    title="请选择取消原因(必选)"
    showTopBar
    showFooter
    @close="onCancelPopupClose"
  >
    <template #topBar>
      <view> 温馨提示：取消订单后，订单将无法恢复，已用抵现积分，优惠券将原路返还 </view>
    </template>

    <template #content>
      <view class="popup-item" v-for="(item, index) in reasonColumns" :key="index">
        <view class="popup-item-text">{{ item.label }}</view>
        <wd-checkbox v-model="item.isChecked" @change="selectReason(item)" />
      </view>
    </template>

    <template #footer>
      <view class="popup-footer">
        <wd-button type="primary" @click="submitCancelReason"> 提交 </wd-button>
      </view>
    </template>
  </my-popup>

  <my-popup :visible="showRequestReasonPopup" title="上传描述与凭证" showFooter @close="onRequestReasonPopup">
    <template #content>
      <view class="popup-request-block">
        <view class="title">问题描述</view>
        <wd-textarea
          style="padding: 0"
          v-model="reasonDesc"
          placeholder="请输入问题描述"
          :rows="3"
          :maxlength="500"
          custom-textarea-class="custom-textarea"
        />
        <view class="title-bar">
          <view class="main-title">上传凭证</view>
          <view class="sub-title">最多3张</view>
        </view>

        <wd-upload
          :file-list="fileList"
          :limit="3"
          action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
          :multiple="false"
          :show-limit-num="true"
          accept="image"
          image-mode="aspectFill"
          :max-count="1"
          :max-size="1024 * 1024 * 2"
          :form-data="{ type: 3 }"
          @change="handleChange"
          @remove="handleRemove"
          @fail="handleError"
          @progress="handleProgress"
          @success="handleSuccess"
          @oversize="handleOversize"
        ></wd-upload>
      </view>
    </template>
    <template #footer>
      <view class="popup-footer">
        <wd-button type="primary" @click="submitRequestReason"> 确认 </wd-button>
      </view>
    </template>
  </my-popup>
</template>

<script>
  // :header="{ Authorization: 'Bearer ' + token, 'Content-Type': 'multipart/form-data' }"
  // 取消订单原因
  const REFUND_REASON = [
    { label: "选错商品", value: "01" },
    { label: "重复下单", value: "02" },
    { label: "商品质量问题", value: "03" },
    { label: "商品缺货", value: "05" },
    { label: "商品价格错误", value: "06" },
    { label: "商品描述不符", value: "07" },
    { label: "其他原因", value: "08" }
  ]
  export default {
    data() {
      return {
        productInfos: [
          {
            image: "https://modao.cc/uploads6/images/13579/135791499/v2_sigj9g.png",
            title: "华为 Mate 60 Pro",
            spec: "(8GB+256GB) 皓月银",
            quantity: 1,
            price: 3999
          }
        ],
        showApplyReasonPopup: false,
        reasonColumns: REFUND_REASON.map((item, index) => {
          return {
            label: item.label,
            value: item.value,
            isChecked: false,
            index: index
          }
        }),
        reason: "请选择退款原因",
        requestReason: "上传商品问题照片，增加申请通过率，最多3张",
        selectedReason: [],

        showRequestReasonPopup: false,
        reasonDesc: "",

        fileList: [
          {
            url: "https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg"
          }
        ],
        backInfo: {
          returnType: "快递寄回",
          returnAddress: "北京市海淀区西二旗中路1号"
        },
        returnInfo: {
          returnType: "按照原路退还到微信",
          returnPrice: "1198"
        }
      }
    },
    methods: {
      onAction(action) {
        switch (action) {
          case "exchange":
            // 提交换货请求
            uni.showToast({
              title: "换货请求已提交",
              icon: "success"
            })
            break
          default:
            console.warn("未知操作:", action)
        }
      },

      // 在 methods 中添加
      selectReason(selectedItem) {
        console.log("selectReason", selectedItem)
        const { label, value, isChecked } = selectedItem || {}
        if (isChecked) {
          // 如果选中，添加到 selectedReason
          this.selectedReason.push({ label, value })
        } else {
          // 如果取消选中，从 selectedReason 中移除
          this.selectedReason = this.selectedReason.filter((item) => item.value !== value)
        }
      },

      async submitCancelReason() {
        if (this.selectedReason.length === 0) {
          uni.showToast({
            title: "请选择取消原因",
            icon: "none"
          })
          return
        }

        console.log("提交取消原因:", this.selectedReason)
        // TODO: 处理取消订单逻辑

        // 关闭弹窗
        this.showApplyReasonPopup = false
        this.reason = this.selectedReason.map((item) => item.label).join(", ") // 更新提示文本
      },

      async submitRequestReason() {
        this.showRequestReasonPopup = false
        if (!this.reasonDesc) {
          uni.showToast({
            title: "请输入问题描述",
            icon: "none"
          })
          return
        }
        if (this.fileList.length === 0) {
          uni.showToast({
            title: "请上传凭证图片",
            icon: "none"
          })
          return
        }
        console.log("提交问题描述:", this.reasonDesc)
        console.log("提交凭证图片:", this.fileList)
        this.requestReason = this.reasonDesc
      },

      onCancelPopupClose() {
        this.showApplyReasonPopup = false
        this.selectedReason = [] // 清空已选的取消原因
        this.reasonColumns.forEach((item) => {
          item.isChecked = false // 重置复选框状态
        })
        this.reason = "请选择退款原因" // 重置提示文本
        console.log("Popup closed, reset selected reasons")
      },

      onRequestReasonPopup() {
        this.showRequestReasonPopup = false
      },

      handleChange(fileList) {
        this.fileList = fileList
        console.log("文件列表更新:", fileList)
      },

      handleRemove({ file = null, fileList = [] }) {
        if (!fileList) {
          console.error("fileList is undefined")
          return
        }

        this.fileList = []
      },

      handleSuccess({ fileList = [] }) {
        console.log("handleSuccess:", fileList)
        const { response = "", size } = fileList[0] || {}
        try {
          const { success, data } = JSON.parse(response) || {}
          console.log("resultInfo success data ", success, data)

          if (!success) {
            return uni.showToast({
              title: "上传失败",
              icon: "none"
            })
          }
          console.log("data >>>", data)
        } catch (err) {
          console.error("handleSuccess error:", err)
        }
      },

      handleProgress({ file }) {
        console.log("handleProgress", file.percent)
      },

      handleOversize({ file }) {
        console.log("handleOversize", file)
        uni.showToast({
          title: "图片不能大于2M",
          icon: "none"
        })
      },

      handleError({ error }) {
        console.log("handleError", error)
        if (error.statusCode === 413) {
          // toast.warning('图片不能大于10M')
          uni.showToast({
            title: "不能大于2M",
            icon: "none"
          })
        }
      },

      onSelectAddress() {
        uni.navigateTo({
          url: "/packages/profile/address/index"
        })
      }
    }
  }
</script>

<style>
  textarea {
    padding: 0 !important;
  }
</style>

<style lang="scss">
  .content {
    display: flex;
    flex-direction: column;
    padding: 30rpx;

    .reason-card {
      display: flex;
      flex-direction: column;
      background-color: #ffffff;
      border-radius: 16px;
      padding: 20rpx 30rpx;

      &__bar {
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid #f0f0f0;
        padding: 10rpx 0;

        &__content {
          display: flex;
          align-items: center;
          font-size: 28rpx;
          flex: 1;
          color: #333333;

          .require {
            color: #ff5722; /* 使用主题色 */
            margin-right: 10rpx;
          }

          .placeholder {
            color: #999999;
            margin-left: 20rpx;
          }
        }
      }

      &__bar-action {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        height: 120rpx;
        border-bottom: none;
        padding: 10rpx 0;

        &__content {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          font-size: 28rpx;
          color: #333333;
          padding: 10rpx 0;
          width: 100%;

          .placeholder {
            flex: 1;
            color: #999999;
          }
        }
      }
    }

    .back-card {
      display: flex;
      flex-direction: column;
      background-color: #ffffff;
      border-radius: 16px;
      padding: 20rpx 30rpx;
      margin-top: 20rpx;

      /* 可以添加更多样式 */
      .back-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        .label {
          font-size: 28rpx;
          color: #333333;
          font-weight: bold;
        }

        .value {
          font-size: 28rpx;
          color: #666666;
        }

        .price-value {
          color: #ff5722; /* 使用主题色 */
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
  .popup-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    font-size: 28rpx;
    color: #333;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
    .popup-item-text {
      line-height: 40rpx;
    }
  }

  .popup-footer {
    display: flex;
    justify-content: center;
    align-items: center;

    .wd-button {
      width: 100%;
      max-width: 600rpx;
      height: 80rpx;
      font-size: 32rpx;
    }
  }

  .popup-request-block {
    display: flex;
    flex-direction: column;
    padding: 20rpx 30rpx;
    background-color: #ffffff;

    .custom-textarea {
      width: 100% !important;
      background-color: #f5f5f5 !important;
      border-radius: 4px !important;
      padding: 10rpx 20rpx !important;
      margin: 0 !important;
      border: none !important;

      .wd-textarea,
      .textarea-wrapper,
      .wd-textarea-inner {
        background: #f5f5f5 !important;
        border: none !important;
        box-shadow: none !important;
        color: #333333 !important;
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
      }

      .textarea-placeholder {
        color: #999999 !important;
      }
    }

    .wd-textarea {
      padding: 0 !important;
    }

    .title {
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 20rpx;
      font-weight: bold;
    }

    .title-bar {
      display: flex;
      align-items: center;
      padding: 20rpx 0;

      .main-title {
        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
      }

      .sub-title {
        font-weight: bold;
        font-size: 28rpx;
        color: #999999;
        margin-left: 10rpx;
      }
    }
  }

  .footer-bar {
    height: 100rpx;
    background-color: #ffffff;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    padding: 20rpx 30rpx env(safe-area-inset-bottom);

    .btn-action {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx 20rpx;
      background-color: #ff5722; /* 使用主题色 */
      border-radius: 30rpx;
      color: #ffffff;
      font-size: 28rpx;
      border: none;
    }
  }
</style>

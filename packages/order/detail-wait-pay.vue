<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-fixed-bar="true"
    :fixed-bar-height="100"
    :show-footer="true"
  >
    <template #nav>
      <my-nav show-icon :iconConfig="iconConfig" title="订单详情" backIcon />
    </template>

    <template #fixedBar>
      <view class="fixed-bar">
        <view class="fixed-bar__tips"
          >还剩 <text class="fixed-bar__time">{{ countDown }}</text> 订单自动取消
        </view>
      </view>
    </template>

    <view class="content">
      <address-bar :address-info="addressInfo" show-icon />
      <view v-if="orderList.length > 0">
        <view v-for="(item, index) in orderList" :key="index" class="order-item-wrapper">
          <order-item
            :order="item"
            :type="item.type"
            :show-amount="false"
            :show-status="false"
            :show-action-bar="false"
          />
        </view>
      </view>
      <order-card :orderInfo="orderInfo" />
      <price-card :price-info="priceInfo" :total-pay="totalPay" />
    </view>

    <template #footer>
      <view class="footer-bar">
        <view class="footer-bar__total-info">
          <text>合计:</text>
          <text class="price">¥{{ totalPay }}</text>
        </view>
        <view class="footer-bar__action">
          <view class="btn-cancel" @tap="onAction('cancel')">取消订单</view>

          <view class="btn-pay" @tap="onAction('pay')">立即支付</view>
        </view>
      </view>
    </template>
  </my-page>
</template>

<script>
  import { formatTime } from "@/common/util"

  // const COUNT_TOTAL = 60 * 60 * 1.5 // 倒计时总秒数
  const COUNT_TOTAL = 30 // 倒计时总秒数，设置为5分钟
  export default {
    data() {
      return {
        iconConfig: {
          color: "#666666",
          size: "22px",
          name: "time"
        },
        addressInfo: {
          name: "张三",
          phone: "13800000000",
          address: "广东省广州市广州市天河路123号"
        },
        orderInfo: {
          orderNo: "35126666778899",
          orderTime: "2024-09-04 10:10:10",
          deliveryType: "快递运输",
          remark: "发货时请选择顺丰",
          payType: "微信支付"
        },
        priceInfo: {
          totalPrice: "100.00",
          chargesPrice: "10.00",
          discountCode: "DISCOUNT2024",
          discountPrice: "5.00"
        },
        totalPay: "90.00",
        countDown: formatTime(COUNT_TOTAL), // 倒计时
        orderList: []
      }
    },
    mounted() {
      this.startCountDown()
    },
    async created() {
      this.orderList = await this.generateOrder()
      console.log("orderList", this.orderList)
    },
    methods: {
      startCountDown() {
        let total = COUNT_TOTAL
        const interval = setInterval(() => {
          if (total <= 0) {
            clearInterval(interval)
            this.countDown = "00:00:00"
            return
          }
          total -= 1
          this.countDown = formatTime(total)
        }, 1000)
      },

      async onAction(type = "") {
        console.log("onAction type >>", type)
        // 模拟支付逻辑
        uni.showToast({
          title: "支付成功",
          icon: "success"
        })
        // 这里可以添加支付成功后的逻辑，比如跳转到订单列表页等

        uni.requestPayment({
          timeStamp: Date.now().toString(),
          nonceStr: Math.random().toString(36).substr(2, 15),
          package: "prepay_id=1234567890",
          signType: "MD5",
          paySign: "abcdefg1234567",
          success: (res) => {
            console.log("支付成功", res)
            uni.navigateBack()
          },
          fail: (err) => {
            console.error("支付失败", err)
            uni.showToast({
              title: "支付失败",
              icon: "none"
            })
          }
        })
      },

      async generateOrder() {
        console.log("generateOrder")
        // 商品模板
        const products = [
          {
            productId: "matepad-pro-13",
            name: "HUAWEI MatePad Pro 13.2英寸 平板电脑 WiFi连接 支持通话",
            specs: { memory: "12GB", storage: "256GB", color: "曜金黑" },
            price: 3999,
            imageUrl: "https://example.com/matepad-pro-13.jpg",
            gifts: [
              {
                name: "平板电脑保护壳",
                quantity: 1,
                imageUrl: "https://example.com/matepad-pro-13.jpg"
              },
              {
                name: "超薄充电器",
                quantity: 1,
                imageUrl: "https://example.com/matepad-pro-13.jpg"
              }
            ],
            isChecked: false
          },
          {
            productId: "matebook-16s-2023",
            name: "HUAWEI MateBook 16s 2023 13代酷睿i5 32G 1T 16英寸高色准",
            specs: {
              processor: "i5",
              memory: "32GB",
              storage: "1TB",
              color: "深空灰"
            },
            price: 7199,
            imageUrl: "https://example.com/matebook-16s.jpg",
            gifts: [
              {
                name: "笔记本电脑保护套",
                quantity: 1,
                imageUrl: "https://example.com/matebook-16s.jpg"
              },
              {
                name: "超薄充电器",
                quantity: 1,
                imageUrl: "https://example.com/matebook-16s.jpg"
              }
            ],
            isChecked: false
          }
        ]

        return Array(4)
          .fill(null)
          .map(() => {
            // 随机选择1-2个商品
            const itemCount = Math.floor(Math.random() * 2) + 1
            const selectedProducts = []
            while (selectedProducts.length < itemCount) {
              const product = products[Math.floor(Math.random() * products.length)]
              if (!selectedProducts.find((p) => p.productId === product.productId)) {
                selectedProducts.push({
                  ...product,
                  quantity: Math.floor(Math.random() * 2) + 1
                })
              }
            }

            // 计算总金额和总数量
            const totalAmount = selectedProducts.reduce((sum, item) => sum + item.price * item.quantity, 0)
            const totalItems = selectedProducts.reduce((sum, item) => sum + item.quantity, 0)

            return {
              orderId: Math.random().toString(36).substr(2, 9),
              status: "待付款",
              type: "payment",
              totalAmount,
              totalItems,
              seller: { name: "华为品牌官方授权", isOfficial: true },
              items: selectedProducts
            }
          })
      }
    }
  }
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    flex-direction: column;
    padding: 30rpx;

    .order-item-wrapper {
      display: flex;
      flex-direction: column;
      padding: 20rpx 0;
    }
  }

  .footer-bar {
    height: 100rpx;
    background-color: #ffffff;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx env(safe-area-inset-bottom);

    &__total-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 28rpx;
      font-weight: bold;
      color: #333333;

      .price {
        color: #ff5722; /* 使用主题色 */
        margin-left: 10rpx;
      }
    }

    &__action {
      display: flex;
      flex: 1;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .btn-cancel {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10rpx 20rpx;
        background-color: #f0f0f0;
        border-radius: 30rpx;
        border: 1px solid #dcdcdc;
        margin-right: 20rpx;
        font-size: 28rpx;
        color: #666666;
      }

      .btn-pay {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10rpx 20rpx;
        background-color: #ff5722; /* 使用主题色 */
        border-radius: 30rpx;
        color: #ffffff;
        font-size: 28rpx;
      }
    }
  }

  .fixed-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    width: 100%;

    &__tips {
      font-size: 28rpx;
      color: #666666;
    }

    &__time {
      color: indianred;
      font-weight: normal;
    }
  }
</style>

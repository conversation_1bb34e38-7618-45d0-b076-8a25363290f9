<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-footer="true"
    :show-back-to-top="true"
  >
    <template #nav>
      <my-nav title="待收货" backIcon />
    </template>

    <view class="content">
      <map
        class="map"
        :latitude="mapConfig.latitude"
        :longitude="mapConfig.longitude"
        :scale="mapConfig.scale"
        enable-3D
        enable-zoom
        enable-rotate
        enable-scroll
        show-compass
        show-scale
        @tap="handleShowLogisticsInfo"
      />
      <view class="wrapper-content">
        <logistics-bar :last-info="lastInfo" :logistics-info="logisticsInfo" @on-show="handleShowLogisticsInfo" />

        <view v-if="orderList.length > 0">
          <view v-for="(item, index) in orderList" :key="index" class="order-item-wrapper">
            <order-item
              :order="item"
              :type="item.type"
              :show-amount="false"
              :show-status="false"
              :show-action-bar="false"
            />
          </view>
        </view>
        <order-card :orderInfo="orderInfo" />
        <price-card :price-info="priceInfo" :total-pay="totalPay" />
      </view>
    </view>

    <template #footer>
      <view class="footer-bar">
        <view class="btn-cancel" @tap="onAction('buyAgain')">再来一单</view>
        <view class="btn-cancel" @tap="onAction('refund')">退货/售后</view>
        <view class="btn-pay" @tap="onAction('confirmGet')">确认收货</view>
      </view>
    </template>
  </my-page>
</template>

<script>
  export default {
    data() {
      return {
        orderInfo: {
          orderNo: "35126666778899",
          orderTime: "2024-09-04 10:10:10",
          deliveryType: "快递运输",
          remark: "发货时请选择顺丰",
          payType: "微信支付"
        },
        priceInfo: {
          totalPrice: "100.00",
          chargesPrice: "10.00",
          discountCode: "DISCOUNT2024",
          discountPrice: "5.00"
        },
        totalPay: "90.00",
        orderList: [],
        lastInfo: {
          title: "运输中",
          desc: "当前位置在【XX市】XX物流集散中心，即将发往XX分拨中心",
          time: "2024-9-8 10:00:00"
        },
        logisticsInfo: {
          name: "张三",
          phone: "132****0350",
          address: "北京市朝阳区XXX街道XX小区001号110"
        },
        mapConfig: {
          latitude: 39.9042, // 北京市纬度
          longitude: 116.4074, // 北京市经度
          scale: 14, // 缩放级别
          markers: [
            {
              id: 1,
              latitude: 39.9042,
              longitude: 116.4074,
              title: "当前位置",
              iconPath: "/static/order/ic-location.png",
              width: 30,
              height: 30
            }
          ],
          polyline: [
            {
              points: [
                { latitude: 39.9042, longitude: 116.4074 },
                { latitude: 39.915, longitude: 116.404 }
              ],
              color: "#FF0000DD",
              width: 2,
              dottedLine: false
            }
          ]
        }
      }
    },
    mounted() {},
    async created() {
      this.orderList = await this.generateOrder()
      console.log("orderList", this.orderList)
    },
    methods: {
      async onAction(type = "") {
        console.log("onAction type >>", type)
        // 这里可以添加支付成功后的逻辑，比如跳转到订单列表页等
      },

      async generateOrder() {
        console.log("generateOrder")
        // 商品模板
        const products = [
          {
            productId: "matepad-pro-13",
            name: "HUAWEI MatePad Pro 13.2英寸 平板电脑 WiFi连接 支持通话",
            specs: { memory: "12GB", storage: "256GB", color: "曜金黑" },
            price: 3999,
            imageUrl: "https://example.com/matepad-pro-13.jpg",
            gifts: [
              {
                name: "平板电脑保护壳",
                quantity: 1,
                imageUrl: "https://example.com/matepad-pro-13.jpg"
              },
              {
                name: "超薄充电器",
                quantity: 1,
                imageUrl: "https://example.com/matepad-pro-13.jpg"
              }
            ],
            isChecked: false
          },
          {
            productId: "matebook-16s-2023",
            name: "HUAWEI MateBook 16s 2023 13代酷睿i5 32G 1T 16英寸高色准",
            specs: {
              processor: "i5",
              memory: "32GB",
              storage: "1TB",
              color: "深空灰"
            },
            price: 7199,
            imageUrl: "https://example.com/matebook-16s.jpg",
            gifts: [
              {
                name: "笔记本电脑保护套",
                quantity: 1,
                imageUrl: "https://example.com/matebook-16s.jpg"
              },
              {
                name: "超薄充电器",
                quantity: 1,
                imageUrl: "https://example.com/matebook-16s.jpg"
              }
            ],
            isChecked: false
          }
        ]

        return Array(3)
          .fill(null)
          .map(() => {
            // 随机选择1-2个商品
            const itemCount = Math.floor(Math.random() * 2) + 1
            const selectedProducts = []
            while (selectedProducts.length < itemCount) {
              const product = products[Math.floor(Math.random() * products.length)]
              if (!selectedProducts.find((p) => p.productId === product.productId)) {
                selectedProducts.push({
                  ...product,
                  quantity: Math.floor(Math.random() * 2) + 1
                })
              }
            }

            // 计算总金额和总数量
            const totalAmount = selectedProducts.reduce((sum, item) => sum + item.price * item.quantity, 0)
            const totalItems = selectedProducts.reduce((sum, item) => sum + item.quantity, 0)

            return {
              orderId: Math.random().toString(36).substr(2, 9),
              status: "待付款",
              type: "payment",
              totalAmount,
              totalItems,
              seller: { name: "华为品牌官方授权", isOfficial: true },
              items: selectedProducts
            }
          })
      },

      handleShowLogisticsInfo() {
        // 处理显示物流信息的逻辑
        uni.showToast({
          title: "显示物流信息",
          icon: "none"
        })
      }
    }
  }
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    flex-direction: column;

    .map {
      width: 100%;
      height: 400rpx;
    }

    .wrapper-content {
      display: flex;
      flex-direction: column;
      padding: 20rpx 30rpx;

      .order-item-wrapper {
        display: flex;
        flex-direction: column;
        padding: 20rpx 0;
      }
    }
  }
  .footer-bar {
    height: 100rpx;
    background-color: #ffffff;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: flex-end;
    padding: 20rpx 30rpx env(safe-area-inset-bottom);

    .btn-cancel {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx 20rpx;
      background-color: #f0f0f0;
      border-radius: 30rpx;
      border: 1px solid #dcdcdc;
      margin-right: 20rpx;
      font-size: 28rpx;
      color: #666666;
    }

    .btn-pay {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10rpx 20rpx;
      background-color: #ff5722; /* 使用主题色 */
      border-radius: 30rpx;
      color: #ffffff;
      font-size: 28rpx;
      border: none;
    }
  }
</style>

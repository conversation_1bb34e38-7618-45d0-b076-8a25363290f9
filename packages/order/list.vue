<template>
	<view class="wrapper">
		<my-nav title="订单列表" backIconColor="black" textColor="#000" backIcon />

		<wd-tabs id="tabs" inactiveColor="#000" color="#C6A670" animated :active="active" @click="onTabClick">
			<wd-tab title="全部" name="all" />
			<wd-tab title="待付款" name="payment" />
			<wd-tab title="待发货" name="delivery" />
			<wd-tab title="待收货" name="received" />
			<wd-tab title="已完成" name="completed" />
			<wd-tab title="待开票" name="invoice" />
			<wd-tab title="已取消" name="cancelled" />
		</wd-tabs>

		<!--  -->
		<scroll-view scroll-y scroll-with-animation refresher-enabled :scroll-top="scrollTop"
			refresher-default-style="black" :refresher-triggered="refreshing" lower-threshold="50" upper-threshold="50"
			refresher-threshold="100" refresher-background="transparent" @refresherrefresh="onRefresh"
			@refresherrestore="onRestore" @refresherabort="onAbort" @scrolltolower="onLoadMore"
			:style="{ height: scrollHeight + 'px' }">
			<view v-for="(item, index) in orderList" :key="index" class="order-item-wrapper">
				<order-item :order="item" :type="item.type" @on-show-detail="showDetail" @on-cancel-order="cancelOrder(item)"
					@on-modify-address="modifyAddress" @on-edit-address="editAddress" @on-pay-now="payNow(item)"
					@on-refund="refund(item)" @on-apply-invoice="applyInvoice(item)" @on-buy-again="buyAgain"
					@on-apply-refund="applyRefund(item)" @on-confirm-get="confirmGet(item)" @on-delete-order="deleteOrder(item)"
					@on-show-invoice="showInvoice" />
			</view>
			<view class="loading-wrapper" v-if="loading">
				<view class="loading">
					<text class="loading-text">正在加载更多</text>
					<text class="dots"></text>
				</view>
			</view>
		</scroll-view>

		<my-popup :visible="showCancelPopup" title="请选择取消原因(必选)" showTopBar showFooter @close="onCancelPopupClose">
			<template #topBar>
				<view> 温馨提示：取消订单后，订单将无法恢复，已用抵现积分，优惠券将原路返还 </view>
			</template>

			<template #content>
				<view class="popup-item" v-for="(item, index) in cancelColumns" :key="index">
					<view class="popup-item-text">{{ item.label }}</view>
					<wd-checkbox v-model="item.isChecked" @change="selectReason(item)" />
				</view>
			</template>

			<template #footer>
				<view class="popup-footer">
					<wd-button type="primary" @click="submitCancelReason"> 提交 </wd-button>
				</view>
			</template>
		</my-popup>

		<my-popup :visible="showRefundPopup" title="请选择退款/售后商品" showTopBar showFooter @close="onCancelRefundPopupClose">
			<template #topBar>
				<view class="refund-select__view">
					<!-- <view class="refund-select__view__title">请选择收货状态</view>
					<view class="refund-select__view__items">
						<view class="refund-select__view__items__item" v-for="item in receivingColumns"
							:key="item.index" :class="item.isChecked ? 'is-active' : 'not-active'"
							@click="setReceivingStatus(item.index)">
							<text class="refund-select__view__items__item__text">{{ item.label }}</text>
						</view>
					</view>

					<view class="refund-select__view__title">请选择售后类型</view> -->
					<view class="refund-select__view__items" v-if="needRefund">
						<view class="refund-select__view__items__item" :class="item.isChecked ? 'is-active' : 'not-active'"
							v-for="item in receivingChildren1[currentRefundIndex].items" :key="item.index"
							@click="setReceivingChildStatus(item.index, item.value)">
							<view class="refund-select__view__items__item__text">{{ item.label }}</view>
						</view>
					</view>
					
					<view class="refund-select__view__items" v-else>
						<view class="refund-select__view__items__item" :class="item.isChecked ? 'is-active' : 'not-active'"
							v-for="item in receivingChildren[currentRefundIndex].items" :key="item.index"
							@click="setReceivingChildStatus(item.index, item.value)">
							<view class="refund-select__view__items__item__text">{{ item.label }}</view>
						</view>
					</view>

					<view class="refund-select__view__title">请选择商品</view>
				</view>
			</template>

			<template #content>
				<!-- <view class="popup-item" v-for="(item, index) in orderList" :key="index">
					<order-check :order="item" @refundItemCheck="onRefundItemCheck" />
				</view> -->
				<view v-if="refundItem" class="goods_view">
					<view v-for="goods in refundItem?.orderDetails" class="goods_top">
						<view class="goods-check">
							<wd-checkbox v-model="goods.isChecked" />
						</view>
						<view class="goods-image">
							<image :src="goods.image" mode="widthFix" />
						</view>
						<view class="goods-option">
							<view class="option-title">{{ goods.productName }}</view>
							<view class="os-step">
								<text>数量 ×{{ goods.num }}</text>
							</view>
							<view class="option-step">
								¥{{ goods.price }}
							</view>
						</view>
					</view>
				</view>

			</template>

			<template #footer>
				<view class="popup-footer-selector">
					<view class="footer-check-box">
						<wd-checkbox v-model="applyRefundSelected" @change="setApplyRefundStatus()">
							<text>全选</text>
						</wd-checkbox>
					</view>

					<wd-button type="primary" @click="submitRefundNext"> 下一步 </wd-button>
				</view>
			</template>
		</my-popup>

		<my-popup :visible="showApplyInvoicePopup" title="发票信息" showTopBar showFooter @close="onCancelInvoicePopupClose">
			<template #topBar>
				<view class="refund-select__view">
					<view class="refund-select__view__title">发票类型</view>
					<view class="refund-select__view__items">
						<view class="refund-select__view__items__item" v-for="item in invoiceType" :key="item.index"
							:class="item.isChecked ? 'is-active' : 'not-active'" @click="setInvoiceType(item.index)">
							<text class="refund-select__view__items__item__text">{{ item.label }}</text>
						</view>
					</view>

					<view class="refund-select__view__title_bar">
						<view class="title">发票抬头</view>
						<view class="right-bar" @tap="onAddWechatInvoice">
							<image src="/static/shop/ic-weixin.png" mode="aspectFit" class="icon" />
							<text>获取微信发票抬头</text>
						</view>
					</view>
					<view class="refund-select__view__items">
						<view class="refund-select__view__items__item" :class="item.isChecked ? 'is-active' : 'not-active'"
							v-for="item in invoiceTitle[currentInvoiceIndex].items" :key="item.index"
							@click="setInvoiceChildStatus(item.index, item.value)">
							<view class="refund-select__view__items__item__text">{{ item.label }}</view>
						</view>
					</view>
				</view>
			</template>

			<template #content>
				<view class="invoice-wrapper">
					<wd-input v-model="invoiceForm.headName" placeholder="请填写发票抬头名称，如姓名" label="抬头名称" required
						label-width="170rpx"></wd-input>
					<wd-input v-if="invoiceForm.type === 'company'" v-model="invoiceForm.taxNumber" placeholder="请填写纳税人识别号"
						label="纳税人识别号" inputmode="numeric" label-width="170rpx" />
					<wd-input v-if="invoiceForm.type === 'company'" v-model="invoiceForm.invoiceAddress" placeholder="请填写公司地址"
						label="公司地址" label-width="170rpx" />
					<wd-input v-if="invoiceForm.type === 'company'" v-model="invoiceForm.invoicePhone" placeholder="请填写公司电话"
						label="公司电话" inputmode="tel" label-width="170rpx" />
					<wd-input v-if="invoiceForm.type === 'company'" v-model="invoiceForm.invoiceBank" placeholder="请填写开户银行名称"
						label="开户银行名称" label-width="170rpx" />
					<wd-input v-if="invoiceForm.type === 'company'" v-model="invoiceForm.invoiceBankNo" placeholder="请填写银行账号"
						label="银行账号" inputmode="numeric" label-width="170rpx" />
					<view class="title">收票人信息</view>
					<wd-input v-model="invoiceForm.receivePhone" placeholder="请填写收票人手机号" label="收票人手机号" inputmode="tel"
						label-width="170rpx"></wd-input>
					<wd-input v-model="invoiceForm.receiveEmail" placeholder="请填写收票人邮箱" inputmode="email" label="收票人邮箱"
						label-width="170rpx" />
					<view class="title">发票内容</view>
					<text class="tips">发票内容将显示详细商品名称与价格信息，发票金额为实际支付，不含优惠扣减金额。</text>
					<wd-input v-if="invoiceForm.type === 'company'" v-model="invoiceForm.remark"
						placeholder="可填写备注信息，此备注将在发票上显示，请谨慎填写。" label="备注" label-width="170rpx" />
				</view>
			</template>

			<template #footer>
				<view class="popup-footer-bar">
					<view class="btn-action" @tap="onAction('invoice')">确定</view>
				</view>
			</template>
		</my-popup>
	</view>
</template>

<script>
	import {
		px2rpx
	} from "@/common/util"
	import OrderItem from "@/components/order-item/order-item.vue"
	import OrderCheck from "@/components/order-swtich/order-check.vue"
	import {
		getOrderList,
		cancelOrder,
		removeOrder,
		placeWeixinOrder,
		orderConfirmOrderReceivedt,
		applyOpenBlueInvoice
	} from "@/api/order.js"

	// 取消订单原因
	const REFUND_REASON = [{
			label: "选错商品",
			value: "01"
		},
		{
			label: "重复下单",
			value: "02"
		},
		{
			label: "商品质量问题",
			value: "03"
		},
		{
			label: "商品缺货",
			value: "05"
		},
		{
			label: "商品价格错误",
			value: "06"
		},
		{
			label: "商品描述不符",
			value: "07"
		},
		{
			label: "其他原因",
			value: "08"
		}
	]

	// 收货状态
	const RECEIVING_STATUS = [{
			label: "未收到货",
			value: "01"
		},
		{
			label: "已收到货",
			value: "02"
		}
	]

	// 发票类型
	const INVOICE_TYPE = [{
			label: "电子普通发票",
			value: "normal"
		},
		{
			label: "专用发票",
			value: "special"
		}
	]

	// 订单状态列表
	const TAB_LIST = [{
			title: "待付款",
			type: "payment"
		},
		{
			title: "待发货",
			type: "delivery"
		},
		{
			title: "待收货",
			type: "received"
		},
		{
			title: "已完成",
			type: "completed"
		},
		{
			title: "待开票",
			type: "invoice"
		},
		{
			title: "已取消",
			type: "cancelled"
		}
	]

	/**
	 * 订单列表页面 api
	 * /store/order/placeOrder
	 * /store/order/appList
	 * /store/order/cancle
	 * /store/apply/applyOpenBlueInvoice
	 * /store/apply/getBlueInvoiceInfoByOrderId
	 * /store/order/remove
	 * /store/order/getInfo
	 * /store/apply/getBlueInvoiceInfoByOrderId
	 */
	export default {
		name: "list",
		components: {
			OrderItem,
			OrderCheck
		},

		data() {
			return {
				needRefund: false,
				active: "all",
				loading: false,
				refundItem: null,
				orderList: [],
				tabList: [{
						title: "全部",
						type: "all",
						num: ''
					},
					{
						title: "待付款",
						type: "payment",
						num: 0
					},
					{
						title: "待发货",
						type: "delivery",
						num: 1
					},
					{
						title: "待收货",
						type: "received",
						num: 2
					},
					{
						title: "已完成",
						type: "completed",
						num: 3
					},
					{
						title: "待开票",
						type: "invoice",
						num: 4
					},
					{
						title: "已取消",
						type: "cancelled",
						num: 5
					}
				],

				refreshing: false,
				scrollHeight: 0,
				scrollTop: 0,

				showCancelPopup: false,
				cancelColumns: REFUND_REASON.map((item, index) => {
					return {
						label: item.label,
						value: item.value,
						isChecked: false,
						index: index
					}
				}),
				selectedReason: [],

				showRefundPopup: false,
				receivingColumns: RECEIVING_STATUS.map((item, index) => {
					return {
						label: item.label,
						value: item.value,
						// 默认第一项选择
						isChecked: index === 0,
						index
					}
				}),
				receivingChildren1: [{
						value: "01",
						items: [{
							label: "退货",
							value: "10",
							isChecked: true,
							index: 0
						}]
					},
					{
						value: "02",
						items: [{
								label: "退货退款",
								value: "20",
								isChecked: true,
								index: 1
							},
							{
								label: "换货",
								value: "21",
								isChecked: false,
								index: 2
							}
						]
					}
				],
				receivingChildren: [{
						value: "01",
						items: [{
							label: "仅退款",
							value: "10",
							isChecked: true,
							index: 0
						}]
					},
					{
						value: "02",
						items: [{
								label: "退货退款",
								value: "20",
								isChecked: true,
								index: 1
							},
							{
								label: "换货",
								value: "21",
								isChecked: false,
								index: 2
							}
						]
					}
				],
				currentRefundIndex: 0,
				currentOrderId: null, // 当前操作的订单ID

				applyRefundSelected: false,

				showApplyInvoicePopup: false,
				invoiceType: INVOICE_TYPE.map((item, index) => {
					return {
						label: item.label,
						value: item.value,
						isChecked: index === 0, // 默认选中第一个
						index
					}
				}),
				invoiceTitle: [{
						value: "normal",
						items: [{
								label: "个人",
								value: "personal",
								isChecked: true,
								index: 0
							},
							{
								label: "单位",
								value: "company",
								isChecked: false,
								index: 1
							}
						]
					},
					{
						value: "special",
						items: [{
							label: "单位",
							value: "company",
							isChecked: true,
							index: 0
						}]
					}
				],
				currentInvoiceIndex: 0,

				invoiceForm: {
					orderId: '',
					headName: '',
					taxNumber: '',
					invoiceAddress: '',
					invoicePhone: '',
					invoiceBank: '',
					invoiceBankNo: '',
					receivePhone: '',
					receiveEmail: '',
					remark: '',
					type: "personal", // 默认电子普通发票
				}
			}
		},

		created() {
			this.getHeight()
		},

		onLoad(options = {}) {
			console.log("options", options)
			if (options.type) {
				// 检查传入的type是否在tabList中
				const isValidType = this.tabList.some((tab) => tab.type === options.type)
				if (isValidType) {
					this.active = options.type
				}
			}
		},

		mounted() {
			this._getOrderList()
		},

		watch: {
			active(val) {
				console.log("active", val)
				if (val === this.active) {
					return
				}
				// 当active改变时，重置列表并获取新数据
				this.orderList = []
				this.refreshing = true
				this.$nextTick(() => {
					this.scrollTop = 0
					this._getOrderList()
				})
			}
		},

		methods: {
			// 获取订单列表
			async _getOrderList() {
				this.needRefund = false
				try {
					// 
					let str = ''
					for (let i of this.tabList) {
						if (i.type === this.active) {
							console.log(i)
							str = i.num
						}
					}
					console.log(str)
					const params = {
						page: 1,
						size: 50,
						orderStatus: str
					}

					// 根据当前选中的tab设置状态参数
					// if (this.active !== "all") {
					//   params.status = this.active
					// }

					const result = await getOrderList(params)
					const {
						code,
						msg,
						data,
						rows
					} = result || {}
					const orderData = data

					if (code === 200 && orderData && orderData.length > 0) {
						for (let i of orderData) {
							let typeArr = ['payment', 'delivery', 'received', 'completed', 'invoice', 'cancelled']
							i.type = typeArr[i.orderStatus]
						}
						this.orderList = orderData
						// 成功获取订单数据，转换数据格式
						// this.orderList = this._transformOrderData(orderData)
						console.log("订单列表加载成功:", this.orderList.length, "个订单")
					} else {
						console.log("订单列表接口返回空数据，使用默认数据:", msg)
						// 保持原有的 mock 数据或显示空状态
						this.orderList = []
					}
				} catch (error) {
					console.log("获取订单列表失败，使用默认数据:", error)
					// 保持原有的 mock 数据或显示空状态
					this.orderList = []
				} finally {
					this.loading = false
					this.refreshing = false
				}
			},

			// 转换订单数据格式
			_transformOrderData(orderData) {
				return orderData.map((order) => ({
					orderId: order.id || order.orderId,
					orderNo: order.orderNo || order.orderNumber,
					status: order.status || "pending",
					type: this._getOrderType(order.status),
					createTime: order.createTime || new Date().toISOString(),
					totalAmount: order.totalAmount || order.amount || 0,
					items: order.items || order.products || [],
					address: order.address || {},
					// 其他订单相关字段
					canCancel: order.canCancel || false,
					canPay: order.canPay || false,
					canRefund: order.canRefund || false
				}))
			},

			// 根据订单状态获取订单类型
			_getOrderType(status) {
				const statusMap = {
					pending: "payment",
					paid: "delivery",
					shipped: "received",
					completed: "completed",
					cancelled: "cancelled",
					invoice: "invoice"
				}
				return statusMap[status] || "payment"
			},

			// 取消订单
			async _cancelOrder(orderId, reason) {
				try {
					const {
						code,
						msg
					} = await cancelOrder({
						orderId,
						reason: reason || "用户取消"
					})
					if (code === 200) {
						uni.showToast({
							title: "订单取消成功",
							icon: "success"
						})
						// 重新加载订单列表
						this._getOrderList()
					} else {
						uni.showToast({
							title: msg || "取消失败",
							icon: "none"
						})
					}
				} catch (error) {
					console.log("取消订单失败:", error)
					uni.showToast({
						title: "取消失败",
						icon: "none"
					})
				}
			},

			// 删除订单
			async _deleteOrder(orderId) {
				try {
					const {
						code,
						msg
					} = await removeOrder({
						orderIds: [orderId]
					})
					if (code === 200) {
						uni.showToast({
							title: "删除成功",
							icon: "success"
						})
						// 重新加载订单列表
						this._getOrderList()
					} else {
						uni.showToast({
							title: msg || "删除失败",
							icon: "none"
						})
					}
				} catch (error) {
					console.log("删除订单失败:", error)
					uni.showToast({
						title: "删除失败",
						icon: "none"
					})
				}
			},

			onTabClick({
				index,
				name
			}) {
				this.active = name
				// 先重置数据和滚动位置
				this.orderList = []
				this.refreshing = true
				// 强制DOM更新后再设置scrollTop
				this.$nextTick(() => {
					this.scrollTop = 0
					// 获取新数据
					this._getOrderList()
				})
			},

			// 在 methods 中添加
			selectReason(selectedItem) {
				console.log("selectReason", selectedItem)
				const {
					label,
					value,
					isChecked
				} = selectedItem || {}
				if (isChecked) {
					// 如果选中，添加到 selectedReason
					this.selectedReason.push({
						label,
						value
					})
				} else {
					// 如果取消选中，从 selectedReason 中移除
					this.selectedReason = this.selectedReason.filter((item) => item.value !== value)
				}
			},

			async submitCancelReason() {
				if (this.selectedReason.length === 0) {
					uni.showToast({
						title: "请选择取消原因",
						icon: "none"
					})
					return
				}

				console.log("提交取消原因:", this.selectedReason)

				// 获取取消原因文本
				const reasonText = this.selectedReason.map((item) => item.label).join(", ")

				// 调用取消订单API
				if (this.currentOrderId) {
					await this._cancelOrder(this.currentOrderId, reasonText)
				}

				// 关闭弹窗
				this.showCancelPopup = false
				// 重置选择的原因
				this.selectedReason = []
				this.currentOrderId = null
			},

			onCancelPopupClose() {
				this.showCancelPopup = false
				this.selectedReason = [] // 清空已选的取消原因
				if (this.cancelColumns.length > 0) {
					this.cancelColumns.forEach((item) => {
						item.isChecked = false // 重置复选框状态
					})
				}
				console.log("Popup closed, reset selected reasons")
			},

			onCancelRefundPopupClose() {
				this.showRefundPopup = false
				console.log("Refund popup closed")
			},

			onCancelInvoicePopupClose() {
				this.showApplyInvoicePopup = false
				console.log("Invoice popup closed")
			},

			onRefundItemCheck(item) {
				console.log("onRefundItemCheck", item)
				// 处理退款商品选中逻辑
				// 这里可以根据业务需求进行处理
			},

			// 设置全选状态
			setApplyRefundStatus() {
				console.log("setApplyRefundStatus", this.applyRefundSelected)
				// 更新所有商品的选中状态
				this.refundItem.orderDetails.forEach((order) => {
					order.isChecked = this.applyRefundSelected
				})
			},

			// 提交退款下一步
			submitRefundNext() {

				// console.log("submitRefundNext", this.receivingColumns, this.receivingChildren[this.currentRefundIndex]
				// 	.items)
				// // 检查是否有选中的退款状态和子项
				// const selectedReceiving = this.receivingColumns.find((item) => item.isChecked)
				// const selectedChild = this.receivingChildren[this.currentRefundIndex].items.find((item) => item.isChecked)
				// if (!selectedReceiving || !selectedChild) {
				// 	uni.showToast({
				// 		title: "请选择收货状态和售后类型",
				// 		icon: "none"
				// 	})
				// 	return
				// }
				const selectedChild = this.refundItem.orderDetails.find((item) => item.isChecked)
				if (selectedChild) {
					let obj = JSON.parse(JSON.stringify(this.refundItem))
					let arr = []
					for (let i of obj.orderDetails) {
						if (i.isChecked) {
							arr.push(i)
						}
					}
					obj.orderDetails = arr
					uni.setStorageSync('refundObj', JSON.stringify(obj))
					console.log(this.needRefund)
					if(this.needRefund) {
						return uni.navigateTo({
							url: `/packages/order/detail-exchange`
						})
					} else {
						return uni.navigateTo({
							url: `/packages/order/refund-apply`
						})
					}
					
					// return uni.navigateTo({
					// 	url: `/packages/order/refund-apply?status=${selectedReceiving.value}&type=${selectedChild.value}`
					// })
				}
			},

			// 在 methods 中添加或修改
			setReceivingStatus(index) {
				// 更新当前选中的退款索引
				this.currentRefundIndex = index

				// 更新选中状态，确保只有一项被选中
				this.receivingColumns.forEach((item, i) => {
					item.isChecked = i === index
				})
			},

			setInvoiceType(index) {
				console.log("setInvoiceType", index)
				// 更新当前选中的发票类型索引
				this.currentInvoiceIndex = index
				if (index === 1) {
					this.invoiceForm.type = "company"
				}

				// 更新选中状态，确保只有一项被选中
				this.invoiceType.forEach((item, i) => {
					item.isChecked = i === index
				})
			},

			setReceivingChildStatus(index, value) {
				// 更新当前选中的退款子项
				this.receivingChildren[this.currentRefundIndex].items.forEach((item) => {
					item.isChecked = item.value === value
				})
			},

			setInvoiceChildStatus(index, value) {
				console.log("setInvoiceChildStatus", index, value)
				if (index === 1) {
					this.invoiceForm.type = "company"
				} else {
					this.invoiceForm.type = "personal"
				}
				// 更新当前选中的发票子项
				this.invoiceTitle[this.currentInvoiceIndex].items.forEach((item) => {
					item.isChecked = item.value === value
				})
			},

			onAddWechatInvoice() {
				uni.chooseInvoiceTitle({
					success: (res) => {
						console.log(res)
						this.invoiceForm.headName = res.title
						this.invoiceForm.invoiceAddress = res.companyAddress
						this.invoiceForm.invoiceBank = res.bankName
						this.invoiceForm.invoiceBankNo = res.bankAccount
						this.invoiceForm.invoicePhone = res.telephone
						this.invoiceForm.taxNumber = res.taxNumber
						this.invoiceForm.type = res.type == '0' ? 'company' : 'personal'
						this.invoiceTitle[this.currentInvoiceIndex].items.forEach((item) => {
							item.isChecked = item.value === this.invoiceForm.type
						})
					},
					fail: (err) => {
						console.error('获取发票抬头失败', err);
						// 错误处理：用户取消选择或其他错误
						if (err.errMsg.includes('cancel')) {
							uni.showToast({
								title: '已取消选择',
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: '获取失败，请重试',
								icon: 'none'
							});
						}
					}
				});
			},

			onRefresh() {
				if (this.refreshing) return
				this.refreshing = true
				this._getOrderList()
			},

			onRestore() {
				console.log("onRestore")
				this.refreshing = false
			},

			onAbort() {
				console.log("onAbort")
				this.refreshing = false
			},

			async onLoadMore() {
				// if (this.loading) return
				// this.loading = true

				// this.orderList = [...this.orderList, ...newOrders]
				// this.loading = false
			},

			showDetail(item) {
				console.log("showDetail", item)
				item = {
					...item,
					orderId: "1234", // 假设orderId是唯一标识符
					type: "all" // 确保有type字段
				}
				return uni.navigateTo({
					url: `/packages/shop/info?orderId=${item.orderId}&type=${item.type}`
				})
			},

			cancelOrder(val) {
				this.currentOrderId = val.orderId
				this.showCancelPopup = true
			},

			// 申请发票
			applyInvoice(val) {
				this.invoiceForm.orderId = val.orderId
				this.showApplyInvoicePopup = true
			},
			
			// 退款
			refund(item) {
				console.log(121111)
				this.needRefund = true;
				let val = JSON.parse(JSON.stringify(item))
				for (let i of val.orderDetails) {
					i.isChecked = false
				}
				this.refundItem = val
				this.showRefundPopup = true
			},
			

			// 申请退款
			applyRefund(item) {
				console.log(item)
				let val = JSON.parse(JSON.stringify(item))
				for (let i of val.orderDetails) {
					i.isChecked = false
				}
				this.refundItem = val
				this.showRefundPopup = true
			},

			async confirmGet(val) {

				uni.showModal({
					title: "确认收货",
					content: "为了保障您的权益，请收到商品确认无误后再确认收货",
					confirmText: "确认收货",
					cancelText: "取消",
					success: async (res) => {
						if (res.confirm) {
							const res = await orderConfirmOrderReceivedt({
								orderId: val.orderId
							})
							if (res.code === 200) {
								uni.showToast({
									title: "收货成功",
									icon: "success"
								})
								this._getOrderList()
							} else {
								uni.showToast({
									title: res.msg,
									icon: "none"
								})
							}
						} else if (res.cancel) {
							console.log("用户取消收货")
						}
					},
					fail: (err) => {
						console.error("确认收货失败", err)
					}
				})
			},

			deleteOrder(val) {
				uni.showModal({
					title: "确认删除此订单吗？",
					content: "订单删除后将无法恢复，请谨慎操作！",
					confirmText: "删除",
					cancelText: "取消",
					success: (res) => {
						if (res.confirm) {
							this._deleteOrder(val.orderId)
						} else if (res.cancel) {
							console.log("用户取消删除订单")
						}
					},
					fail: (err) => {
						console.error("删除订单失败", err)
					}
				})
			},

			showInvoice() {
				console.log("showInvoice")
				uni.navigateTo({
					url: "/packages/order/detail-invoice"
				})
			},

			// 再来一单
			buyAgain() {
				return uni.switchTab({
					url: "/pages/shoppingCart/index"
				})
			},

			modifyAddress() {
				uni.navigateTo({
					url: "/packages/profile/address/edit"
				})
			},

			editAddress() {
				uni.navigateTo({
					url: "/packages/profile/address/edit"
				})
			},

			onAction(action = "") {
				console.log("onAction", action)
				if (action === "invoice") {
					// 提交发票信息
					this.submitInvoice()
				}
			},

			async submitInvoice() {
				let submirObj = JSON.parse(JSON.stringify(this.invoiceForm))
				submirObj.headType = submirObj.type === "company" ? '1' : '0'
				submirObj.invoiceType = this.currentInvoiceIndex === 0 ? '82' : '81'
				const res = await applyOpenBlueInvoice(submirObj)
				if(res.code === 200) {
					uni.showToast({
						title: res.msg,
						icon: "success"
					})
					this.showApplyInvoicePopup = false
					this._getOrderList()
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})
				}
				// console.log("submitInvoice", this.invoiceForm)
			},

			async payNow(val) {
				const res2 = await placeWeixinOrder({
					orderId: val.orderId
				})
				if (res2.code === 200) {
					res2.data.package = res2.data.packageValue
					uni.requestPayment({
						...res2.data,
						success: (res) => {
							uni.showToast({
								title: "支付成功",
								icon: "success"
							})
						},
						fail: (err) => {
							console.log(err)
							uni.showToast({
								title: "支付失败",
								icon: "none"
							})
						}
					})
				} else {
					uni.showToast({
						title: res2.msg,
						icon: "none"
					})
				}
			},

			getHeight() {
				const {
					safeArea: {
						height: safeAreaHeight
					}
				} = uni.getWindowInfo()
				const navHeight = uni.getStorageSync("navHeight")
				this.t2bHeight = uni.upx2px(px2rpx(navHeight))

				uni
					.createSelectorQuery()
					.select("#tabs")
					.boundingClientRect()
					.exec((res) => {
						if (res && res[0]) {
							let tabsHeight = res[0].height || 0
							tabsHeight = uni.upx2px(px2rpx(tabsHeight))
							this.scrollHeight = safeAreaHeight - this.t2bHeight - tabsHeight
						} else {
							this.scrollHeight = safeAreaHeight - this.t2bHeight
						}
					})
			}
		}
	}
</script>

<style scoped lang="scss">
	.goods_view {
		.goods_top {
			display: flex;
			align-items: center;
			justify-content: center;

			.goods-check {
				width: 80rpx;
				text-align: center;
			}

			.goods-image {
				width: 150rpx;
				height: 150rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 30rpx;

				>image {
					width: 100%;
					max-height: 180rpx;
				}
			}

			.goods-option {
				flex: 1;

				.option-title {
					font-weight: bold;
				}

				.os-step {
					font-size: 26rpx;
					color: #666;
					margin: 15rpx 0;
				}
			}
		}
	}

	.wrapper {
		padding-bottom: env(safe-area-inset-bottom);
		background: #f5f5f5;
		height: 100vh;
		display: flex;
		flex-direction: column;

		.popup-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
			font-size: 28rpx;
			color: #333;
			border-bottom: 1rpx solid #f0f0f0;

			&:last-child {
				border-bottom: none;
			}

			.popup-item-text {
				line-height: 40rpx;
			}
		}

		.popup-footer {
			display: flex;
			justify-content: center;
			align-items: center;

			.wd-button {
				width: 100%;
				max-width: 600rpx;
				height: 80rpx;
				font-size: 32rpx;
			}
		}

		.popup-footer-selector {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.footer-check-box {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #333;

				.wd-checkbox {
					margin-right: 10rpx;
				}
			}
		}

		.refund-select {
			display: flex;
			flex-direction: column;

			&__view {
				background-color: #fff;

				&__title {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
					margin-bottom: 10rpx;
				}

				&__title_bar {
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 10rpx;

					.title {
						font-size: 28rpx;
						color: #333;
						font-weight: 500;
					}

					.right-bar {
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: flex-end;
						font-size: 28rpx;
						color: #333;
						font-weight: 500;

						.icon {
							margin-right: 10rpx;
							width: 36rpx;
							height: 36rpx;
						}
					}
				}

				&__items {
					display: flex;
					flex-direction: row;
					align-items: center;
					flex-wrap: wrap; // 添加这一行实现自动换行
					gap: 20rpx; // 添加间距
					margin-bottom: 20rpx; // 与下一个标题保持距离

					&__item {
						// 非必要，但建议设置宽度让布局更美观
						max-width: calc(50% - 10rpx); // 两列布局
						// 或者 max-width: calc(33.33% - 14rpx); // 三列布局

						// 现有样式保持不变
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 30rpx;
						padding: 10rpx 20rpx;
						border: 1rpx solid #f0f0f0;
						margin-bottom: 10rpx; // 垂直方向间距

						&__text {
							font-size: 26rpx;
							flex-grow: 1;
							text-align: center; // 文本居中
						}
					}

					&__item.is-active {
						background-color: #c6a670;
						color: #fff;
					}

					&__item.not-active {
						background-color: #fff;
						color: #666;
					}
				}
			}
		}

		.invoice-wrapper {
			display: flex;
			flex-direction: column;
			padding: 20rpx 30rpx;

			.title {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
				margin-bottom: 10rpx;
			}

			.tips {
				font-size: 24rpx;
				color: #999;
				margin-bottom: 20rpx;
			}
		}
	}

	.tab-bar {
		display: flex;
		justify-content: space-around;
		align-items: center;
		height: 100rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

		.tab-item {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			width: 33.33%;
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
			border-bottom: 4rpx solid transparent;

			&.active {
				color: #07c160;
				border-color: #07c160;
			}

			.tab-item-title {
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}

	.order-item-wrapper {
		display: flex;
		flex-direction: column;
		padding: 20rpx 30rpx;
	}

	.popup-footer-bar {
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		flex: 1;
		align-items: center;
		justify-content: center;

		.btn-action {
			display: flex;
			width: 100%;
			align-items: center;
			justify-content: center;
			padding: 20rpx;
			background-color: #ff5722;
			/* 使用主题色 */
			border-radius: 60rpx;
			color: #ffffff;
			font-size: 28rpx;
			border: none;
		}
	}

	.loading-wrapper {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		background: #f5f5f5;

		.loading {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #999999;
			font-size: 24rpx;

			.loading-text {
				margin-right: 4rpx;
			}

			.dots {
				width: 24rpx;
				display: inline-block;

				&::after {
					content: ".";
					animation: dotAnimation 1.5s steps(4, end) infinite;
				}
			}
		}
	}

	@keyframes dotAnimation {
		0% {
			content: ".";
		}

		25% {
			content: "..";
		}

		50% {
			content: "...";
		}

		75% {
			content: "..";
		}

		100% {
			content: ".";
		}
	}
</style>
<template>
  <view>
    <my-page
      background="#ececec"
      :refresher-config="{
        enabled: true
      }"
      :load-more-config="{
        enabled: false
      }"
      :show-footer="true"
    >
      <template #nav>
        <my-nav title="申请退款" backIcon />
      </template>

      <view class="content">
        <view class="refund-reason">
          <view class="refund-reason__label">退款原因</view>
          <view class="refund-reason__section" @tap="handleChange">
            <view class="refund-reason__section__text">{{ refundInfo.reason || "请选择退款原因" }}</view>
            <wd-icon name="arrow-right" size="20" color="#999" />
          </view>
        </view>
        <wd-input label="退款方式" readonly label-width="120rpx" v-model="refundInfo.way" />
        <wd-input
          label="退款金额 (￥)"
          type="number"
          readonly
          error
          label-width="200rpx"
          v-model="refundInfo.price"
          placeholder="请填写退款金额"
        />
      </view>

      <template #footer>
        <view class="footer-bar">
          <wd-button type="primary" @click="onSubmit">申请退款</wd-button>
        </view>
      </template>
    </my-page>
    <my-popup :visible="showCancelPopup" title="请选择取消原因(必选)" showTopBar showFooter @close="onCancelPopupClose">
      <template #topBar>
        <view> 温馨提示：取消订单后，订单将无法恢复，已用抵现积分，优惠券将原路返还 </view>
      </template>

      <template #content>
        <view class="popup-item" v-for="(item, index) in cancelColumns" :key="index">
          <view class="popup-item-text">{{ item.label }}</view>
          <wd-checkbox v-model="item.isChecked" @change="selectReason(item)" />
        </view>
      </template>

      <template #footer>
        <view class="popup-footer">
          <wd-button type="primary" @click="submitCancelReason"> 确定 </wd-button>
        </view>
      </template>
    </my-popup>
  </view>
</template>

<script>
  // 取消订单原因
  const REFUND_REASON = [
    { label: "选错商品", value: "01" },
    { label: "重复下单", value: "02" },
    { label: "商品质量问题", value: "03" },
    { label: "商品缺货", value: "05" },
    { label: "商品价格错误", value: "06" },
    { label: "商品描述不符", value: "07" },
    { label: "其他原因", value: "08" }
  ]
  
  import { afterSaleApply } from '@/api/order.js'

  export default {
    name: "refundApply",
    data() {
      return {
        refundInfo: {
          reason: "",
          way: "按照原路返回",
          price: ""
        },
        showCancelPopup: false,
        cancelColumns: REFUND_REASON.map((item, index) => {
          return {
            label: item.label,
            value: item.value,
            isChecked: false,
            index: index
          }
        }),
        selectedReason: [] // 用于存储已选的取消原因
      }
    },
	onShow() {
		this.refundObj = JSON.parse(uni.getStorageSync('refundObj'))
		console.log(this.refundObj)
		this.refundInfo = {
		  reason: "",
		  way: "按照原路返回",
		  price: this.refundObj.totalAmount
		}
	},
    methods: {
      handleChange({ value }) {
        console.log("handleChange", value)
        // 处理输入变化
        this.showCancelPopup = true
      },
      onCancelPopupClose() {
        this.showCancelPopup = false
        this.selectedReason = [] // 清空已选的取消原因
        this.columns &&
          this.columns.forEach((item) => {
            item.isChecked = false // 重置复选框状态
          })
        console.log("Popup closed, reset selected reasons")
      },
      selectReason(selectedItem) {
        console.log("selectReason", selectedItem)
        const { label, value, isChecked } = selectedItem || {}
        if (isChecked) {
          // 如果选中，添加到 selectedReason
          this.selectedReason.push({ label, value })
        } else {
          // 如果取消选中，从 selectedReason 中移除
          this.selectedReason = this.selectedReason.filter((item) => item.value !== value)
        }
      },
      async submitCancelReason() {
        if (this.selectedReason.length === 0) {
          uni.showToast({
            title: "请选择取消原因",
            icon: "none"
          })
          return
        }

        console.log("提交取消原因:", this.selectedReason)
        this.refundInfo.reason = this.selectedReason
          .map((item) => {
            return item.label
          })
          .join(",")
		  
        this.showCancelPopup = false
      },
      async onSubmit() {
        if (!this.refundInfo.reason) {
          uni.showToast({
            title: "请填写退款原因",
            icon: "none"
          })
          return
        }
        if (!this.refundInfo.way) {
          uni.showToast({
            title: "请选择退款方式",
            icon: "none"
          })
          return
        }
        if (!this.refundInfo.price) {
          uni.showToast({
            title: "请填写退款金额",
            icon: "none"
          })
          return
        }
		let arr = []
		for(let i of this.refundObj.orderDetails) {
			arr.push({
				num: i.num,
				orderDetailId: i.orderDetailId
			})
		}
		let submitObj = {
			afterSaleProducts: arr,
			afterSaleType: 1,
			orderId: this.refundObj.orderId,
			reason: this.refundInfo.reason
		}
		
		const res = await afterSaleApply(submitObj)
		if(res.code === 200) {
			return uni.navigateTo({
			  url: `/packages/order/refund-ing?orderId=${this.refundObj.orderId}&price=${this.refundObj.totalAmount}`,
			  events: {
			    refundInfo: this.refundInfo
			  },
			  success: (res) => {
			    console.log("Navigate to refund-info success", res)
			  },
			  fail: (err) => {
			    console.error("Navigate to refund-info failed", err)
			  }
			})
		} else {
			uni.showToast({
			  title: res.msg,
			  icon: "none"
			})
		}
        
      }
    }
  }
</script>

<style scoped lang="scss">
  .content {
    .refund-reason {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx;
      font-size: 28rpx;
      color: #666;
      background-color: #fff;

      &__label {
        width: 120rpx;
        text-align: left;
        margin-left: 10rpx;
        margin-right: 30rpx;
        color: #333;
      }

      &__section {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &__text {
          flex: 1;
          color: #333;
          font-size: 28rpx;
          line-height: 40rpx;
        }
      }
    }
  }

  .footer-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    background-color: #ffffff;
    border-top: 1px solid #e5e5e5;
    margin-bottom: env(safe-area-inset-bottom);

    :deep() {
      .custom-btn {
        width: 100%;
        height: 80rpx;
        font-size: 32rpx;
        background-color: #c6a670;
        color: #ffffff;
        border-radius: 8rpx;
      }
    }
  }

  .popup-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    font-size: 28rpx;
    color: #333;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
    .popup-item-text {
      line-height: 40rpx;
    }
  }

  .popup-footer {
    display: flex;
    justify-content: center;
    align-items: center;

    .wd-button {
      width: 100%;
      max-width: 600rpx;
      height: 80rpx;
      font-size: 32rpx;
    }
  }
</style>

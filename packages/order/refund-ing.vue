<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
  >
    <template #nav>
      <my-nav title="进度详情" backIcon />
    </template>

    <view class="content">
      <refund-status :order-number="orderNumber" :status-value="statusValue" />

      <refund-info :amount="refundInfo.amount" :reason="refundInfo.reason" :time="refundInfo.time" />

      <!-- <refund-step :active="stepInfo.active" :steps="stepInfo.steps" /> -->
    </view>
  </my-page>
</template>

<script>
  export default {
    name: "RefundIng",
		onLoad(options = {}) {
			this.refundInfo.amount = options.price
			this.orderNumber = options.orderId
		},
    data() {
      return {
        orderNumber: "",
        statusValue: "processing",

        refundInfo: {
          amount: "",
          reason: "",
          time: ""
        },

        stepInfo: {
          active: 1,
          steps: [
            { title: "申请退款", description: "2023-10-01 12:00:00", status: "finished" },
            { title: "审核中", description: "2023-10-02 14:00:00", status: "finished" },
            { title: "退款处理中", description: "2023-10-03 16:00:00", status: "finished" },
            { title: "退款完成", description: "2023-10-04 18:00:00", status: "finished" }
          ]
        }
      }
    },
    methods: {}
  }
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    flex-direction: column;
    padding: 30rpx;
  }
</style>

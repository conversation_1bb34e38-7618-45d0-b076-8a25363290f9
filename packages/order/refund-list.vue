<template>
	<view class="wrapper">
		<my-nav title="退换/售后" backIconColor="black" textColor="#000" backIcon />

		<wd-tabs id="tabs" inactiveColor="#000" color="#C6A670" animated :active="active" @click="onTabClick">
			<wd-tab title="售后申请" :name="1" />
			<wd-tab title="处理中" :name="0" />
			<wd-tab title="售后记录" name="" />
		</wd-tabs>

		<search-bar id="search-bar" v-model="searchVal" :placeholder="'请输入商品名称、订单号搜索'" search-type="refund" />

		<scroll-view scroll-y scroll-with-animation refresher-enabled :scroll-top="scrollTop"
			refresher-default-style="black" :refresher-triggered="refreshing" lower-threshold="50" upper-threshold="50"
			refresher-threshold="100" refresher-background="transparent" @refresherrefresh="onRefresh"
			@refresherrestore="onRestore" @refresherabort="onAbort" @scrolltolower="onLoadMore"
			:style="{ height: scrollHeight + 'px' }">
			<view v-for="(item, index) in orderList" :key="index">
				<order-item @on-cancel-apply="cancelApply(item)" @on-edit-tracking="editTracking(item)" :order="item" :type="item.type" />
			</view>
			<view class="loading-wrapper" v-if="loading">
				<view class="loading">on-
					<text class="loading-text">正在加载更多</text>
					<text class="dots"></text>
				</view>
			</view>
		</scroll-view>
		
		<my-popup :visible="waitPop" custom-style="height: 200px;" title="填写快递单号" showFooter @close="waitPop = false">

			<template #content>
				<view style="padding: 30rpx;">
					<view>快递公司</view>
					<view style="margin-top: 20rpx;">
						<wd-radio-group v-model="waitObj.deliveryName" shape="button">
							<wd-radio value="圆通快递">圆通快递</wd-radio>
							<wd-radio value="中通快递">中通快递</wd-radio>
							<wd-radio value="顺丰快递">顺丰快递</wd-radio>
							<wd-radio value="京东快递">京东快递</wd-radio>
							<wd-radio value="申通快递">申通快递</wd-radio>
							<wd-radio value="韵达快递">韵达快递</wd-radio>
							<wd-radio value="德邦快递">德邦快递</wd-radio>
							<wd-radio value="百世快递">百世快递</wd-radio>
						  </wd-radio-group>
					</view>
				</view>
				<view style="padding: 30rpx;">
					<view>快递单号</view>
					<view style="margin-top: 20rpx;">
						<wd-input v-model="waitObj.deliveryNo" />
					</view>
				</view>
			</template>
		
			<template #footer>
				<view class="popup-footer-bar">
					<view class="btn-action" @tap="onAction()">确定</view>
				</view>
			</template>
		</my-popup>
		
	</view>
</template>

<script>
	import {
		px2rpx
	} from "@/common/util"
	import MyNav from "@/components/my-nav/my-nav.vue"
	import OrderItem from "@/components/order-item/order-item.vue"
	import SearchBar from "@/components/search-bar/search-bar.vue"
	import {
		getAfterSalesAppList,
		afterSale_cancle,
		afterSale_writeDeliveryNo
	} from "@/api/order.js"

	const TAB_STATUS = ["applying", "processing", "record"]
	const REFUND_TYPES = [{
			name: "退货",
			value: "return"
		},
		{
			name: "换货",
			value: "exchange"
		}
	]

	export default {
		name: "refund",
		components: {
			MyNav,
			OrderItem,
			SearchBar
		},
		data() {
			return {
				searchVal: '',
				active: 1,
				loading: false,
				waitPop: false,
				orderList: [],
				waitObj: {
					afterSaleId: '',
					deliveryName: '',
					deliveryNo: ''
				},
				tabList: [{
						title: "售后申请",
						type: 1
					},
					{
						title: "处理中",
						type: 0
					},
					{
						title: "售后记录",
						type: ""
					}
				],

				refreshing: false,
				scrollHeight: 0,
				scrollTop: 0
			}
		},

		created() {
			this.getHeight()
		},

		onLoad(options = {}) {
			console.log("options", options)
			if (options.type) {
				// 检查传入的type是否在tabList中
				const isValidType = this.tabList.some((tab) => tab.type === options.type)
				if (isValidType) {
					this.active = options.type
				}
			}
		},

		mounted() {
			// this.onLoadMore()
			this._getAfterSalesAppList()
		},

		methods: {
			async onAction() {
				const res = await afterSale_writeDeliveryNo(this.waitObj)
				if (res.code === 200) {
					uni.showToast({
						title: res.msg,
						icon: "success"
					})
					// 重新加载订单列表
					this._getAfterSalesAppList()
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})
				}
			},
			editTracking(val) {
				this.waitObj.afterSaleId = val.afterSaleId
				this.waitPop = true
			},
			cancelApply(val) {
				console.log(val)
				uni.showModal({
					title: "",
					content: "确认取消售后申请吗？",
					confirmText: "确认",
					cancelText: "取消",
					success: (res) => {
						if (res.confirm) {
							this._afterSale_cancle(val.afterSaleId)
						} else if (res.cancel) {
							console.log("用户取消删除订单")
						}
					},
					fail: (err) => {
						console.error("删除订单失败", err)
					}
				})
			},
			async _afterSale_cancle(id) {
				const res = await afterSale_cancle({afterSaleId: id})
				if (res.code === 200) {
					uni.showToast({
						title: res.msg,
						icon: "success"
					})
					// 重新加载订单列表
					this._getAfterSalesAppList()
				} else {
					uni.showToast({
						title: res.msg,
						icon: "none"
					})
				}
			},
			async _getAfterSalesAppList() {
				let obj = {
					searchValue: this.searchVal,
					status: this.active,
					pageNum: 1,
					pageSize: 200
				}
				const res = await getAfterSalesAppList(obj)
				if (res.code === 200) {
					let arr = []
					for (let i of res.rows) {
						if (this.active === 1) {
							i.type = "applying"
						} else {
							i.type = "processing"
						}
						arr.push({
							productId: i.afterSaleId,
							items: [i]
						})
					}


					this.orderList = res.rows
				} else {

				}
				this.refreshing = false
			},
			onTabClick({
				index,
				name
			}) {
				this.active = name
				// 先重置数据和滚动位置
				this.orderList = []
				this.refreshing = true
				// 强制DOM更新后再设置scrollTop
				this.$nextTick(() => {
					this.scrollTop = 0
					// 获取新数据
					this._getAfterSalesAppList()
				})
			},

			async getListInfo() {
				// TODO 获取订单列表信息
				// await new Promise((resolve) => setTimeout(resolve, 500))
				// this.orderList = await this.generateOrder()
				// this.refreshing = false
				// uni.showToast({
				// 	title: "加载成功",
				// 	icon: "success",
				// 	duration: 2000
				// })
			},

			onRefresh() {
				if (this.refreshing) return
				this.refreshing = true
				this._getAfterSalesAppList()
			},

			onSearch(value) {
				console.log("onSearch", value)
			},

			onRestore() {
				console.log("onRestore")
				this.refreshing = false
			},

			onAbort() {
				console.log("onAbort")
				this.refreshing = false
			},

			async generateOrder() {
				console.log("generateOrder", this.active)

				// 商品模板
				const products = [{
						productId: "matepad-pro-13",
						name: "HUAWEI MatePad Pro 13.2英寸 平板电脑 WiFi连接 支持通话",
						specs: {
							memory: "12GB",
							storage: "256GB",
							color: "曜金黑"
						},
						price: 3999,
						imageUrl: "https://example.com/matepad-pro-13.jpg",
						gifts: [{
								name: "平板电脑保护壳",
								quantity: 1,
								imageUrl: "https://example.com/matepad-pro-13.jpg"
							},
							{
								name: "超薄充电器",
								quantity: 1,
								imageUrl: "https://example.com/matepad-pro-13.jpg"
							}
						]
					},
					{
						productId: "matebook-16s-2023",
						name: "HUAWEI MateBook 16s 2023 13代酷睿i5 32G 1T 16英寸高色准",
						specs: {
							processor: "i5",
							memory: "32GB",
							storage: "1TB",
							color: "深空灰"
						},
						price: 7199,
						imageUrl: "https://example.com/matebook-16s.jpg",
						gifts: [{
								name: "笔记本电脑保护套",
								quantity: 1,
								imageUrl: "https://example.com/matebook-16s.jpg"
							},
							{
								name: "超薄充电器",
								quantity: 1,
								imageUrl: "https://example.com/matebook-16s.jpg"
							}
						]
					}
				]

				return Array(10)
					.fill(null)
					.map(() => {
						// 随机选择1-2个商品
						const itemCount = Math.floor(Math.random() * 2) + 1
						const selectedProducts = []
						while (selectedProducts.length < itemCount) {
							const product = products[Math.floor(Math.random() * products.length)]
							if (!selectedProducts.find((p) => p.productId === product.productId)) {
								selectedProducts.push({
									...product,
									quantity: Math.floor(Math.random() * 2) + 1
								})
							}
						}

						// 计算总金额和总数量
						const totalAmount = selectedProducts.reduce((sum, item) => sum + item.price * item
							.quantity, 0)
						const totalItems = selectedProducts.reduce((sum, item) => sum + item.quantity, 0)

						let refundType = ""
						if (this.active !== "applying") {
							refundType = REFUND_TYPES[Math.floor(Math.random() * REFUND_TYPES.length)].value
						}

						return {
							orderId: Math.random().toString(36).substr(2, 9),
							refundId: Math.random().toString(36).substr(2, 9),
							refundType,
							type: this.active,
							orderTime: new Date().toLocaleString(),
							status: "",
							totalAmount,
							totalItems,
							seller: {
								name: "华为品牌官方授权",
								isOfficial: true
							},
							items: selectedProducts
						}
					})
			},

			async onLoadMore() {
				// if (this.loading) return
				// this.loading = true

				// // 模拟异步加载
				// await new Promise((resolve) => setTimeout(resolve, 500))
				// const newOrders = await this.generateOrder()

				// this.orderList = [...this.orderList, ...newOrders]
				// this.loading = false
			},

			getHeight() {
				const {
					safeArea: {
						height: safeAreaHeight
					}
				} = uni.getWindowInfo()
				const navHeight = uni.getStorageSync("navHeight")
				this.t2bHeight = uni.upx2px(px2rpx(navHeight))

				uni
					.createSelectorQuery()
					.select("#search-bar")
					.boundingClientRect()
					.exec((res) => {
						if (res && res[0]) {
							let searchBarSize = res[0].height || 0
							searchBarSize = uni.upx2px(px2rpx(searchBarSize))
							this.scrollHeight = safeAreaHeight - this.t2bHeight - searchBarSize
						} else {
							this.scrollHeight = safeAreaHeight - this.t2bHeight
						}
					})

				uni
					.createSelectorQuery()
					.select("#tabs")
					.boundingClientRect()
					.exec((res) => {
						if (res && res[0]) {
							let tabsHeight = res[0].height || 0
							tabsHeight = uni.upx2px(px2rpx(tabsHeight))
							this.scrollHeight = this.scrollHeight - tabsHeight
						}
					})

				this.scrollTop = 0
			}
		}
	}
</script>

<style scoped lang="scss">
	.btn-action {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx;
		background-color: #ff5722;
		/* 使用主题色 */
		border-radius: 60rpx;
		color: #ffffff;
		font-size: 28rpx;
		border: none;
	}
	.wrapper {
		padding-bottom: env(safe-area-inset-bottom);
		background: #f5f5f5;
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.tab-bar {
		display: flex;
		justify-content: space-around;
		align-items: center;
		height: 100rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

		.tab-item {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			width: 33.33%;
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
			border-bottom: 4rpx solid transparent;

			&.active {
				color: #07c160;
				border-color: #07c160;
			}

			.tab-item-title {
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}

	.loading-wrapper {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		background: #f5f5f5;

		.loading {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #999999;
			font-size: 24rpx;

			.loading-text {
				margin-right: 4rpx;
			}

			.dots {
				width: 24rpx;
				display: inline-block;

				&::after {
					content: ".";
					animation: dotAnimation 1.5s steps(4, end) infinite;
				}
			}
		}
	}

	.search-bar {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	@keyframes dotAnimation {
		0% {
			content: ".";
		}

		25% {
			content: "..";
		}

		50% {
			content: "...";
		}

		75% {
			content: "..";
		}

		100% {
			content: ".";
		}
	}
</style>
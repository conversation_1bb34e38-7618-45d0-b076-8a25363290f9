<template>
	<view class="wrapper">
		<my-nav title="确认订单" backIconColor="black" textColor="#000" backIcon />

		<scroll-view scroll-y scroll-with-animation :scroll-top="scrollTop" refresher-default-style="black"
			:refresher-triggered="refreshing" refresher-background="transparent" :style="{ height: scrollHeight + 'px' }">
			<!-- @scrolltolower="onLoadMore" -->
			<view class="top-add" @tap="chooseAddress">
				<image v-if="!defaultAddress" src="/packages/order/image/choose_address.png" mode="widthFix"></image>
				<view v-else class="ta-isDaf">
					<view>
						<image src="/static/address.png" mode="widthFix"></image>
					</view>
					<view>
						<view>
							<text>{{defaultAddress.consigneeName}} </text>
							<text> {{defaultAddress.consigneePhone}}</text>
						</view>
						<view>
							{{defaultAddress.address}}
						</view>
					</view>
					<view>
						<wd-icon name="arrow-right" size="22px"></wd-icon>
					</view>
				</view>
			</view>
			<view v-for="(item, index) in orderList" :key="index" class="order-item-wrapper">
				<order-item :order="item" :type="'payment'" :showBottomTips="true" :showAmount="false" :showActionBar="false" />
			</view>
			<view class="loading-wrapper" v-if="loading">
				<view class="loading">
					<text class="loading-text">正在加载更多</text>
					<text class="dots"></text>
				</view>
			</view>

			<view class="delivery_view">
				<view class="dv-top">
					<view>配送服务</view>
					<view>
						<view>快递运输</view>
						<view>可享受免费退换货</view>
					</view>
				</view>
				<view>
					<wd-input label="订单备注" type="tel" label-width="120rpx" v-model="remark" placeholder="建议留言前先与客服沟通确认" />
				</view>
			</view>

			<view class="delivery_view delivery_view2">
				<view class="dv-top">
					<view>商品金额</view>
					<view>¥{{allPrice}}</view>
				</view>
				<view class="dv-top">
					<view>运费（包邮）</view>
					<view>¥0</view>
				</view>
				<view class="dv-top" @tap="goUserCup">
					<view>优购码</view>
					<view style="display: flex; align-item: right;">
						<view style="flex: 1;"></view>
						<view v-if="checkedCoup === ''">{{couTotal}}个优购码可用</view>
						<view v-else style="color: #D81E07;">-{{sureChCoupInfo.discountAmount}}</view>
						<view><wd-icon name="arrow-right" color="#878787" size="22px"></wd-icon></view>
					</view>
				</view>
				<view class="dv-top" v-if="checkedCoup !== ''">
					<view style="flex: 1; color: #878787;">优购码编号：{{sureChCoupInfo.discountCodeNo}}</view>
				</view>
			</view>

			<view class="delivery_view delivery_view2">
				<view class="dv-top">
					<view>支付方式</view>
					<view style="display: flex; align-item: right;">
						<view style="flex: 1;"></view>
						<view>微信支付</view>
						<view><wd-icon name="arrow-right" color="#878787" size="22px"></wd-icon></view>
					</view>
				</view>
			</view>

		</scroll-view>

		<template #footer>
			<view class="footer-bar">
				<view class="footer-bar__total-info">
					<text style="color: #6F6F6F;">合计：</text>
					<text style="color: #D81E07;"
						v-if="checkedCoup">{{allPrice-sureChCoupInfo.discountAmount<0?0:allPrice-sureChCoupInfo.discountAmount}}</text>
					<text style="color: #D81E07;" v-else>{{allPrice}}</text>
				</view>
				<view class="footer-bar__action">
					<view class="btn-pay" @tap="onAction('pay')">微信支付</view>
				</view>
			</view>
		</template>

		<my-popup :visible="showCoupPopup" title="优购码" showTopBar showFooter @close="onCancelPopupClose">
			<template #content>
				<view class="">
					<view class="popup-item" v-for="(item, index) in couList" :key="index">
						<view class="pi1">
							<wd-checkbox v-model="item.isChecked" @change="selectReason(item)" />
						</view>
						<view class="pi2" @tap="selectReason(item)">
							<view><text>优惠码编号: </text>{{ item.discountCodeNo }}</view>
							<view><text>优惠金额: </text>{{ item.discountAmount }}</view>
							<view><text>有效期: </text>{{ item.endTime }}</view>
						</view>
					</view>
				</view>
			</template>
			<template #footer>
				<view class="footer-btn" @click="sureCoup">确定</view>
			</template>
		</my-popup>
	</view>
</template>

<script>
	import {
		px2rpx
	} from "@/common/util"
	import OrderItem from "@/components/order-item/order-item.vue"
	import OrderCheck from "@/components/order-swtich/order-check.vue"
	import {
		getOrderList,
		cancelOrder,
		removeOrder,
		placeOrder,
		placeWeixinOrder,
		simulationPaymentSuccessful
	} from "@/api/order.js"
	import {
		getAddressList
	} from "@/api/address.js"
	import {
		getCouponList
	} from '@/api/coupon.js'

	// 取消订单原因
	const REFUND_REASON = [{
			label: "选错商品",
			value: "01"
		},
		{
			label: "重复下单",
			value: "02"
		},
		{
			label: "商品质量问题",
			value: "03"
		},
		{
			label: "商品缺货",
			value: "05"
		},
		{
			label: "商品价格错误",
			value: "06"
		},
		{
			label: "商品描述不符",
			value: "07"
		},
		{
			label: "其他原因",
			value: "08"
		}
	]

	// 收货状态
	const RECEIVING_STATUS = [{
			label: "未收到货",
			value: "01"
		},
		{
			label: "已收到货",
			value: "02"
		}
	]

	// 发票类型
	const INVOICE_TYPE = [{
			label: "电子普通发票",
			value: "normal"
		},
		{
			label: "专用发票",
			value: "special"
		}
	]

	// 订单状态列表
	const TAB_LIST = [{
			title: "待付款",
			type: "payment"
		},
		{
			title: "待发货",
			type: "delivery"
		},
		{
			title: "待收货",
			type: "received"
		},
		{
			title: "已完成",
			type: "completed"
		},
		{
			title: "待开票",
			type: "invoice"
		},
		{
			title: "已取消",
			type: "cancelled"
		}
	]

	/**
	 * 订单列表页面 api
	 * /store/order/placeOrder
	 * /store/order/appList
	 * /store/order/cancle
	 * /store/apply/applyOpenBlueInvoice
	 * /store/apply/getBlueInvoiceInfoByOrderId
	 * /store/order/remove
	 * /store/order/getInfo
	 * /store/apply/getBlueInvoiceInfoByOrderId
	 */
	export default {
		name: "list",
		components: {
			OrderItem,
			OrderCheck
		},

		data() {
			return {
				remark: '',
				allPrice: 0,
				active: "all",
				loading: false,
				orderList: [],
				tabList: [{
						title: "全部",
						type: "all"
					},
					{
						title: "待付款",
						type: "payment"
					},
					{
						title: "待发货",
						type: "delivery"
					},
					{
						title: "待收货",
						type: "received"
					},
					{
						title: "已完成",
						type: "completed"
					},
					{
						title: "待开票",
						type: "invoice"
					},
					{
						title: "已取消",
						type: "cancelled"
					}
				],

				refreshing: false,
				scrollHeight: 0,
				scrollTop: 0,

				showCoupPopup: false,
				cancelColumns: REFUND_REASON.map((item, index) => {
					return {
						label: item.label,
						value: item.value,
						isChecked: false,
						index: index
					}
				}),
				selectedReason: [],

				showRefundPopup: false,
				receivingColumns: RECEIVING_STATUS.map((item, index) => {
					return {
						label: item.label,
						value: item.value,
						// 默认第一项选择
						isChecked: index === 0,
						index
					}
				}),
				receivingChildren: [{
						value: "01",
						items: [{
							label: "仅退款",
							value: "10",
							isChecked: true,
							index: 0
						}]
					},
					{
						value: "02",
						items: [{
								label: "退货退款",
								value: "20",
								isChecked: true,
								index: 1
							},
							{
								label: "换货",
								value: "21",
								isChecked: false,
								index: 2
							}
						]
					}
				],
				currentRefundIndex: 0,
				currentOrderId: null, // 当前操作的订单ID

				applyRefundSelected: false,

				showApplyInvoicePopup: false,
				invoiceType: INVOICE_TYPE.map((item, index) => {
					return {
						label: item.label,
						value: item.value,
						isChecked: index === 0, // 默认选中第一个
						index
					}
				}),
				invoiceTitle: [{
						value: "normal",
						items: [{
								label: "个人",
								value: "personal",
								isChecked: true,
								index: 0
							},
							{
								label: "单位",
								value: "company",
								isChecked: false,
								index: 1
							}
						]
					},
					{
						value: "special",
						items: [{
							label: "单位",
							value: "company",
							isChecked: true,
							index: 0
						}]
					}
				],
				currentInvoiceIndex: 0,

				invoiceForm: {
					title: "",
					phone: "",
					email: "",
					type: "personal", // 默认电子普通发票
					taxNumber: "",
					companyAddress: "",
					companyPhone: "",
					bankName: "",
					bankAccount: "",
					remark: ""
				},

				couList: [],
				addressList: [],
				defaultAddress: null,
				couTotal: 0,
				checkedCoup: '',
				sureCheckedCoup: '',
				sureChCoupInfo: null
			}
		},

		created() {
			this.getHeight()
		},

		onLoad(options = {}) {

			if (options.type) {
				// 检查传入的type是否在tabList中
				const isValidType = this.tabList.some((tab) => tab.type === options.type)
				if (isValidType) {
					this.active = options.type
				}
			}
		},

		mounted() {
			this._getAddressList()
			this._getCouponList()
		},

		onShow() {
			uni.$on('navigateBackData', this.handleBackData);

			const data = JSON.parse(uni.getStorageSync('startPayData'))
			console.log(data)
			this.orderList = data
			let num = 0;
			for (let i of this.orderList) {
				for (let j of i.items) {
					num += j.attrValues[0].insidePrice
				}
			}
			this.allPrice = num
		},
		onUnload() {
			// 页面卸载时移除监听，非常重要！
			uni.$off('navigateBackData', this.handleBackData);
		},
		methods: {
			sureCoup() {
				if (this.checkedCoup === '') {
					uni.showToast({
						title: "请选择优购码",
						icon: "none",
						duration: 4000
					})
					return false
				}
				this.sureCheckedCoup = this.checkedCoup
				this.showCoupPopup = false
			},
			goUserCup() {
				if (this.couTotal) {
					this.showCoupPopup = true
				} else {
					uni.showToast({
						title: "暂无可用优购码",
						icon: "none",
						duration: 4000
					})
				}
			},
			async _getCouponList() {
				const res = await getCouponList({
					usedStatus: 0,
					pageNum: 1,
					pageSize: 1000
				})
				for (let i of res.rows) {
					i.isChecked = false
					if (i.discountCodeId === this.checkedCoup) {
						i.isChecked = true
					}
				}
				this.couTotal = res.total || 0
				this.couList = res.rows || []
			},
			handleBackData(data) {
				console.log('收到数据:', data);
				for (let i of this.addressList) {
					if (i.userAddressId === data.userAddressId) {
						this.defaultAddress = i
					}
				}
			},
			chooseAddress() {
				let userAddressId = this.defaultAddress.userAddressId || ''
				uni.navigateTo({
					url: `/packages/profile/address/choose?userAddressId=${userAddressId}`
				})
			},
			async _getAddressList() {
				try {
					const result = await getAddressList({
						userId: uni.getStorageSync("userId")
					})

					const addressData = result
					if (addressData && addressData.length > 0) {
						// 成功获取地址数据，转换数据格式
						this.defaultAddress = null
						this.addressList = addressData.map((address) => ({
							address: this._formatAddress(address),
							...address
						}))
						for (let i of this.addressList) {
							if (i.isDefault === '1') {
								this.defaultAddress = i
							}
						}
					} else {
						this.addressList = []
					}
				} catch (error) {} finally {
					this.loading = false
					this.refreshing = false
				}
			},
			_formatAddress(address) {
				const parts = [
					address.province,
					address.city,
					address.district,
					address.detail,
				].filter(Boolean)
				return parts.join("") || address.address || "地址信息"
			},
			// 获取订单列表
			async _getOrderList() {
				try {
					const params = {
						page: 1,
						size: 20
					}

					// 根据当前选中的tab设置状态参数
					if (this.active !== "all") {
						params.status = this.active
					}

					const result = await getOrderList(params)
					const {
						code,
						msg,
						data,
						rows
					} = result || {}
					const orderData = data || rows

					if (code === 200 && orderData && orderData.length > 0) {
						// 成功获取订单数据，转换数据格式
						this.orderList = this._transformOrderData(orderData)
						console.log("订单列表加载成功:", this.orderList.length, "个订单")
					} else {
						console.log("订单列表接口返回空数据，使用默认数据:", msg)
						// 保持原有的 mock 数据或显示空状态
						this.orderList = []
						await this.getListInfo()
					}
				} catch (error) {
					console.log("获取订单列表失败，使用默认数据:", error)
					// 保持原有的 mock 数据或显示空状态
					this.orderList = []
					await this.getListInfo()
				} finally {
					this.loading = false
					this.refreshing = false
				}
			},

			// 转换订单数据格式
			_transformOrderData(orderData) {
				return orderData.map((order) => ({
					orderId: order.id || order.orderId,
					orderNo: order.orderNo || order.orderNumber,
					status: order.status || "pending",
					type: this._getOrderType(order.status),
					createTime: order.createTime || new Date().toISOString(),
					totalAmount: order.totalAmount || order.amount || 0,
					items: order.items || order.products || [],
					address: order.address || {},
					// 其他订单相关字段
					canCancel: order.canCancel || false,
					canPay: order.canPay || false,
					canRefund: order.canRefund || false
				}))
			},

			// 根据订单状态获取订单类型
			_getOrderType(status) {
				const statusMap = {
					pending: "payment",
					paid: "delivery",
					shipped: "received",
					completed: "completed",
					cancelled: "cancelled",
					invoice: "invoice"
				}
				return statusMap[status] || "payment"
			},

			// 取消订单
			async _cancelOrder(orderId, reason) {
				try {
					const {
						code,
						msg
					} = await cancelOrder({
						orderId,
						reason: reason || "用户取消"
					})
					if (code === 200) {
						uni.showToast({
							title: "订单取消成功",
							icon: "success"
						})
						// 重新加载订单列表
						this._getOrderList()
					} else {
						uni.showToast({
							title: msg || "取消失败",
							icon: "none"
						})
					}
				} catch (error) {
					console.log("取消订单失败:", error)
					uni.showToast({
						title: "取消失败",
						icon: "none"
					})
				}
			},

			// 删除订单
			async _deleteOrder(orderId) {
				try {
					const {
						code,
						msg
					} = await removeOrder({
						orderIds: [orderId]
					})
					if (code === 200) {
						uni.showToast({
							title: "删除成功",
							icon: "success"
						})
						// 重新加载订单列表
						this._getOrderList()
					} else {
						uni.showToast({
							title: msg || "删除失败",
							icon: "none"
						})
					}
				} catch (error) {
					console.log("删除订单失败:", error)
					uni.showToast({
						title: "删除失败",
						icon: "none"
					})
				}
			},

			onTabClick({
				index,
				name
			}) {
				this.active = name
				// 先重置数据和滚动位置
				this.orderList = []
				this.refreshing = true
				// 强制DOM更新后再设置scrollTop
				this.$nextTick(() => {
					this.scrollTop = 0
					// 获取新数据
					this._getOrderList()
				})
			},

			// 在 methods 中添加
			selectReason(selectedItem) {
				this.checkedCoup = selectedItem.discountCodeId
				this.sureChCoupInfo = selectedItem
				for (let i of this.couList) {
					i.isChecked = false
					if (i.discountCodeId === this.checkedCoup) {
						i.isChecked = true
					}
				}
			},

			async submitCancelReason() {
				if (this.selectedReason.length === 0) {
					uni.showToast({
						title: "请选择取消原因",
						icon: "none"
					})
					return
				}

				console.log("提交取消原因:", this.selectedReason)

				// 获取取消原因文本
				const reasonText = this.selectedReason.map((item) => item.label).join(", ")

				// 调用取消订单API
				if (this.currentOrderId) {
					await this._cancelOrder(this.currentOrderId, reasonText)
				}

				// 关闭弹窗
				this.showCancelPopup = false
				// 重置选择的原因
				this.selectedReason = []
				this.currentOrderId = null
			},

			onCancelPopupClose() {
				this.showCoupPopup = false
			},

			onCancelRefundPopupClose() {
				this.showRefundPopup = false
				console.log("Refund popup closed")
			},

			onCancelInvoicePopupClose() {
				this.showApplyInvoicePopup = false
				console.log("Invoice popup closed")
			},

			onRefundItemCheck(item) {
				console.log("onRefundItemCheck", item)
				// 处理退款商品选中逻辑
				// 这里可以根据业务需求进行处理
			},

			// 设置全选状态
			setApplyRefundStatus() {
				console.log("setApplyRefundStatus", this.applyRefundSelected)
				// 更新所有商品的选中状态
				this.orderList.forEach((order) => {
					order.items.forEach((item) => {
						item.isChecked = this.applyRefundSelected
					})
				})
			},

			// 提交退款下一步
			submitRefundNext() {
				console.log("submitRefundNext", this.receivingColumns, this.receivingChildren[this.currentRefundIndex].items)
				// 检查是否有选中的退款状态和子项
				const selectedReceiving = this.receivingColumns.find((item) => item.isChecked)
				const selectedChild = this.receivingChildren[this.currentRefundIndex].items.find((item) => item.isChecked)
				if (!selectedReceiving || !selectedChild) {
					uni.showToast({
						title: "请选择收货状态和售后类型",
						icon: "none"
					})
					return
				}

				return uni.navigateTo({
					url: `/packages/order/refund-apply?status=${selectedReceiving.value}&type=${selectedChild.value}`
				})
			},

			// 在 methods 中添加或修改
			setReceivingStatus(index) {
				// 更新当前选中的退款索引
				this.currentRefundIndex = index

				// 更新选中状态，确保只有一项被选中
				this.receivingColumns.forEach((item, i) => {
					item.isChecked = i === index
				})
			},

			setInvoiceType(index) {
				console.log("setInvoiceType", index)
				// 更新当前选中的发票类型索引
				this.currentInvoiceIndex = index
				if (index === 1) {
					this.invoiceForm.type = "company"
				}

				// 更新选中状态，确保只有一项被选中
				this.invoiceType.forEach((item, i) => {
					item.isChecked = i === index
				})
			},

			setReceivingChildStatus(index, value) {
				// 更新当前选中的退款子项
				this.receivingChildren[this.currentRefundIndex].items.forEach((item) => {
					item.isChecked = item.value === value
				})
			},

			setInvoiceChildStatus(index, value) {
				console.log("setInvoiceChildStatus", index, value)
				if (index === 1) {
					this.invoiceForm.type = "company"
				} else {
					this.invoiceForm.type = "personal"
				}
				// 更新当前选中的发票子项
				this.invoiceTitle[this.currentInvoiceIndex].items.forEach((item) => {
					item.isChecked = item.value === value
				})
			},

			onAddWechatInvoice() {
				console.log("onAddWechatInvoice")
			},

			async getListInfo() {
				// TODO 获取订单列表信息
				await new Promise((resolve) => setTimeout(resolve, 500))
				this.orderList = await this.generateOrder()
				this.refreshing = false
				uni.showToast({
					title: "加载成功",
					icon: "success",
					duration: 2000
				})
			},

			onRefresh() {
				if (this.refreshing) return
				this.refreshing = true
				// this._getOrderList()
			},

			onRestore() {
				console.log("onRestore")
				this.refreshing = false
			},

			onAbort() {
				console.log("onAbort")
				this.refreshing = false
			},

			async generateOrder() {
				console.log("generateOrder", this.active)

				// 商品模板
				const products = [{
						productId: "matepad-pro-13",
						name: "HUAWEI MatePad Pro 13.2英寸 平板电脑 WiFi连接 支持通话",
						specs: {
							memory: "12GB",
							storage: "256GB",
							color: "曜金黑"
						},
						price: 3999,
						imageUrl: "https://example.com/matepad-pro-13.jpg",
						gifts: [{
								name: "平板电脑保护壳",
								quantity: 1,
								imageUrl: "https://example.com/matepad-pro-13.jpg"
							},
							{
								name: "超薄充电器",
								quantity: 1,
								imageUrl: "https://example.com/matepad-pro-13.jpg"
							}
						],
						isChecked: false
					},
					{
						productId: "matebook-16s-2023",
						name: "HUAWEI MateBook 16s 2023 13代酷睿i5 32G 1T 16英寸高色准",
						specs: {
							processor: "i5",
							memory: "32GB",
							storage: "1TB",
							color: "深空灰"
						},
						price: 7199,
						imageUrl: "https://example.com/matebook-16s.jpg",
						gifts: [{
								name: "笔记本电脑保护套",
								quantity: 1,
								imageUrl: "https://example.com/matebook-16s.jpg"
							},
							{
								name: "超薄充电器",
								quantity: 1,
								imageUrl: "https://example.com/matebook-16s.jpg"
							}
						],
						isChecked: false
					}
				]

				return Array(10)
					.fill(null)
					.map(() => {
						// 随机选择1-2个商品
						const itemCount = Math.floor(Math.random() * 2) + 1
						const selectedProducts = []
						while (selectedProducts.length < itemCount) {
							const product = products[Math.floor(Math.random() * products.length)]
							if (!selectedProducts.find((p) => p.productId === product.productId)) {
								selectedProducts.push({
									...product,
									quantity: Math.floor(Math.random() * 2) + 1
								})
							}
						}

						// 计算总金额和总数量
						const totalAmount = selectedProducts.reduce((sum, item) => sum + item.price * item.quantity, 0)
						const totalItems = selectedProducts.reduce((sum, item) => sum + item.quantity, 0)
						let statusIndex = Math.floor(Math.random() * TAB_LIST.length)
						if (this.active !== "all") {
							statusIndex = TAB_LIST.findIndex((tab) => tab.type === this.active)
						}

						return {
							orderId: Math.random().toString(36).substr(2, 9),
							status: TAB_LIST[statusIndex].title,
							type: TAB_LIST[statusIndex].type,
							totalAmount,
							totalItems,
							seller: {
								name: "华为品牌官方授权",
								isOfficial: true
							},
							items: selectedProducts
						}
					})
			},

			async onLoadMore() {
				if (this.loading) return
				this.loading = true

				// 模拟异步加载
				await new Promise((resolve) => setTimeout(resolve, 500))
				const newOrders = await this.generateOrder()

				this.orderList = [...this.orderList, ...newOrders]
				this.loading = false
			},

			showDetail(item) {
				console.log("showDetail", item)
				item = {
					...item,
					orderId: "1234", // 假设orderId是唯一标识符
					type: "all" // 确保有type字段
				}
				return uni.navigateTo({
					url: `/packages/shop/info?orderId=${item.orderId}&type=${item.type}`
				})
			},

			cancelOrder(orderId) {
				this.currentOrderId = orderId
				this.showCancelPopup = true
			},

			// 退款
			refund() {
				console.log("refund")
				this.showRefundPopup = true
			},

			// 申请发票
			applyInvoice() {
				console.log("applyInvoice ")
				this.showApplyInvoicePopup = true
			},

			// 申请退款
			applyRefund() {
				this.showRefundPopup = true
			},

			confirmGet() {
				console.log("confirmGet")

				uni.showModal({
					title: "确认收货",
					content: "为了保障您的权益，请收到商品确认无误后再确认收货",
					confirmText: "确认收货",
					cancelText: "取消",
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: "收货成功",
								icon: "success"
							})
						} else if (res.cancel) {
							console.log("用户取消收货")
						}
					},
					fail: (err) => {
						console.error("确认收货失败", err)
					}
				})
			},

			deleteOrder() {
				uni.showModal({
					title: "确认删除此订单吗？",
					content: "订单删除后将无法恢复，请谨慎操作！",
					confirmText: "删除",
					cancelText: "取消",
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: "订单已删除",
								icon: "success"
							})
							// 删除订单逻辑
							this.orderList = this.orderList.filter((order) => order.status !== "已取消")
						} else if (res.cancel) {
							console.log("用户取消删除订单")
						}
					},
					fail: (err) => {
						console.error("删除订单失败", err)
					}
				})
			},

			showInvoice() {
				console.log("showInvoice")
				uni.navigateTo({
					url: "/packages/order/detail-invoice"
				})
			},

			// 再来一单
			buyAgain() {
				return uni.switchTab({
					url: "/pages/shoppingCart/index"
				})
			},

			modifyAddress() {
				uni.navigateTo({
					url: "/packages/profile/address/edit"
				})
			},

			editAddress() {
				uni.navigateTo({
					url: "/packages/profile/address/edit"
				})
			},

			async onAction(action = "") {
				let arr = []
				for (let i of this.orderList) {
					for (let j of i.items) {
						arr.push({
							accountUnit: j.accountUnit,
							cartId: j.cateId,
							giftProducts: j.gifts,
							num: j.num || 1,
							productAttrValueId: j.attrValues[0].productAttrValueId,
							productId: j.productId,
							price: j.attrValues[0].insidePrice
						})
					}
				}
				const submitObj = {
					deliveryService: 0,
					discountCodeId: this.sureChCoupInfo?.discountCodeId || '',
					remark: this.remark,
					userAddressId: this.defaultAddress?.userAddressId,
					details: arr
				}
				const res1 = await placeOrder(submitObj)
				if (res1.code === 200) {
					
					const res2 = await simulationPaymentSuccessful({orderId: res1.data})
					if(res2.code === 200) {
						uni.showToast({
							title: '支付成功！',
							icon: 'success'
						});
						setTimeout(() => {
							uni.redirectTo({
								url: `/packages/order/list`
							});
						}, 1500);
					} else {
						uni.showToast({
							title: res2.msg,
							icon: "none"
						})
					}
					
					// const res2 = await placeWeixinOrder({
					// 	orderId: res1.data
					// })
					// if(res2.code === 200) {
					// 	try {
					// 		// const paymentResult = await uni.requestPayment({
					// 		// 	...res2.data
					// 		// });
					// 		// uni.hideLoading();
					// 		// uni.showToast({
					// 		// 	title: '支付成功！',
					// 		// 	icon: 'success'
					// 		// });
					// 		res2.data.package = res2.data.packageValue
					// 		uni.requestPayment({
					// 		  ...res2.data,
					// 		  success: (res) => {
					// 		    uni.showToast({
					// 		      title: "支付成功",
					// 		      icon: "success"
					// 		    })
					// 		  },
					// 		  fail: (err) => {
					// 				console.log(err)
					// 		    uni.showToast({
					// 		      title: "支付失败",
					// 		      icon: "none"
					// 		    })
					// 		  }
					// 		})
							
					// 		// setTimeout(() => {
					// 		// 	uni.redirectTo({
					// 		// 		url: `/pages/pay-success/pay-success?orderId=${this.data.orderId}`
					// 		// 	});
					// 		// }, 1500);
					// 	} catch (err) {
							
					// 	}
					// } else {
					// 	uni.showToast({
					// 		title: res2.msg,
					// 		icon: "none"
					// 	})
					// }
				} else {
					uni.showToast({
						title: res1.msg,
						icon: "none"
					})
				}
				// ,
				// 

				// console.log("onAction", action)
				// if (action === "invoice") {
				// 	// 提交发票信息
				// 	this.submitInvoice()
				// 	this.showApplyInvoicePopup = false
				// }
			},

			async submitInvoice() {
				console.log("submitInvoice", this.invoiceForm)
			},

			async payNow() {
				console.log("payNow")
				uni.requestPayment({
					timeStamp: Date.now().toString(),
					nonceStr: Math.random().toString(36).substr(2, 15),
					package: "prepay_id=1234567890",
					signType: "MD5",
					paySign: "abcdefg1234567890",
					success: (res) => {
						uni.showToast({
							title: "支付成功",
							icon: "success"
						})
					},
					fail: (err) => {
						uni.showToast({
							title: "支付失败",
							icon: "none"
						})
					}
				})
			},

			getHeight() {
				const {
					safeArea: {
						height: safeAreaHeight
					}
				} = uni.getWindowInfo()
				const navHeight = uni.getStorageSync("navHeight")
				this.t2bHeight = uni.upx2px(px2rpx(navHeight))

				uni
					.createSelectorQuery()
					.select("#tabs")
					.boundingClientRect()
					.exec((res) => {
						if (res && res[0]) {
							let tabsHeight = res[0].height || 0
							tabsHeight = uni.upx2px(px2rpx(tabsHeight))
							this.scrollHeight = safeAreaHeight - this.t2bHeight - tabsHeight
						} else {
							this.scrollHeight = safeAreaHeight - this.t2bHeight
						}
					})
			}
		}
	}
</script>

<style scoped lang="scss">
	.footer-btn {
		background: #C6A772 100%;
		color: white;
		width: 500rpx;
		border-radius: 50rpx;
		padding: 20rpx 0;
		text-align: center;
		margin: 0 auto;
	}

	.popup-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		font-size: 28rpx;
		color: #333;
		box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
		margin: 0 30rpx;
		border-radius: 30rpx;
		padding-bottom: 20rpx;

		.pi1 {
			width: 80rpx;
		}

		.pi2 {
			flex: 1;

			>view {
				margin-top: 15rpx;
				font-size: 32rpx;

				>text {
					font-weight: 500;
				}
			}
		}
	}

	.delivery_view2 {
		.dv-top {
			>view:nth-child(1) {
				width: 300rpx !important;
			}
		}

		padding-bottom: 30rpx;
	}

	.delivery_view {
		margin: 20rpx 30rpx;
		background-color: white;
		border-radius: 30rpx;

		.dv-top {
			display: flex;
			padding: 0 30rpx;
			padding-top: 30rpx;

			>view:nth-child(1) {
				width: 150rpx;
			}

			>view:nth-child(2) {
				flex: 1;
				text-align: right;

				>view:nth-child(1) {
					padding-bottom: 20rpx;
				}

				>view:nth-child(2) {
					color: #6F6F6F;
				}
			}
		}
	}

	.footer-bar {
		height: 80rpx;
		background-color: #ffffff;
		display: flex;
		flex: 1;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx env(safe-area-inset-bottom);

		.footer-bar__total-info {
			margin-left: 30rpx;
		}

		&__total-info {
			display: flex;
			flex-direction: row;
			align-items: center;
			font-size: 28rpx;
			font-weight: bold;
			color: #333333;

			.footer-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				margin: 0; // 重置默认 margin
				margin-right: 30rpx;
				padding: 0; // 重置默认 padding
				background-color: transparent; // 使背景透明
				line-height: normal; // 重置行高
				font-size: 24rpx;

				// 在 uni-app 中，按钮的边框是通过 ::after 伪元素实现的，需要将其重置
				&::after {
					border: none;
				}

				text {
					font-size: 24rpx;
					margin-top: 4rpx;
					color: #666666;
				}

				&:last-child {
					margin-right: 0;
					/* 最后一个不需要右边距 */
				}
			}
		}

		&__action {
			display: flex;
			flex: 1;
			flex-direction: row;
			align-items: center;
			justify-content: flex-end;

			.btn-cancel {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 10rpx 25rpx;
				background-color: #f8f8f8;
				border-top-left-radius: 30rpx;
				border-bottom-left-radius: 30rpx;
				border: 1px solid #C6A670;
				font-size: 28rpx;
				color: #C6A670;
			}

			.btn-pay {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 12rpx 35rpx;
				background-color: #C6A670;
				border-radius: 30rpx;
				color: #ffffff;
				font-size: 28rpx;
				margin-right: 30rpx;
			}
		}
	}

	.wrapper {
		padding-bottom: env(safe-area-inset-bottom);
		background: #f5f5f5;
		height: 100vh;
		display: flex;
		flex-direction: column;

		.popup-footer {
			display: flex;
			justify-content: center;
			align-items: center;

			.wd-button {
				width: 100%;
				max-width: 600rpx;
				height: 80rpx;
				font-size: 32rpx;
			}
		}

		.popup-footer-selector {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.footer-check-box {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #333;

				.wd-checkbox {
					margin-right: 10rpx;
				}
			}
		}

		.refund-select {
			display: flex;
			flex-direction: column;

			&__view {
				background-color: #fff;

				&__title {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
					margin-bottom: 10rpx;
				}

				&__title_bar {
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 10rpx;

					.title {
						font-size: 28rpx;
						color: #333;
						font-weight: 500;
					}

					.right-bar {
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: flex-end;
						font-size: 28rpx;
						color: #333;
						font-weight: 500;

						.icon {
							margin-right: 10rpx;
							width: 36rpx;
							height: 36rpx;
						}
					}
				}

				&__items {
					display: flex;
					flex-direction: row;
					align-items: center;
					flex-wrap: wrap; // 添加这一行实现自动换行
					gap: 20rpx; // 添加间距
					margin-bottom: 20rpx; // 与下一个标题保持距离

					&__item {
						// 非必要，但建议设置宽度让布局更美观
						max-width: calc(50% - 10rpx); // 两列布局
						// 或者 max-width: calc(33.33% - 14rpx); // 三列布局

						// 现有样式保持不变
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 30rpx;
						padding: 10rpx 20rpx;
						border: 1rpx solid #f0f0f0;
						margin-bottom: 10rpx; // 垂直方向间距

						&__text {
							font-size: 26rpx;
							flex-grow: 1;
							text-align: center; // 文本居中
						}
					}

					&__item.is-active {
						background-color: #c6a670;
						color: #fff;
					}

					&__item.not-active {
						background-color: #fff;
						color: #666;
					}
				}
			}
		}

		.invoice-wrapper {
			display: flex;
			flex-direction: column;
			padding: 20rpx 30rpx;

			.title {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
				margin-bottom: 10rpx;
			}

			.tips {
				font-size: 24rpx;
				color: #999;
				margin-bottom: 20rpx;
			}
		}
	}

	.tab-bar {
		display: flex;
		justify-content: space-around;
		align-items: center;
		height: 100rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

		.tab-item {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			width: 33.33%;
			font-size: 32rpx;
			color: #333;
			font-weight: 500;
			border-bottom: 4rpx solid transparent;

			&.active {
				color: #07c160;
				border-color: #07c160;
			}

			.tab-item-title {
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}

	.order-item-wrapper {
		display: flex;
		flex-direction: column;
		padding: 20rpx 30rpx;
	}

	.top-add {
		margin: 20rpx 30rpx;
		overflow: hidden;
		background: white;
		border-radius: 20rpx;

		>image {
			width: calc(100% - 40rpx);
			height: auto;
			margin-left: 20rpx;
		}

		.ta-isDaf {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 30rpx 40rpx;
			position: relative;

			>view:nth-child(3) {
				position: absolute;
				right: 10rpx;
			}

			>view:nth-child(1) {
				width: 110rpx;

				>image {
					width: 100%;
					height: auto;
				}
			}

			>view:nth-child(2) {
				flex: 1;
				margin-left: 20rpx;

				>view:nth-child(1) {
					font-weight: bold;
					font-size: 32rpx;

					>text:nth-child(1) {
						display: inline-block;
						margin-right: 20rpx;
					}
				}

				>view:nth-child(2) {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					font-size: 26rpx;
					margin-top: 15rpx;
				}
			}
		}
	}

	.popup-footer-bar {
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		flex: 1;
		align-items: center;
		justify-content: center;

		.btn-action {
			display: flex;
			width: 100%;
			align-items: center;
			justify-content: center;
			padding: 20rpx;
			background-color: #ff5722;
			/* 使用主题色 */
			border-radius: 60rpx;
			color: #ffffff;
			font-size: 28rpx;
			border: none;
		}
	}

	.loading-wrapper {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 20rpx 0;
		background: #f5f5f5;

		.loading {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #999999;
			font-size: 24rpx;

			.loading-text {
				margin-right: 4rpx;
			}

			.dots {
				width: 24rpx;
				display: inline-block;

				&::after {
					content: ".";
					animation: dotAnimation 1.5s steps(4, end) infinite;
				}
			}
		}
	}

	@keyframes dotAnimation {
		0% {
			content: ".";
		}

		25% {
			content: "..";
		}

		50% {
			content: "...";
		}

		75% {
			content: "..";
		}

		100% {
			content: ".";
		}
	}
</style>
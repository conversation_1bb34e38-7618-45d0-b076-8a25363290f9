<template>
	<my-page background="#ececec" :refresher-config="{
      enabled: true,
      threshold: 100
    }" :load-more-config="{
      enabled: true,
      loadingText: '加载中...'
    }" :loading="loading" :finished="finished" :refreshing="refreshing" :show-footer="true" @refresh="onRefresh"
		@restore="onRestore" @abort="onAbort" @load-more="onLoadMore">
		<template #nav>
			<my-nav title="管理收货地址" backIcon />
		</template>

		<view class="content">
			<view class="address-card" v-for="item in list" :key="item.id">

				<view class="address-card__footer__info">
					<wd-checkbox class="default-checkbox" v-model="item.ifChoose"
						@change="onCheckboxChange(item.userAddressId)"></wd-checkbox>
				</view>

				<view class="address-center" @tap="onCheckboxChange(item.userAddressId)">
					<view class="address-card__header">
						<text class="name">{{ item.consigneeName }}</text>
						<text class="phone">{{ item.consigneePhone }}</text>
					</view>
					<view class="address-card__body">
						<text class="address">{{ item.address }}</text>
					</view>
				</view>

				<view class="address-card__footer__edit">
					<view class="address-card__footer__edit__block" @click="onEdit('edit', item.userAddressId)">
						<wd-icon name="edit" size="16" color="#696969"></wd-icon>
						<text class="edit-text">编辑</text>
					</view>
				</view>
			</view>
		</view>

		<template #footer>
			<view class="footer-bar">
				<view class="footer-bar__button" @click="onEdit('add', 'wx')">
					<wd-icon name="add" size="16" color="#333333"></wd-icon>
					<text class="button-text">选择微信地址</text>
				</view>
				<view class="footer-bar__button" @click="onEdit('add', 'new')">
					<wd-icon name="add" size="16" color="#333333"></wd-icon>
					<text class="button-text">添加新地址</text>
				</view>
			</view>
		</template>
	</my-page>
</template>

<script>
	import {
		getAddressList,
		editAddress,
		deleteAddress,
		setDefaultAddress,
		addAddress
	} from "@/api/address.js"

	/**
	 * 地址管理页面 api
	 * /system/address/list
	 */
	export default {
		name: "address",
		data() {
			return {
				loading: false,
				finished: false,
				refreshing: false,
				userAddressId: '',
				list: [
					// {
					//   id: 1,
					//   isDefault: true,
					//   name: "张三",
					//   phone: "13800000000",
					//   address: "北京市朝阳区某某街道某某小区"
					// }
				]
			}
		},
		onShow() {
			this._getAddressList()
		},

		onLoad(options) {
			console.log(options.id)
			this.userAddressId = options.userAddressId
		},


		methods: {
			// 获取地址列表
			async _getAddressList() {
				try {
					const result = await getAddressList({
						userId: uni.getStorageSync("userId")
					})

					const addressData = result
					if (addressData && addressData.length > 0) {
						// 成功获取地址数据，转换数据格式
						for (let i of addressData) {
							i.ifChoose = false
						}
						for (let i of addressData) {
							if (i.userAddressId === this.userAddressId) {
								i.ifChoose = true
							}
						}
						this.list = addressData.map((address) => ({
							address: this._formatAddress(address),
							...address
						}))
					} else {
						this.list = []
					}
				} catch (error) {} finally {
					this.loading = false
					this.refreshing = false
				}
			},

			// 格式化地址显示
			_formatAddress(address) {
				const parts = [
					address.province,
					address.city,
					address.district,
					address.detail,
				].filter(Boolean)
				return parts.join("") || address.address || "地址信息"
			},

			// 设置默认地址
			async _setDefaultAddress(addressId, value, obj) {
				try {
					let submitObj = JSON.parse(JSON.stringify(obj))
					submitObj.isDefault = value ? 1 : 0
					const result = await editAddress(submitObj)
					const {
						code,
						msg
					} = result || {}

					if (code === 200) {
						uni.showToast({
							title: msg,
							icon: "success",
							duration: 1500
						})
						// 重新加载地址列表
						this._getAddressList()
					} else {
						uni.showToast({
							title: msg || "设置失败",
							icon: "none"
						})
					}
				} catch (error) {
					console.log("设置默认地址失败:", error)
					uni.showToast({
						title: "设置失败",
						icon: "none"
					})
				}
			},

			// 删除地址
			async _deleteAddress(addressId) {
				try {
					const result = await deleteAddress({
						userAddressIds: [addressId]
					})
					const {
						code,
						msg
					} = result || {}

					if (code === 200) {
						uni.showToast({
							title: "删除成功",
							icon: "success"
						})
					} else {
						uni.showToast({
							title: msg || "删除失败",
							icon: "none"
						})
					}
					this._getAddressList()
				} catch (error) {
					console.log("删除地址失败:", error)
					uni.showToast({
						title: "删除失败",
						icon: "none"
					})
				}
			},

			onRefresh() {
				console.log("onRefresh")
				if (this.refreshing) return
				this.refreshing = true
				this._getAddressList()
			},

			onRestore() {
				console.log("onRestore")
				this.refreshing = false
			},

			onLoadMore() {
				if (this.loading || this.finished) return
				this.loading = true
				setTimeout(() => {
					this.loading = false
					// 模拟加载更多数据
					// this.orderList.push(...newData)
					// 如果没有更多数据，设置 finished 为 true
					// this.finished = true
				}, 1000)
			},
			chooseAddress(id, value, obj) {
				this.userAddressId = id
				for (let i of this.list) {
					i.ifChoose = false
					if (i.userAddressId === this.userAddressId) {
						i.ifChoose = true
					}
				}
			},
			onCheckboxChange(id, value, obj) {
				this.userAddressId = id
				for (let i of this.list) {
					i.ifChoose = false
					if (i.userAddressId === this.userAddressId) {
						i.ifChoose = true
					}
				}
				uni.$emit('navigateBackData', {
					userAddressId: this.userAddressId
				});

				uni.navigateBack({
					delta: 1
				});
			},

			onAbort() {
				console.log("onAbort")
				this.refreshing = false
			},

			getListInfo() {
				console.log("getListInfo")
				setTimeout(() => {
					this.refreshing = false
				}, 1000)
			},

			onEdit(action, id) {
				console.log(`onEdit action: ${action}, id: ${id}`)
				if (action === "edit") {
					uni.navigateTo({
						url: `/packages/profile/address/edit?id=${id}`
					})
				} else if (action === "delete") {
					this._confirmDeleteAddress(id)
				} else if (action === "add") {
					if (id === "wx") {
						try {
							// 直接调用chooseAddress，不需要预先authorize
							uni.chooseAddress({
								success: async (res) => {
									console.log("选择的微信地址:", res)

									const addressData = {
										detail: res.detailInfo,
										isDefault: 0,
										userId: uni.getStorageSync("userId"),
										province: res.provinceName,
										provinceId: res.postalCode + '',
										city: res.cityName,
										// cityId: res.cityId+'',
										district: res.countyName,
										// districtId: res.districtId+'',
										consigneeName: res.userName,
										consigneePhone: res.telNumber,
									}
									const result = await addAddress(addressData)
									if (result.code === 200) {
										uni.showToast({
											title: result.msg,
											icon: "success"
										})
										this._getAddressList()
									} else {
										uni.showToast({
											title: result.msg,
											icon: "none"
										})
									}
								},
								fail: (err) => {
									console.error("选择微信地址失败:", err)
									if (err.errMsg.includes("requiredPrivateInfos")) {
										uni.showModal({
											title: "权限配置缺失",
											content: '请在app.json中添加 "requiredPrivateInfos": ["chooseAddress"] 配置',
											showCancel: false
										})
									} else {
										uni.showToast({
											title: "选择地址失败",
											icon: "none"
										})
									}
								}
							})
						} catch (e) {
							console.error("调用微信地址API异常:", e)
						}
					} else if (id === "new") {
						uni.navigateTo({
							url: "/packages/profile/address/edit"
						})
					}
				} else {
					console.warn(`未知操作: ${action}`)
				}
			},

			// 确认删除地址
			_confirmDeleteAddress(id) {
				console.log(`删除地址 ID: ${id}`)
				uni.showModal({
					title: "确认删除",
					content: "确定要删除此地址吗？",
					success: (res) => {
						if (res.confirm) {
							this._deleteAddress(id)
						}
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		padding: 40rpx 10rpx;

		.address-card {
			background: #fff;
			border-radius: 10px;
			margin-bottom: 20rpx;
			padding: 30rpx 10rpx;
			display: flex;

			.address-card__footer__info {
				width: 50rpx;
			}

			.address-center {
				flex: 1;
				margin: 0 20rpx;
			}

			.address-card__footer__edit {
				width: 110rpx;
			}

			&__header {
				display: flex;
				align-items: center;
				margin-bottom: 10rpx;
				font-size: 32rpx;
				color: #333333;

				.phone {
					margin-left: 20rpx;
				}
			}

			&__body {
				margin-bottom: 20rpx;

				.address {
					color: #696969;
					font-size: 28rpx;
				}
			}

			&__line {
				height: 1px;
				background: #f0f0f0;
				margin: 20rpx 0;
			}

			&__footer {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 28rpx;
				color: #999999;

				&__info {
					display: flex;
					align-items: center;

					.default-checkbox {
						margin-right: 10rpx;
					}

					.default-text {
						color: #333333;
					}
				}

				&__edit {
					display: flex;
					align-items: center;

					&__block {
						display: flex;
						align-items: center;

						text {
							color: #696969;
							font-size: 28rpx;
							margin-left: 10rpx;
						}

						&:not(:last-child) {
							margin-right: 30rpx;
						}

						.wd-icon {
							margin-right: 10rpx;
						}
					}
				}
			}
		}
	}

	.footer-bar {
		height: 100rpx;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-top: 1px solid #f0f0f0;
		padding: 0 30rpx;
		margin-bottom: env(safe-area-inset-bottom);

		&__button {
			display: flex;
			align-items: center;
			justify-content: center;
			background: #ececec;
			color: #fff;
			border-radius: 50px;
			padding: 20rpx 30rpx;
			flex: 1;
			margin: 0 10rpx;

			&:first-child {
				margin-left: 0;
			}

			&:last-child {
				margin-right: 0;
			}

			.button-text {
				margin-left: 10rpx;
				font-size: 28rpx;
				color: #333333;
			}
		}

		text {
			color: #333333;
			font-size: 32rpx;
		}
	}
</style>
<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: false
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-footer="true"
  >
    <template #nav>
      <my-nav :title="title" backIcon />
    </template>

    <view class="content">
      <wd-input
        label="收货人"
        type="text"
        label-width="120rpx"
        v-model="addressInfo.name"
        placeholder="请填写收货人姓名"
        @change="handleChange"
      />
      <wd-input
        label="手机号码"
        type="tel"
        label-width="120rpx"
        v-model="addressInfo.phone"
        placeholder="请填写收货人手机号"
        @change="handleChange"
      />

      <view class="select-col-address-bar">
				<!-- addressInfo.address -->
        <wd-col-picker
          label="收货地址"
          label-width="120rpx"
          placeholder="请选择省份/城市/地区/街道"
          v-model="addressInfo.selectArea"
          :columns="area"
          ellipsis
          custom-class="select-col-address-bar__col"
          :column-change="columnChange"
          @confirm="handleConfirm"
        ></wd-col-picker>

        <wd-icon
          name="location"
          size="18"
          color="#333"
          class="select-col-address-bar__picker"
          @click="handleLocationClick"
        />
      </view>

      <wd-cell-group border>
        <wd-textarea
          label-width="120rpx"
          v-model="addressInfo.detail"
          label="详细地址"
          clearable
          placeholder="请填写小区/门牌号/楼层"
          auto-height
        />
      </wd-cell-group>

      <view class="paste-address">
        <view class="paste-address__input">
          <wd-textarea
            v-model="pastedText"
            placeholder="请粘贴包含地址和手机号的信息"
            class="paste-address__textarea"
            :height="200"
            no-border
          />
        </view>
        <view class="paste-address__actions">
          <view class="paste-address__action" @click="handleClearPasted">清空</view>
          <view class="paste-address__action paste-address__action--primary" @click="handleParseAddress"
            >粘贴并识别地址</view
          >
        </view>
      </view>

      <view class="set-switch-bar">
        <view class="set-switch-bar__label">设为默认地址</view>

        <view class="set-switch-bar__item">
          <wd-switch v-model="addressInfo.isDefault" size="small" @change="handleChange" />
        </view>
      </view>
    </view>

    <template #footer>
      <view class="my-page__footer">
        <wd-button block type="primary" @click="handleSave" custom-class="custom-btn">保存</wd-button>
      </view>
    </template>
  </my-page>
</template>

<script>
  import { useColPickerData } from "@/hooks/useColPickerData"
  import { addAddress, editAddress, getAddressInfo } from "@/api/address.js"
  const { colPickerData, findChildrenByCode } = useColPickerData()

  /**
   * 修改地址 api
   * /system/address/add
   * /system/address/edit
   */
  export default {
    data() {
      return {
        title: "管理收货地址",
        type: "add", // 默认添加地址
        addressId: null, // 编辑时的地址ID

        pastedText: "",
				hisArr: [],
        addressInfo: {
          isDefault: false,
          name: "",
          phone: "",
          selectArea: [],
          address: "",
          detail: "",
          province: "",
          city: "",
          district: ""
        },

        area: [
          colPickerData.map((item) => {
            return {
              value: item.value,
              label: item.text
            }
          })
        ] // 地址数据
      }
    },

    onLoad(options = {}) {
      const { type, id } = options
      if (!id) {
        this.title = "新增收货地址"
      } else {
        this.type = "edit"
        this.title = "编辑收货人信息"
        this.addressId = id
        this._getAddressInfo(id)
      }
    },

    methods: {
      handleClearPasted() {
        this.pastedText = ""
      },

      handleParseAddress() {
        if (!this.pastedText) return

        // Extract phone number (simple pattern matching for Chinese mobile numbers)
        const phoneMatch = this.pastedText.match(/(\+?86)?1[3-9]\d{9}/)
        if (phoneMatch) {
          this.addressInfo.phone = phoneMatch[0].replace(/^\+?86/, "")
        }

        // Extract name (assuming it's at the beginning of the text)
        const nameMatch = this.pastedText.match(/^[^\d+\s]+/)
        if (nameMatch) {
          this.addressInfo.name = nameMatch[0].trim()
        }

        // The rest is treated as address
        let address = this.pastedText
        if (nameMatch) address = address.substring(nameMatch[0].length).trim()
        if (phoneMatch) address = address.replace(phoneMatch[0], "").trim()

        this.addressInfo.detail = address

        // Clear the pasted text after parsing
        this.pastedText = ""
      },
      handleChange() {
        // 处理输入变化
        console.log("输入变化:", this.addressInfo)
      },

      columnChange({ selectedItem, resolve, finish }) {
				console.log(selectedItem.value, selectedItem.label)
				this.hisArr.push({
					value: selectedItem.value,
					label: selectedItem.label
				})
        const areaData = findChildrenByCode(colPickerData, selectedItem.value)
        if (areaData && areaData.length) {
          resolve(
            areaData.map((item) => {
              return {
                value: item.value,
                label: item.text
              }
            })
          )
        } else {
          finish()
        }
      },

      handleConfirm({ value }) {
				for(let i of this.hisArr) {
					if(i.value === value[0]) {
						this.addressInfo.province = i.label
						this.addressInfo.provinceId = i.value
					}
					if(i.value === value[1]) {
						this.addressInfo.city = i.label
						this.addressInfo.cityId = i.value
					}
					if(i.value === value[2]) {
						this.addressInfo.district = i.label
						this.addressInfo.districtId = i.value
					}
				}
      },

      // 获取地址详情（编辑时使用）
      async _getAddressInfo(addressId) {
        try {
          const result = await getAddressInfo({ userAddressId: addressId })
          const { code, msg, data } = result || {}

          if (code === 200 && data) {
						this.addressInfo = {
							...data,
						  name: data.consigneeName,
						  phone: data.consigneePhone,
							address: `${data.province}${data.city}${data.district}`,
						  isDefault: data.isDefault === '0' ? false : true,
							selectArea: []
						}
						// if(data.cityId && data.districtId) {
						// 	this.addressInfo.selectArea = [data.provinceId, data.cityId, data.districtId]
						// }
            console.log("地址详情加载成功:", this.addressInfo)
          } else {
            console.log("获取地址详情失败:", msg)
            uni.showToast({
              title: msg || "获取地址信息失败",
              icon: "none"
            })
          }
        } catch (error) {
          console.log("获取地址详情失败:", error)
          uni.showToast({
            title: "获取地址信息失败",
            icon: "none"
          })
        }
      },

      async handleSave() {
        // 保存地址信息
        if (!this.addressInfo.name || !this.addressInfo.phone || !this.addressInfo.detail) {
          uni.showToast({
            title: "请填写完整的收货人信息",
            icon: "none"
          })
          return
        }

        try {
          const addressData = {
            province: this.addressInfo.province,
            provinceId: this.addressInfo.provinceId+'',
            city: this.addressInfo.city,
            cityId: this.addressInfo.cityId+'',
            district: this.addressInfo.district,
            districtId: this.addressInfo.districtId+'',
            consigneeName: this.addressInfo.name,
            consigneePhone: this.addressInfo.phone,
            detail: this.addressInfo.detail,
            isDefault: this.addressInfo.isDefault ? 1 : 0,
						userId: uni.getStorageSync("userId")
          }
					
					let result = {};
          if (this.type === "edit" && this.addressId) {
            // 编辑地址
            result = await editAddress({
              ...addressData,
              userAddressId: this.addressId
            })
          } else {
            // 新增地址
            result = await addAddress(addressData)
          }

          const { code, msg } = result
          if (code == 200) {
            uni.showToast({
              title: this.type === "edit" ? "地址修改成功" : "地址添加成功",
              icon: "success"
            })
            // 返回上一页
            setTimeout(() => {
              uni.navigateBack()
            }, 1500)
          } else {
            uni.showToast({
              title: msg || "保存失败",
              icon: "none"
            })
          }
        } catch (error) {
          console.log("保存地址失败:", error)
          uni.showToast({
            title: "保存失败",
            icon: "none"
          })
        }
      },

      handleLocationClick() {
        // 打开微信地图选择位置
        uni.chooseLocation({
          success: (res) => {
            this.addressInfo.address = res.address
            console.log("选择的地址:", res)
          },
          fail: (err) => {
            console.error("选择地址失败:", err)
          }
        })
      }
    }
  }
</script>

<style lang="scss">
  .paste-address {
    background: #fff;
    padding: 24rpx 30rpx;
    margin-bottom: 20rpx;

    &__input {
      border: 1px solid #e6e6e6;
      border-radius: 8rpx;
      padding: 16rpx;
      margin-bottom: 20rpx;
    }

    &__textarea {
      width: 100%;
      min-height: 100rpx;
      font-size: 28rpx;
      line-height: 1.5;
    }

    &__actions {
      display: flex;
      justify-content: flex-end;
      gap: 30rpx;
    }

    &__action {
      font-size: 26rpx;
      color: #666;

      &--primary {
        color: #4d80f0;
      }
    }
  }
</style>

<style scoped lang="scss">
  .content {
    .select-col-address-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 30rpx;
      background-color: #ffffff;

      &__col {
        flex: 1;
      }

      &__picker {
        flex-shrink: 0; // 防止图标被压缩
        margin-left: 10rpx;
      }

      :deep(.wd-col-picker) {
        width: 100% !important;
      }

      // 确保内部元素也是充满宽度的
      :deep(.wd-col-picker__wrap),
      :deep(.wd-cell),
      :deep(.wd-cell__right) {
        width: 100%;
        flex: 1;
      }
    }

    .set-switch-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 30rpx;
      background-color: #ffffff;

      &__label {
        font-size: 28rpx;
        color: #333;
      }

      &__item {
        display: flex;
        align-items: center;
      }
    }
  }

  .my-page__footer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    background-color: #ffffff;
    border-top: 1px solid #e5e5e5;
    margin-bottom: env(safe-area-inset-bottom);

    :deep() {
      .custom-btn {
        width: 100%;
        height: 80rpx;
        font-size: 32rpx;
        background-color: #c6a670;
        color: #ffffff;
        border-radius: 8rpx;
      }
    }
  }
</style>

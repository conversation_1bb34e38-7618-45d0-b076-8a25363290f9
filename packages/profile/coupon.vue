<template>
  <view class="wrapper">
    <my-nav title="优惠券" backIconColor="black" textColor="#000" backIcon />
    <view class="container">
      <wd-tabs id="tabs" inactiveColor="#000" color="#C6A670" animated :active="active" @click="onTabClick">
        <wd-tab title="未使用" name="unused" />
        <wd-tab title="已使用" name="used" />
        <wd-tab title="已过期" name="expired" />
      </wd-tabs>

      <scroll-view
        scroll-y
        scroll-with-animation
        refresher-enabled
        refresher-default-style="black"
        lower-threshold="50"
        upper-threshold="50"
        refresher-threshold="100"
        :refresher-triggered="refreshing"
        refresher-background="transparent"
        @refresherrefresh="onRefresh"
        @refresherrestore="onRestore"
        @refresherabort="onAbort"
        :style="{ height: scrollHeight + 'px' }"
      >
        <view class="list-view">
          <view class="coupon-card">
            <image src="../../static/logo.png" mode="widthFix" />
            <view class="coupon-content">
              <text> 优惠券名称：满100减10</text>
              <text>有效期：2021-12-31</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
  import { px2rpx } from "@/common/util"
  import CouponItem from "../../components/coupon-item/coupon-item.vue"
  import { getUnusedCoupons, getUsedCoupons, getExpiredCoupons } from "@/api/coupon.js"

  /**
   * 优购码页面api
   * /store/code/appList
   */
  export default {
    name: "coupon",
    components: { CouponItem },

    data() {
      return {
        active: "unused",
        tabList: [
          {
            title: "未使用",
            type: "unused"
          },
          {
            title: "已使用",
            type: "used"
          },
          {
            title: "已过期",
            type: "expired"
          }
        ],
        refreshing: false,
        scrollHeight: 0,
        scrollTop: 0,
        couponList: []
      }
    },

    created() {
      this.getHeight()
    },

    mounted() {
      this.getListInfo()
    },

    methods: {
      onTabClick({ index, name }) {
        this.active = name
        this._getCouponList()
      },

      // 获取优购码列表
      async _getCouponList() {
        try {
          let result
          switch (this.active) {
            case "unused":
              result = await getUnusedCoupons()
              break
            case "used":
              result = await getUsedCoupons()
              break
            case "expired":
              result = await getExpiredCoupons()
              break
            default:
              result = await getUnusedCoupons()
          }

          const { code, msg, data, rows } = result || {}
          const couponData = data || rows

          if (code === 200 && couponData) {
            this.couponList = couponData
            console.log(`${this.active}优购码加载成功:`, this.couponList.length, "张")
          } else {
            console.log(`${this.active}优购码接口返回空数据:`, msg)
            this.couponList = []
          }
        } catch (error) {
          console.log(`获取${this.active}优购码失败:`, error)
          this.couponList = []
        } finally {
          this.refreshing = false
        }
      },

      async getListInfo() {
        this._getCouponList()
      },

      onRefresh() {
        if (this.refreshing) return
        this.refreshing = true
        this.getListInfo()
      },

      onRestore() {
        console.log("onRestore")
        this.refreshing = false
      },

      onAbort() {
        console.log("onAbort")
        this.refreshing = false
      },

      getHeight() {
        const {
          safeArea: { height: safeAreaHeight }
        } = uni.getWindowInfo()
        const navHeight = uni.getStorageSync("navHeight")
        this.t2bHeight = uni.upx2px(px2rpx(navHeight))

        uni
          .createSelectorQuery()
          .select("#tabs")
          .boundingClientRect()
          .exec((res) => {
            if (res && res[0]) {
              let tabsHeight = res[0].height || 0
              tabsHeight = uni.upx2px(px2rpx(tabsHeight))
              this.scrollHeight = safeAreaHeight - this.t2bHeight - tabsHeight
            } else {
              this.scrollHeight = safeAreaHeight - this.t2bHeight
            }
          })
      }
    }
  }
</script>

<style scoped lang="scss">
  .wrapper {
    padding-bottom: env(safe-area-inset-bottom);
    background: #f5f5f5;
  }

  .container {
    display: flex;
    flex-direction: column;
    height: 100vh;

    .tab-bar {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 100rpx;
    }

    .list-view {
      flex: 1;
      overflow: auto;

      .coupon-card {
        display: flex;
        align-items: center;
        padding: 20upx;
        background: #fff;
        margin: 20rpx;
        border-radius: 10upx;
        box-shadow: 0 0 10upx rgba(0, 0, 0, 0.1);

        image {
          width: 100upx;
          height: 100upx;
        }

        .coupon-content {
          display: flex;
          flex-direction: column;

          text {
            font-size: 30upx;
            color: #c6a670;
          }
        }
      }
    }
  }
</style>

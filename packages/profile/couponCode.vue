<template>
  <view class="wrapper">
    <my-nav title="优购码" backIconColor="black" textColor="#000" backIcon />
    <view class="container">
      <wd-tabs id="tabs" inactiveColor="#000" color="#C6A670" animated :active="active" @click="onTabClick">
        <wd-tab title="未使用" :name="0" />
        <wd-tab title="已使用" :name="1" />
        <wd-tab title="已过期" :name="2" />
      </wd-tabs>

      <scroll-view
        scroll-with-animation
        refresher-enabled="true"
        refresher-default-style="black"
        :refresher-threshold="100"
        :refresher-triggered="refreshing"
        refresher-background="transparent"
        @refresherrefresh="onRefresh"
        @refresherrestore="onRestore"
        @refresherabort="onAbort"
        :style="{ height: scrollHeight + 'px' }"
      >
        <view class="list-view">
          <view v-for="(coupon, index) in coupons" :key="index">
            <coupon-item :coupon="coupon" />
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
  import { px2rpx } from "@/common/util"
  import CouponItem from "../../components/coupon-item/coupon-item.vue"
	import { getCouponList } from '@/api/coupon.js'

  /**
   * 优惠码页面 api
   * /store/code/appList
   */
  export default {
    name: "couponCode",
    components: { CouponItem },

    data() {
      return {
        active: 0,
        tabList: [
          {
            title: "未使用",
            type: 0
          },
          {
            title: "已使用",
            type: 1
          },
          {
            title: "已过期",
            type: 2
          }
        ],
        coupons: [],
        refreshing: false,
        scrollHeight: 0,
        scrollTop: 0
      }
    },

    created() {
      this.getHeight()
    },

    mounted() {
      this.getListInfo()
    },

    methods: {
      onTabClick(type) {
        this.active = type.name
				this.getListInfo()
      },
      async getListInfo() {
				const res = await getCouponList({
					usedStatus: this.active,
					pageNum: 1,
					pageSize: 1000
				})
				this.coupons = res.rows || []
				this.refreshing = false
        // await new Promise((resolve) => {
        //   setTimeout(() => {
        //     this.refreshing = false
        //     resolve()
        //   }, 2000)
        // })
      },

      onRefresh() {
        if (this.refreshing) return
        this.refreshing = true
        this.getListInfo()
      },

      onRestore() {
        console.log("onRestore")
        this.refreshing = false
      },

      onAbort() {
        console.log("onAbort")
        this.refreshing = false
      },

      getHeight() {
        const { windowHeight, safeArea } = uni.getSystemInfoSync()
        const navHeight = uni.getStorageSync("navHeight")
        const t2bHeight = uni.upx2px(px2rpx(navHeight))
        const safaBottom = windowHeight - safeArea.bottom

        uni
          .createSelectorQuery()
          .select("#tabs")
          .boundingClientRect()
          .exec((res) => {
            const { height = 0 } = res[0] || {}
            this.scrollHeight = windowHeight - t2bHeight - height - safaBottom
          })
      }
    }
  }
</script>

<style scoped lang="scss">
  .wrapper {
    padding-bottom: env(safe-area-inset-bottom);
    background: #f5f5f5;
  }

  .container {
    display: flex;
    flex-direction: column;
    height: 100vh;

    .tab-bar {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 100rpx;
    }

    .list-view {
      flex: 1;
      overflow: auto;
    }
  }
</style>

<template>
  <view class="wrapper">
    <my-nav
        title="积分"
        backIconColor="black"
        textColor="#000"
        backIcon
    />

    <scroll-view
        scroll-with-animation
        refresher-enabled="true"
        refresher-default-style="black"
        :refresher-threshold="100"
        :refresher-triggered="refreshing"
        refresher-background="transparent"
        @refresherrefresh="onRefresh"
        @refresherrestore="onRestore"
        @refresherabort="onAbort"
        :style="{ height: scrollHeight + 'px' }">
      <view class="list-view">
        <view class="points">
          <image src="../../static/logo.png" mode="widthFix"></image>
          <text>30000积分</text>
        </view>

      </view>
    </scroll-view>
  </view>
</template>

<script>
import { px2rpx } from "../../common/util.js";

export default {
  name: "points",
  data() {
    return {
      refreshing: false,
      scrollHeight: 0,
    };
  },

  created() {
    this.getHeight();
  },

  mounted() {
    this.getListInfo();
  },

  methods:{
    onRefresh() {
      if (this.refreshing) return;
      this.refreshing = true;
      this.getListInfo();
    },

    onRestore() {
      console.log("onRestore");
      this.refreshing = false;
    },

    onAbort() {
      console.log("onAbort");
      this.refreshing = false;
    },

    getListInfo() {
      setTimeout(() => {
        this.refreshing = false;
      }, 1000);
    },

    getHeight() {
      const { windowHeight, safeArea } = uni.getSystemInfoSync();
      const navHeight = uni.getStorageSync("navHeight");
      const t2bHeight = uni.upx2px(px2rpx(navHeight));
      const safaBottom = windowHeight - safeArea.bottom;

      this.scrollHeight = windowHeight - t2bHeight - safaBottom;
    },

  }
};
</script>

<style scoped lang="scss">
.wrapper{
  padding-bottom: env(safe-area-inset-bottom);
  background: #f5f5f5;
}

.list-view {
  padding: 20upx;
}

.points {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20upx;
  background: #fff;
  border-radius: 10upx;

  image {
    width: 100upx;
    height: 100upx;
  }

  text {
    font-size: 32upx;
    color: #333;
  }
}
</style>

<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-fixed-bar="true"
    :fixed-bar-height="fixedBarHeight"
  >
    <template #nav>
      <my-nav title="搜索商品" backIcon />
    </template>

    <template #fixedBar>
      <view class="fixed-bar" id="search-fixed-bar">
        <wd-search
          v-model="keyword"
          @change="onChange"
          @blur="onBlur"
          @clear="onClear"
          placeholder="请输入商品名称"
          custom-class="search-bar"
          placeholder-left
        >
          <template #suffix>
            <view class="search-right-block" v-if="resultList.length > 0" @click="toggleListWay">
              <wd-icon name="view-list" size="18px" v-if="showListWay === 'list'"></wd-icon>
              <wd-icon name="app" size="18px" v-else></wd-icon>
            </view>
          </template>
        </wd-search>
        <view class="select-bar" v-if="resultList.length > 0">
          <view
            class="switch-tips"
            :class="sortInfo.type === 'default' ? 'switch-tips-active' : ''"
            @tap="onSelectAction('default')"
            >推荐
          </view>
          <view
            class="switch-tips"
            :class="sortInfo.type === 'lasted' ? 'switch-tips-active' : ''"
            @tap="onSelectAction('lasted')"
            >最新
          </view>

          <wd-sort-button title="价格" v-model="sortInfo.price" allow-reset @change="onPriceSort" />
          <view class="brand-block" @tap="showSlider = true">
            <text class="pageBrandName">{{ sortInfo.brand }}</text>
            <wd-icon name="arrow-down" size="16px" color="#999"></wd-icon>
          </view>

          <view class="filter-bar" @tap="showSlider = true">
            <text>筛选</text>
            <wd-icon name="filter" size="16px" color="#999"></wd-icon>
          </view>
        </view>
      </view>
    </template>

    <view class="content">
      <view class="idle-block" v-if="!keyword && !resultList.length">
        <view class="title-bar">
          <view class="left-title">搜索历史</view>
          <view class="right-bar" @tap="onClearHistory">
            <text>清除</text>
            <wd-icon name="delete" size="16px" color="#999" @click="historyKeys = []"></wd-icon>
          </view>
        </view>
        <view class="wrap-history">
          <view
            class="history-item"
            v-for="(item, index) in historyKeys"
            :key="index"
            @click="onHistoryItemClick(item)"
          >
            {{ item }}
          </view>
        </view>

        <!-- <view class="title-bar">
          <view class="left-title">猜你喜欢</view>
          <view class="right-bar" @tap="onChangeGuess">
            <text>换一换</text>
            <wd-icon name="refresh" size="16px" color="#999"></wd-icon>
          </view>
        </view>
        <view class="wrap-history">
          <view class="history-item" v-for="(item, index) in guessKeys" :key="index" @click="onHistoryItemClick(item)">
            {{ item }}
          </view>
        </view> -->

        <!-- <view class="hot-title-bar">
          <image src="/static/search/ic-hot.png" class="icon" mode="widthFix" />
          <view class="left-title">热搜榜</view>
        </view>
        <view class="hot-list">
          <view class="hot-item-container" v-for="(item, index) in hotList" :key="index" @click="keyword = item.label">
            <view class="hot-item" @tap="onHotItemClick(item)">
              <image :src="item.coverImage" class="hot-item__image" mode="widthFix"></image>
              <view class="hot-item__ranking-bg">
                <image
                  src="/static/search/ic-ranking-top.png"
                  v-if="index <= 2"
                  class="ranking-icon"
                  mode="widthFix"
                ></image>
                <image src="/static/search/ic-ranking-normal.png" v-else class="ranking-icon" mode="widthFix"></image>
              </view>
              <view class="hot-item__ranking">
                <text class="ranking">{{ item.ranking }}</text>
              </view>
              <view class="hot-item__info">
                <view class="title">{{ item.title }}</view>
                <view class="description">{{ item.description }}</view>
              </view>
            </view>
          </view>
        </view> -->
      </view>
      <view class="content-block">
        <view class="product-list" :class="{ 'grid-view': showListWay === 'grid' }">
          <view
            v-for="(item, index) in resultList"
            :key="index"
            class="product-item-wrapper"
            :style="{ width: showListWay === 'grid' ? 'calc(50% - 20rpx)' : '100%' }"
          >
            <product-card :product="item" @show-detail="onShowDetail" />
          </view>
        </view>
      </view>
    </view>
  </my-page>

  <wd-popup
    v-model="showSlider"
    :z-index="100"
    position="right"
    transition="fade"
    @close="handleClose"
    safe-area-inset-bottom
    custom-class="slider-popup"
  >
    <view class="slide-content">
      <view class="slide-header" :style="{ marginTop: navBarHeight + 'px' }">
        <view class="header-title">筛选</view>
      </view>
      <view class="slide-body">
        <view class="select-box">
          <view class="title">价格区间</view>
          <view class="price-range-block">
            <wd-input-number
              v-model="sortInfo.minPrice"
              placeholder="请输入最低价"
              @change="handleChangeMin"
              :step="1"
              input-width="70px"
            />
            <text>至</text>
            <wd-input-number
              v-model="sortInfo.maxPrice"
              placeholder="请输入最高价"
              @change="handleChangeMax"
              :step="1"
              input-width="70px"
            />
          </view>
        </view>

        <view class="select-box">
          <view class="title">品牌</view>
          <view class="brand-list">
            <view
              class="brand-item"
              v-for="(brand, index) in sortInfo.preAllBrands"
              :key="index"
              :class="{ active: sortInfo.brand === brand.label }"
              @click="sortInfo.brand = brand.label"
            >
              {{ brand.label }}
            </view>
          </view>
        </view>
      </view>
      <view class="slide-footer">
        <wd-button type="warning" @click="handleReset">重置</wd-button>
        <wd-button type="primary" @click="handleConfirm">确定</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script>
  const MOCK_LIST = [
    {
      productId: "101",
      title: "HUAWEI MatePad Pro13.2英寸平板电脑WiFi连接支持通话",
      description: "13.2英寸柔性0LED大屏，鸿蒙专业系统，支持多屏协同",
      originalPrice: 4999,
      price: 4199,
      imageUrl: "https://tc.666150.xyz/i/2025/07/02/irbggu.png"
    }
  ]

  import { searchProducts, getHotSearchList, getProductBrand } from "@/api/product.js"
  import { getSearchHistory, getGuessYouLikeProducts } from "@/api/system.js"

  /**
   * 搜索页面api
   * /store/product/appList
   */
  export default {
    name: "SearchIndex",
    data() {
      return {
        keyword: "",
        searchType: "goods",
        resultList: [],
        showListWay: "list", // 列表展示方式，默认为列表
        fixedBarHeight: 0, // 搜索栏高度

        historyKeys: [],
        guessKeys: ["苹果", "华为", "小米", "三星", "联想", "戴尔"],
        hotList: [
          {
            productId: "1",
            title: "HUAWEI MatePad Pro13.2英寸曜金黑",
            description: "年中大促，直降800",
            ranking: 1,
            coverImage: "https://tc.666150.xyz/i/2025/07/02/irbggu.png"
          },
				],

        sortInfo: {
          price: 0,
          type: "default",
          preAllBrands: [],
          brand: "全部", // 品牌选择，默认为全部
          minPrice: 0,
          maxPrice: 10000 // 默认价格区间
        },

        showSlider: false, // 是否显示侧边栏
        navBarHeight: uni.getStorageSync("navHeight")
      }
    },

    onLoad(options = {}) {
      console.log("options", options)
      // 可以在这里处理传入的参数
      if (options.keyword) {
        this.keyword = options.keyword
      }
      if (options.searchType) {
        this.searchType = options.searchType
      }
    },

    created() {
      console.log("created")
      this.getFixedBarHeight()
    },

    onShow() {
      this._loadInitialData()
			this._getProductBrand()
    },

    methods: {
			async _getProductBrand() {
				const res = await getProductBrand()
				this.sortInfo.preAllBrands = res.data || []
			},
      // 加载初始数据
      async _loadInitialData() {
				this._getSearchHistory()
        // await Promise.all([this._getSearchHistory(), this._getGuessYouLike(), this._getHotSearchList()])
      },

      // 获取搜索历史
      async _getSearchHistory() {
        try {
          const result = await getSearchHistory()
          const { code, msg, data, rows } = result || {}
          const historyData = rows

          if (code === 200 && historyData && historyData.length > 0) {
            this.historyKeys = historyData.map((item) => item.queryText)
            console.log("搜索历史加载成功:", this.historyKeys)
          } else {
            console.log("搜索历史接口返回空数据，使用默认数据:", msg)
            // 保持原有的 mock 数据
          }
        } catch (error) {
          console.log("获取搜索历史失败，使用默认数据:", error)
          // 保持原有的 mock 数据
        }
      },

      // 获取猜你喜欢
      async _getGuessYouLike() {
        try {
          const result = await getGuessYouLikeProducts({ page: 1, size: 6 })
          const { code, msg, data, rows } = result || {}
          const guessData = data || rows

          if (code === 200 && guessData && guessData.length > 0) {
            this.guessKeys = guessData.map((item) => item.name || item.keyword || item.title)
            console.log("猜你喜欢加载成功:", this.guessKeys)
          } else {
            console.log("猜你喜欢接口返回空数据，使用默认数据:", msg)
            // 保持原有的 mock 数据
          }
        } catch (error) {
          console.log("获取猜你喜欢失败，使用默认数据:", error)
          // 保持原有的 mock 数据
        }
      },

      // 获取热搜榜
      async _getHotSearchList() {
        try {
          const result = await getHotSearchList({ limit: 6 })
          const { code, msg, data, rows } = result || {}
          const hotData = data || rows

          if (code === 200 && hotData && hotData.length > 0) {
            this.hotList = hotData.map((item, index) => ({
              productId: item.id || item.productId || `hot-${index}`,
              title: item.name || item.title || `热搜商品${index + 1}`,
              description: item.description || item.subtitle || "热门商品",
              ranking: index + 1,
              coverImage: item.image || item.coverImage || "https://tc.666150.xyz/i/2025/07/02/irbggu.png"
            }))
            console.log("热搜榜加载成功:", this.hotList)
          } else {
            console.log("热搜榜接口返回空数据，使用默认数据:", msg)
            // 保持原有的 mock 数据
          }
        } catch (error) {
          console.log("获取热搜榜失败，使用默认数据:", error)
          // 保持原有的 mock 数据
        }
      },

      onChange() {
        console.log("搜索内容:", this.keyword)
        // 这里可以添加搜索逻辑
      },

      async onBlur() {
        console.log("搜索框失去焦点")
        // 这里可以添加失去焦点后的逻辑
        if (`${this.keyword}`.trim() === "") {
          this.resultList = []
        }

        const _result = await this._searchProducts({ searchValue: this.keyword})
        console.log("onSearch结果:", _result)
        this.resultList = _result || []
        if (this.resultList.length > 0) {
          // 如果有搜索结果，记录历史搜索
          if (!this.historyKeys.includes(this.keyword)) {
            this.historyKeys.unshift(this.keyword)
          }
        } else {
          console.warn("没有找到相关商品")
        }
      },

      onClear() {
        console.log("清除搜索内容")
        this.keyword = ""
        this.resultList = []
      },

      // 搜索商品
      async _searchProducts(obj = {}) {
        if (!obj.searchValue) {
          return []
        }
				let searchObj = {
					searchValue: obj.searchValue,
					sortField: obj.sortField,
					page: 1,
					size: 40
				}
        try {
          const result = await searchProducts(searchObj)
          const { code, msg, data, rows } = result || {}
          const searchData = rows

          if (code === 200 && searchData && searchData.length > 0) {
            // 成功获取搜索结果，转换数据格式
            const searchResults = searchData.map((item) => ({
							name: item.product_name,
							image: item.image,
							insidePrice: item.insidePrice,
							externalPrice: item.externalPrice,
							description: item.description
            }))
            console.log("搜索结果加载成功:", searchResults.length, "个商品")
            return searchResults
          } else {
            console.log("搜索接口返回空数据，使用模拟数据:", msg)
            // 返回模拟数据
            return this._getMockSearchResults(keyword)
          }
        } catch (error) {
          console.log("搜索商品失败，使用模拟数据:", error)
          // 返回模拟数据
          return this._getMockSearchResults(keyword)
        }
      },

      // 获取模拟搜索结果
      _getMockSearchResults(keyword) {
        const randomCount = Math.floor(Math.random() * (30 - 10 + 1)) + 10
        return Array.from({ length: randomCount }, (_, index) => {
          const baseIndex = index % MOCK_LIST.length
          return {
            ...MOCK_LIST[baseIndex],
            productId: `${MOCK_LIST[baseIndex].productId}-${index}`,
            title: `${keyword} ${MOCK_LIST[baseIndex].title} ${index + 1}`,
            price: MOCK_LIST[baseIndex].price - index * 10
          }
        })
      },

      toggleListWay() {
        this.showListWay = this.showListWay === "list" ? "grid" : "list"
        console.log("切换显示方式为:", this.showListWay)
      },

      onHotItemClick(item) {
        console.log("点击热搜项:", item)
        // 这里可以添加点击热搜项后的逻辑，比如跳转到搜索结果页面
      },

      onSelectAction(type) {
        console.log("选择排序方式:", type)
        this.sortInfo.type = type
        // 这里可以添加排序逻辑，比如根据sortType重新排序resultList
        if (type === "default") {
          this.resultList.sort((a, b) => a.price - b.price) // 按价格升序
        } else if (type === "lasted") {
          this.resultList.sort((a, b) => new Date(b.productId) - new Date(a.productId)) // 按时间降序
        }
      },

      onPriceSort({ value }) {
        console.log("价格排序", value)
        this.sortInfo.price = value
        // 这里可以添加价格排序的逻辑
        this.resultList.sort((a, b) => a.price - b.price) // 按价格升序
      },

      handleClose() {
        this.showSlider = false
        console.log("侧边栏已关闭")

        this.$nextTick(() => {
          this.getFixedBarHeight() // 更新搜索栏高度
        })
      },

      handleChangeMin({ value }) {
        console.log("价格区间变化:", value)
        // 这里可以添加价格区间变化的逻辑
        this.sortInfo.minPrice = value
      },

      handleChangeMax({ value }) {
        console.log("价格区间变化:", value)
        // 这里可以添加价格区间变化的逻辑
        this.sortInfo.maxPrice = value
      },

      handleReset() {
        console.log("重置筛选条件")
        this.sortInfo = {
          price: 0,
          type: "default",
          preAllBrands: [],
          brand: "全部",
          minPrice: 0,
          maxPrice: 10000
        }
        this.showSlider = false
        // 这里可以添加重置筛选条件后的逻辑，比如重新获取商品列表
        this.resultList = MOCK_LIST // 重置为初始的Mock数据
        console.log("筛选条件已重置")
        if (this.resultList.length === 0) {
          uni.showToast({
            title: "没有符合条件的商品",
            icon: "none"
          })
        }
        this.$nextTick(() => {
          this.getFixedBarHeight() // 更新搜索栏高度
        })
      },

      handleConfirm() {
        console.log("确认筛选条件")
        this.showSlider = false
        // 这里可以添加确认筛选条件后的逻辑，比如重新获取商品列表
        this.resultList = this.resultList.filter((item) => {
          return (
            item.price >= this.sortInfo.minPrice &&
            item.price <= this.sortInfo.maxPrice &&
            (this.sortInfo.brand === "全部" || item.title.includes(this.sortInfo.brand))
          )
        })
        console.log("筛选后的商品列表:", this.resultList)
        if (this.resultList.length === 0) {
          uni.showToast({
            title: "没有符合条件的商品",
            icon: "none"
          })
        }
        this.$nextTick(() => {
          this.getFixedBarHeight() // 更新搜索栏高度
        })
      },

      onChangeGuess() {
        console.log("换一换猜你喜欢")
        // 重新获取猜你喜欢的数据
        this._getGuessYouLike()
      },

      onClearHistory() {
        console.log("清除历史搜索")
        if (!this.historyKeys.length) {
          uni.showToast({
            title: "没有历史搜索记录",
            icon: "none"
          })
          return
        }
        this.historyKeys = []
      },

      onHistoryItemClick(item) {
        console.log("点击历史搜索项:", item)
        this.keyword = item
        // 触发搜索逻辑
        this.onBlur()
      },

      onShowDetail(item) {
        console.log("查看商品详情:", item)
        // 这里可以添加跳转到商品详情页的逻辑
        uni.navigateTo({
          url: `/packages/shop/info?id=${item.productId}`
        })
      },

      getFixedBarHeight() {
        // 获取搜索栏高度
        const fixedBar = uni.createSelectorQuery().select("#search-fixed-bar")
        fixedBar
          .boundingClientRect((rect) => {
            if (rect) {
              this.fixedBarHeight = rect.height
              console.log("fixedBarHeight", this.fixedBarHeight)
            } else {
              console.warn("无法获取搜索栏高度")
            }
          })
          .exec()
      }
    }
  }
</script>

<style lang="scss">
	.pageBrandName {
		max-width: 120rpx;
		display: inline-block;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
  .fixed-bar {
    width: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
    background: #ffffff;

    .search-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 80rpx;
      width: calc(100% - 60rpx) !important;
      min-width: 0;
      margin: 0;
      margin-left: 30rpx !important;
      padding: 20rpx 0 !important;
      box-sizing: border-box;
      background-color: transparent;

      .search-right-block {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        margin-left: 10rpx;
        background-color: #fff;
      }
    }

    .select-bar {
      display: flex;
      align-items: center;
      height: 80rpx;
      padding: 0 30rpx;
      justify-content: space-between;

      .switch-tips {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        font-size: 32rpx;
        color: #666;
      }

      .switch-tips-active {
        color: #e54d42;
        font-weight: 500;
      }

      .brand-block {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60rpx;
        padding: 0 20rpx;
        background-color: #f4f4f4;
        border-radius: 60rpx;
        font-size: 28rpx;
        color: #666;
      }

      .filter-bar {
        display: flex;
        align-items: center;
        height: 60rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #666;

        text {
          margin-right: 4rpx;
        }
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    margin-top: 20rpx;

    .idle-block {
      display: flex;
      flex-direction: column;
      background: #ffffff;

      .title-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 30rpx;
        width: 100%;
        box-sizing: border-box;

        .left-title {
          font-size: 32rpx;
          color: #333;
          font-weight: bold;
        }

        .right-bar {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          flex: 1;
          height: 60rpx;
          font-size: 28rpx;
          color: #999;

          text {
            margin-right: 10rpx;
          }
        }
      }

      .wrap-history {
        display: flex;
        flex-wrap: wrap;
        padding: 0 30rpx 20rpx 30rpx;
        box-sizing: border-box;

        .history-item {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 60rpx;
          padding: 0 20rpx;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          background-color: #f4f4f4;
          border-radius: 100rpx;
          font-size: 28rpx;
          color: #666;
          cursor: pointer;

          &:hover {
            background-color: #e0e0e0;
          }
        }
      }

      .hot-title-bar {
        display: flex;
        align-items: center;
        padding: 20rpx 30rpx;
        width: 100%;
        box-sizing: border-box;

        .icon {
          width: 48rpx;
          height: 48rpx;
          margin-right: 10rpx;
        }

        .left-title {
          font-size: 32rpx;
          color: #333;
          font-weight: bold;
        }
      }

      .hot-list {
        display: flex;
        flex-direction: column;
        padding: 0 30rpx 20rpx 30rpx;

        .hot-item-container {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;

          .hot-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 20rpx;
            background-color: #f9f9f9;
            border-radius: 8rpx;
            position: relative;

            &__image {
              width: 120rpx;
              height: 120rpx;
              border-radius: 8rpx;
              margin-right: 20rpx;
            }

            &__ranking-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 60rpx;
              height: 50rpx;
              display: flex;
              transform: rotate(-30deg) translateY(-16rpx) translateX(-5rpx);

              .ranking-icon {
                width: 60rpx;
                height: 50rpx;
              }
            }

            &__ranking {
              position: absolute;
              top: 4rpx;
              left: 4rpx;
              width: 36rpx;
              height: 36rpx;
              background-color: transparent; /* 使用主题色 */
              border-radius: 50%;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 24rpx;
              transform: rotate(-30deg);
            }

            &__info {
              flex: 1;

              .title {
                font-size: 28rpx;
                color: #333;
                font-weight: bold;
                margin-bottom: 10rpx;
              }

              .description {
                font-size: 24rpx;
                color: #666;
              }
            }
          }
        }
      }
    }

    .content-block {
      display: flex;
      flex-direction: column;
      padding: 20rpx 30rpx;
      box-sizing: border-box;

      .product-list {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        &.grid-view {
          justify-content: space-between;
        }

        .product-item-wrapper {
          width: calc(50% - 20rpx);
          box-sizing: border-box;
        }
      }
    }
  }

  .slider-popup {
    width: 80%;
    max-width: 600rpx;
    height: 100%;

    .slide-content {
      display: flex;
      flex-direction: column;
      height: 100%;
      background-color: #ececec;
      border-top-left-radius: 30rpx;
      border-bottom-left-radius: 30rpx;

      .slide-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 30rpx;

        .header-title {
          font-size: 32rpx;
          color: #333;
          font-weight: bold;
        }
      }

      .slide-body {
        display: flex;
        flex-direction: column;
        flex: 1;

        .select-box {
          display: flex;
          flex-direction: column;
          margin-top: 20rpx;
          padding: 20rpx 30rpx;
          background: #ffffff;

          .title {
            font-size: 32rpx;
            color: #333333;
            font-weight: bold;
          }

          .price-range-block {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 20rpx;
          }

          .brand-list {
            display: flex;
            flex-wrap: wrap;
            margin-top: 20rpx;

            .brand-item {
              padding: 10rpx 30rpx;
              margin: 10rpx 5rpx;
              background-color: #f4f4f4;
              border-radius: 50rpx;
              font-size: 28rpx;
              color: #666;

              &.active {
                background-color: #e54d42;
                color: #fff;
              }
            }
          }
        }
      }

      .slide-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #ffffff;
        padding: 20rpx 30rpx env(safe-area-inset-bottom);
      }
    }
  }
</style>

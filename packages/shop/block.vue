<template>
  <my-page
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-fixed-bar="true"
    :fixed-bar-height="fixedBarHeight"
    :show-back-to-top="true"
  >
    <template #nav>
      <my-nav :title="title" backIcon />
    </template>

    <template #fixedBar>
      <view class="fixed-bar" id="block-fixed-bar">
        <search-bar />
        <!-- <wd-tabs v-if="tabs.length > 0" v-model="active" slidable="always">
          <block v-for="tab in tabs" :key="tab.id">
            <wd-tab :title="`${tab.name}`" />
          </block>
        </wd-tabs> -->
      </view>
    </template>

    <view class="content">
      <view
        v-for="(item, index) in productList"
        :key="index"
        class="product-item-wrapper"
        :style="{ width: 'calc(50% - 20rpx)' }"
      >
        <product-card :product="item" @show-detail="onShowDetail(item)" />
      </view>
    </view>
  </my-page>
</template>

<script>
	import { getNewProducts } from "@/api/product.js"
  /**
   * 商品区块api
   * /store/productCate/list
   */
  export default {
    data() {
      return {
        title: "上新产品",
        fixedBarHeight: 0, // 仅搜索栏高度
        tabs: [],
        active: "1", // 默认选中第一个tab
        productList: []
      }
    },
    created() {
      console.log("created")
      this.getFixedBarHeight()
    },

    onLoad(options = {}) {
      console.log("onLoad", options)
      // 初始化产品列表
      this.getListInfo()
      // this.getCategoryList()
    },

    watch: {
      active: function (newVal, oldVal) {
        console.log("Tab changed from", oldVal, "to", newVal)
        // 可以在这里添加切换Tab时的逻辑
      }
    },
    methods: {
      async getListInfo() {
				const result = await getNewProducts({
					pageNum: 1,
					pageSize: 30
				})
				const { code, msg, data, rows, total } = result || {}
				this.productList = rows.map((item, index) => ({
				  name: item.product_name,
				  image: item.image,
				  insidePrice: item.insidePrice,
				  externalPrice: item.externalPrice,
				  description: item.description,
					product_id: item.product_id
				}))
				
        // await new Promise((resolve) => setTimeout(resolve, 500))
        // this.productList = await this.generateList()
        // uni.showToast({
        //   title: "加载成功",
        //   icon: "success",
        //   duration: 2000
        // })
      },

      async getCategoryList() {
        // TODO 获取分类列表信息
        await new Promise((resolve) => setTimeout(resolve, 500))
        this.tabs = [
          { id: 1, name: "推荐" },
          { id: 2, name: "平板电脑" },
          { id: 3, name: "手机" },
          { id: 4, name: "笔记本" },
          { id: 5, name: "音箱" },
          { id: 6, name: "数码相机" },
          { id: 7, name: "显示器" },
          { id: 8, name: "智能穿戴" },
          { id: 9, name: "智能家居" },
          { id: 10, name: "配件" }
        ]
      },

      async generateList() {

        // 商品模板
        const products = []

        return Array(20)
          .fill(null)
          .map(() => {
            const product = products[Math.floor(Math.random() * products.length)]
            return {
              ...product,
              isChecked: false
            }
          })
      },

      onShowDetail(item) {
        uni.navigateTo({
          url: `/packages/shop/info?id=${item.product_id}`
        })
      },
      onAction(action) {
        // 处理不同的操作逻辑
        console.log(`Action: ${action}`)
      },

      getFixedBarHeight() {
        // 获取搜索栏高度
        const fixedBar = uni.createSelectorQuery().select("#block-fixed-bar")
        fixedBar
          .boundingClientRect((rect) => {
            if (rect) {
              this.fixedBarHeight = rect.height
              console.log("fixedBarHeight", this.fixedBarHeight)
            } else {
              console.warn("无法获取搜索栏高度")
            }
          })
          .exec()
      }
    }
  }
</script>

<style scoped lang="scss">
  .fixed-bar {
    display: flex;
    flex-direction: column;
    background: #ffffff;
    padding: 0 30rpx;
  }

  .content {
    padding: 20rpx 30rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .product-item-wrapper {
    margin-bottom: 20rpx;
    box-sizing: border-box;
  }
</style>

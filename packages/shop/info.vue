<template>
  <my-page
    ref="myPage"
    background="#ececec"
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :show-fixed-bar="true"
    :fixed-bar-height="fixedBarHeight"
    :show-back-to-top="true"
    :show-footer="true"
    :footer-height="80"
    @scroll="onScroll"
    :scroll-to-top="scrollTopValue"
    :has-scroll-to-top="true"
  >
    <template #nav>
      <my-nav title="商品详情" backIcon />
    </template>

    <template #fixedBar>
      <view class="fixed-bar" id="info-fixed-bar">
        <wd-tabs v-if="tabs.length > 0" v-model="active" slidable="always" @change="onTabClick">
          <block v-for="tab in tabs" :key="tab.id">
            <wd-tab :title="`${tab.name}`" />
          </block>
        </wd-tabs>
      </view>
    </template>

    <view class="content" id="product-info-content">
      <wd-swiper
        :list="swiperList"
        autoplay
        :interval="8000"
        height="540rpx"
        v-model:current="currentSwiper"
        :indicator="{ type: 'fraction' }"
        indicatorPosition="bottom-right"
        customClass="my-swiper"
        @click="handleSwiperClick"
        @change="onSwiperChange"
      ></wd-swiper>

      <view class="price-block">
        <view class="price-block__title_bar">
          <view class="price-title">
            <text class="price">内购价¥ {{ priceInfo.price }}</text>
            <text class="origin-price">市场价¥ {{ priceInfo.originalPrice }}</text>
          </view>
          <view class="action-bar" @tap="onShare('price')">
            <image src="/static/shop/ic-share.png" mode="aspectFill" class="action-icon" />
            <view class="action-tips">
              <text>分享</text>
            </view>
          </view>
        </view>
        <view class="price-block__content">{{ priceInfo.title }}</view>
        <view class="price-block__description">
          <text>{{ priceInfo.description }}</text>
        </view>
      </view>

      <view class="select-block">
        <view class="select-block__bar">
          <view class="left-title">
            <text>规格</text>
          </view>
          <view class="content">
            <view class="content-ctrl">
              <view class="select-description-bar">
                <text>{{ spaceName }}</text>
              </view>
              <!-- <view class="select-image-bar">
                <image
                  v-for="(item, index) in productItems.previewImage"
                  :key="item"
                  class="select-image"
                  :class="index === selectedInfo.imageIndex ? ' select-image-border' : ''"
                  src="/static/logo.png"
                  mode="aspectFill"
                />
              </view> -->
            </view>
            <view class="next-icon">
              <wd-icon name="arrow-right" size="24rpx" color="#969696" />
            </view>
          </view>
        </view>
        <view class="select-block__bar">
          <view class="left-title">
            <text>赠品</text>
          </view>
          <view class="content" v-if="selectedInfo.gifts.length>0">
            <view class="content-ctrl">
              <view class="select-image-bar">
                <image class="select-image" src="/static/logo.png" mode="aspectFill" />
              </view>
            </view>
            <view class="next-icon">
              <wd-icon name="arrow-right" size="24rpx" color="#969696" />
            </view>
          </view>
					<view class="content" v-else>
						<view style="font-size: 24rpx; color: #969696;">无</view>
					</view>
        </view>
        <view class="select-block__bar">
          <view class="left-title">
            <text>服务</text>
          </view>
          <view class="content">
            <view class="content-ctrl">
              <view class="select-description-bar">
                <text>7天无理由退货</text>
                <text>15天免费换货</text>
                <text>一年质保</text>
              </view>
            </view>
            <view class="next-icon">
              <wd-icon name="arrow-right" size="24rpx" color="#969696" />
            </view>
          </view>
        </view>
      </view>

      <view class="product-info-description" id="product-info-description">
        <view class="title-h2">商品详情</view>
        <!-- <view class="title-h3">规格参数</view>
        <view class="title-h3">主体</view> -->
				<view class="description-item" v-for="(value, key) in spaceObj" :key="key">
					<text class="description-item__name">{{ key }}:</text>
					<text class="description-item__value">{{ value || '无' }}</text>
				</view>
        <!-- <view class="description-item" v-for="(value, key) in productDescription" :key="key">
          <text class="description-item__name">{{ descriptionKeyName[key] || key }}:</text>
          <text class="description-item__value" v-if="key !== 'features'">{{ value }}</text>
          <view class="description-item__value" v-else>
            <text v-for="(feature, index) in value" :key="index"
              >{{ feature }}<text v-if="index < value.length - 1">、</text>
            </text>
          </view>
        </view> -->
      </view>
			
			<view class="product-info-description" style="margin-top: 20rpx;" v-html="productInfo"></view>

      <view class="product-info-images">
        <wd-img
          v-for="(image, index) in productDescImages"
          :key="index"
          :src="image"
          mode="widthFix"
          class="desc-img"
        />
      </view>
    </view>

    <template #footer>
      <view class="footer-bar">
        <view class="footer-bar__total-info">
          <button v-for="item in footerItems" :key="item.name" class="footer-item" @tap="onAction(item.action)">
            <wd-icon :name="item.icon" size="40rpx" color="#666666" />
            <text>{{ item.name }}</text>
          </button>
        </view>
        <view class="footer-bar__action">
          <view class="btn-cancel" @tap="onAction('addCart')">加入购物车</view>

          <view class="btn-pay" @tap="onAction('pay')">立即购买</view>
        </view>
      </view>
    </template>
  </my-page>
</template>

<script>
  const DESCRIPTION_KEY_NAME = {
    name: "商品名称",
    brand: "品牌",
    series: "系列",
    model: "型号",
    os: "操作系统",
    features: "特色功能"
  }

  /**
   * 商品详情页面api
   * /store/product/getInfo
   */
	import { getProductInfo } from "@/api/product.js"
	import { addToCart } from "@/api/cart.js"
	
  export default {
    data() {
      return {
				_productId: '',
        active: 0,
				spaceName: '',
				spaceObj: {},
        tabs: [
          {
            id: 1,
            name: "商品"
          },
          {
            id: 2,
            name: "详情"
          }
        ],
        swiperList: [],
        currentSwiper: 0,
        fixedBarHeight: 0,
        productInfoDescTop: 0,

        priceInfo: {
          price: 0,
          originalPrice: 0,
          title: "",
          description: ""
        },

        selectedInfo: {
          color: "曜金黑",
          version: "12GB+512GB",
          quantity: 1,
          coverImgUrl: "https://modao.cc/uploads6/images/13585/135850963/v2_sii7c6.png",
          imageIndex: 0,
          // 赠品
          gifts: [
            // {
            //   name: "HUAWEI MatePad Pro保护壳",
            //   image: "https://modao.cc/uploads6/images/13584/135843321/v2_sii4c9.png",
            //   quantity: 1
            // }
          ]
        },

        productItems: {
          version: ["12GB+512GB", "8GB+256GB", "8GB+128GB"],
          color: ["曜金黑", "晨曦银", "皓月白"],
          previewImage: [
            "https://modao.cc/uploads6/images/13585/135850963/v2_sii7c6.png",
            "https://modao.cc/uploads6/images/13585/135850963/v2_sii7c6.png",
            "https://modao.cc/uploads6/images/13585/135850963/v2_sii7c6.png"
          ]
        },

        descriptionKeyName: DESCRIPTION_KEY_NAME,
        productDescription: {
          name: "",
          brand: "",
          series: "",
          model: "",
          os: "",
          features: []
        },
				productInfo: '',
        productDescImages: [],

        footerItems: [
          {
            name: "首页",
            icon: "home",
            action: "home"
          },
          {
            name: "购物车",
            icon: "cart",
            action: "cart"
          },
          {
            name: "客服",
            icon: "service",
            action: "service"
          }
        ],

        isTabClick: false,
				productData: null,
				allData: null,
        scrollThrottleTimer: null,
        scrollTopValue: 0
      }
    },
    watch: {
      active(newVal, oldVal) {
        // console.log("active changed from", oldVal, "to", newVal)
      },
      scrollTopValue(newVal) {
        // console.log("scrollTopValue changed to", newVal)
      }
    },
    created() {
      console.log("created")
    },
    onReady() {
      this.getLayoutInfo()
    },
		onLoad(options) {
			console.log(options.id)
			this._productId = options.id
			this._getProductInfo()
		},

    methods: {
			async _getProductInfo() {
				const result = await getProductInfo({productId: this._productId})
				const res = result.data
				if(result.code === 200) {
					this.productData = res
					this.swiperList = res.sliderImage.split(',')
					this.priceInfo = {
					  price: res.attrValues[0].insidePrice,
					  originalPrice: res.attrValues[0].externalPrice,
					  title: res.productName,
					  description: res.description
					}
					this.productDescription = {
					  name: res.productName,
					  brand: res.brandName,
					  series: "无",
					  model: "无",
					  features: []
					}
					this.productInfo = res.productInfo
					let ruleObj = JSON.parse(res.attrValues[0].paramsValue)
					this.spaceObj = ruleObj || {}
					this.spaceName = ruleObj['规格'] || '无'
					this.allData = res
				}
			},
      handleSwiperClick(e) {
				
      },
      onSwiperChange(e) {
        // console.log(e)
      },
      onShare(type) {
        console.log("分享类型:", type)
        uni.showToast({
          title: `分享 ${type} 成功`,
          icon: "success",
          duration: 2000
        })
      },
      async onAction(type = "") {
        console.log("onAction type >>", type)
        if (type === "pay") {
          // uni.showToast({
          //   title: "支付成功",
          //   icon: "success"
          // })
					let arr = [{
						accountUnit: this.allData.accountUnit,
						items: [this.allData]
					}]
					uni.setStorageSync('startPayData', JSON.stringify(arr))
					uni.navigateTo({
						url: '/packages/order/waitPay'
					})
        } else if (type === "addCart") {
					console.log(this.productData)
          
					const submitObj = {
						productId: this.productData.productId,
						productAttrValueId: this.productData.attrValues[0].productAttrValueId,
						num: 1
					}
					const res = await addToCart(submitObj)
					uni.showToast({
					  title: res.msg,
					  icon: "success"
					})
        } else if (type === "home") {
          uni.switchTab({
            url: "/pages/home/<USER>"
          })
        } else if (type === "cart") {
          uni.switchTab({
            url: "/pages/shoppingCart/index"
          })
        } else if (type === "service") {
          return uni.showModal({
            title: "联系客服",
            content: "客服电话：010-********/********",
            showCancel: false,
            confirmText: "关闭"
          })
        }
      },

      getLayoutInfo() {
        const query = uni.createSelectorQuery().in(this)
        query.select("#info-fixed-bar").boundingClientRect()
        query.select("#product-info-description").boundingClientRect()
        query.exec((res) => {
          if (res[0]) {
            this.fixedBarHeight = res[0].height
          } else {
            console.warn("无法获取 fixed bar 高度")
          }
          if (res[1]) {
            this.productInfoDescTop = res[1].top
          } else {
            console.warn("无法获取商品详情区域位置")
          }
        })
      },

      onTabClick({ name, index }) {
        console.log("onTabClick index >>", index, name)
        this.isTabClick = true
        this.active = index

        // 根据tab索引获取目标元素
        const targetId = index === 1 ? "#product-info-description" : "#product-info-content"
        this.$nextTick(() => {
          this.scrollToElement(targetId)
        })
      },

      // 新增滚动到指定元素的方法
      scrollToElement(selector) {
        console.log("scrollToElement selector >>", selector)
        const query = uni.createSelectorQuery().in(this)
        query.select(selector).boundingClientRect()
        query.selectViewport().scrollOffset()
        query.exec((res) => {
          if (res[0] && res[1]) {
            this.scrollTopValue = res[0].top + res[1].scrollTop
            this.isTabClick = false
          }
        })
      },
      onScroll(e) {
        // 只有当不是从tab点击触发的滚动时才更新active
        if (this.isTabClick) return

        // 节流处理
        if (this.scrollThrottleTimer) return
        this.scrollThrottleTimer = setTimeout(() => {
          this.scrollThrottleTimer = null

          // 获取商品详情区域的位置
          const query = uni.createSelectorQuery().in(this)
          query.select("#product-info-description").boundingClientRect()
          query.selectViewport().scrollOffset()
          query.exec((res) => {
            if (res[0]) {
              // 考虑fixedBar的高度，计算何时应该切换tab
              const detailSectionTop = res[0].top
              const fixedBarHeightPx = uni.upx2px(this.fixedBarHeight)

              // 当商品详情区域滚动到fixedBar下方时切换到详情tab
              if (detailSectionTop <= fixedBarHeightPx + 10) {
                // 加10px的缓冲
                if (this.active !== 1) {
                  this.active = 1
                }
              } else {
                if (this.active !== 0) {
                  this.active = 0
                }
              }
            }
          })
        }, 200) // 100ms 节流
      }
    }
  }
</script>

<style lang="scss" scoped>
  .content {
    display: flex;
    flex-direction: column;

    :deep(.wd-swiper__track) {
      border-radius: 0 !important;
    }

    .price-block {
      display: flex;
      flex-direction: column;
      background: #ffffff;
      margin: 30rpx 0;

      &__title_bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;
        height: 80rpx;

        .price-title {
          flex: 1;
          display: flex;
          align-items: flex-start;

          .price {
            margin-right: 20rpx;
            font-size: 36rpx;
            color: #D81E07;
						font-weight: bold;
          }

          .origin-price {
            text-decoration: line-through;
            color: #999999;
            font-size: 26rpx;
            margin-left: 10rpx;
						padding-top: 5rpx;
          }
        }

        .action-bar {
          display: flex;
          flex-direction: column;
          align-items: center;

          .action-icon {
            width: 34rpx;
            height: 34rpx;
          }

          .action-tips {
            margin-top: 4rpx;
            font-size: 20rpx;
            color: #969696;
          }
        }
      }

      &__content {
        display: flex;
        font-size: 30rpx;
        color: #333333;
        padding: 10rpx 30rpx;
        font-weight: bold;
      }

      &__description {
        font-size: 24rpx;
        color: #969696;
        padding: 10rpx 30rpx;
        line-height: 1.5;
      }
    }

    .select-block {
      display: flex;
      flex-direction: column;
      background: #ffffff;
      margin-bottom: 30rpx;
      padding: 20rpx 0;

      &__bar {
        display: flex;
        justify-content: space-between;
        padding: 0 30rpx;

        .left-title {
          font-size: 24rpx;
          color: #969696;
        }

        .content {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          flex: 1;
          padding-left: 20rpx;

          .content-ctrl {
            display: flex;
            flex-direction: column;
            flex: 1;

            .select-description-bar {
              display: flex;
              flex-direction: row;
              align-items: center;
              font-size: 24rpx;
              color: #333333;

              text {
                margin-right: 16rpx;
              }
            }

            .select-image-bar {
              display: flex;
              flex-direction: row;
              align-items: center;
              margin-top: 10rpx;

              .select-image {
                width: 60rpx;
                height: 60rpx;
                border-radius: 6rpx;
                margin-right: 10rpx;
              }

              .select-image-border {
                border: 1px solid #ff3d00;
                box-sizing: border-box;
              }
            }
          }

          .next-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40rpx;
            height: 40rpx;
          }
        }
      }

      &__bar:not(:first-child) {
        margin-top: 20rpx;
      }
    }

    .product-info-description {
      display: flex;
      flex-direction: column;
      background: #ffffff;
      padding: 30rpx;

      .title-h2 {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
      }

      .title-h3 {
        font-size: 30rpx;
        color: #333333;
        font-weight: bold;
        margin-top: 10rpx;
      }

      .description-item {
        display: flex;
        flex-direction: row;
        margin-top: 10rpx;

        &__name {
          font-size: 28rpx;
          color: #666666;
          width: 140rpx; /* 固定宽度 */
        }

        &__value {
          font-size: 28rpx;
          color: #333333;
          flex: 1; /* 占据剩余空间 */
        }
      }
    }

    .product-info-images {
      display: flex;
      flex-direction: column;

      .desc-img {
        width: 100%;
        height: auto;
      }
    }
  }

  .footer-bar {
    min-height: 80rpx;
    background-color: #ffffff;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx env(safe-area-inset-bottom);

    &__total-info {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 28rpx;
      font-weight: bold;
      color: #333333;

      .footer-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 0; // 重置默认 margin
        margin-right: 30rpx;
        padding: 0; // 重置默认 padding
        background-color: transparent; // 使背景透明
        line-height: normal; // 重置行高
        font-size: 24rpx;

        // 在 uni-app 中，按钮的边框是通过 ::after 伪元素实现的，需要将其重置
        &::after {
          border: none;
        }

        text {
          font-size: 24rpx;
          margin-top: 4rpx;
          color: #666666;
        }

        &:last-child {
          margin-right: 0; /* 最后一个不需要右边距 */
        }
      }
    }

    &__action {
      display: flex;
      flex: 1;
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;

      .btn-cancel {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10rpx 25rpx;
        background-color: #f8f8f8;
        border-top-left-radius: 30rpx;
        border-bottom-left-radius: 30rpx;
        border: 1px solid #C6A670;
        font-size: 28rpx;
        color: #C6A670;
      }

      .btn-pay {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12rpx 35rpx;
        background-color: #C6A670;
        border-top-right-radius: 30rpx;
        border-bottom-right-radius: 30rpx;
        color: #ffffff;
        font-size: 28rpx;
      }
    }
  }
</style>

<template>
  <my-page
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :autoHeight="false"
    :has-tab-bar="true"
    :show-background="true"
    :back-ground-style="bgStyle"
    :show-back-to-top="true"
    :show-fixed-header="true"
    :fixed-header-height="headerBlockHeight"
    @refresh="onRefresh"
    @restore="onRestore"
  >
    <!-- 导航栏 -->
    <template #nav>
      <my-nav :backIcon="false" :bgColor="navGradientStyle" />
    </template>

    <!-- 固定头部区域 -->
    <template #fixedHeader>
      <view class="header-block" id="header-block">
        <search-bar />
        <view class="header-title">
          <text>欢迎来到，伟仕佳杰内购商城！</text>
          <view>
            <image src="/pages/home/<USER>/logo.png" mode="widthFix"></image>
          </view>
        </view>
      </view>
    </template>

    <!-- 滚动内容区域 -->
    <view class="content">
      <view class="home-goods-view">
        <view class="home-top-new">
          <image src="/pages/home/<USER>/new.png" mode="widthFix"></image>
        </view>
        <view class="home-goods-title-view" @tap="goMoreNew">
          <text>更多</text>
          <wd-icon name="arrow-right" size="40rpx" />
        </view>
        <view class="home-goods-list-view">
          <goods-item :data="goodsList"></goods-item>
        </view>
      </view>

      <view class="home-type-goods-view">
        <scroll-view class="htg-scroll-view" scroll-x="true">
          <view
            v-for="(item, index) in goodsTypeList"
            @tap="changeGoodsType(item)"
            :class="`htg-item-view ${item.checked ? 'htg-checked' : 'htg-nocheck'}`"
            :key="index"
            >{{ item.name }}
          </view>
        </scroll-view>
        <view class="htg-title-border"></view>
        <view class="home-goods-list-view">
          <goods-item :data="secondGoodsList"></goods-item>
        </view>
      </view>

      <view class="home-bottom-share">
        <view class="c-title">商城福利</view>
        <view class="card">
          <view class="header">
            <image class="star" src="/pages/home/<USER>/fav.png" mode="widthFix"></image>
            <text class="title">获得在线支持，优惠福利抢先得。</text>
          </view>
          <view class="content">
            <img class="qr-code" src="/static/logo.png" alt="二维码" />
          </view>
        </view>
      </view>
    </view>
  </my-page>
  <my-tabbar tabName="home" />
</template>

<script>
  import goodsItem from "@/components/goods-item/goods-item.vue"
  import { getNewProducts, getHotProducts } from "@/api/product.js"

  /**
   * 首页api
   * /store/product/appList
   */
  export default {
    components: {
      goodsItem
    },
    data() {
      return {
        // Navigation gradient style for seamless transition
        navGradientStyle: "rgb(194, 152, 92)",

        // Background style for my-page component
        bgStyle: {
          height: "560rpx",
          background: "rgb(194, 152, 92)",
          borderRadius: "0 0 30rpx 30rpx"
        },

        // Header block height for fixed header
        headerBlockHeight: 130,

        // Component state
        isFirstShow: false,
        total: 0,
        searchValue: "",
        goodsTypeList: [
          {
            name: "热卖",
            id: "",
            checked: true
          },
          {
            name: "笔记本",
            id: "",
            checked: false
          },
          {
            name: "手机",
            id: "",
            checked: false
          },
          {
            name: "平板",
            id: "",
            checked: false
          },
          {
            name: "智能设备",
            id: "",
            checked: false
          }
        ],
        goodsList: [],
        secondGoodsList: []
      }
    },

    onLoad() {
      this.isFirstShow = true
    },

    async mounted() {
      await this.$nextTick()
      this._getFixHeaderHeight()
    },

    onShow() {
      if (this.isFirstShow) {
        this._getNewProducts()
        this._getSecondProducts(this.goodsTypeList[0].name)
        this.isFirstShow = false
      }
    },

    methods: {
      goMoreNew() {
        uni.navigateTo({
          url: "/packages/shop/block"
        })
      },
      async _getSecondProducts(val) {
        try {
          const result = await getHotProducts({
            searchValue: val,
            pageNum: 1,
            pageSize: 12
          })
          const { code, msg, data, rows, total } = result || {}
          const productData = data || rows

          if (code === 200 && productData) {
            this.secondGoodsList = productData
          } else {
            console.log("新品接口返回空数据，使用默认数据:", msg)
            // 保持原有的 mock 数据
          }
        } catch (error) {
          console.log("获取新品列表失败，使用默认数据:", error)
          // 保持原有的 mock 数据
          this.secondGoodsList = []
        }
      },

      async _getNewProducts() {
        try {
          const result = await getNewProducts({
            pageNum: 1,
            pageSize: 4
          })
          const { code, msg, data, rows, total } = result || {}
          const productData = data || rows

          if (code === 200 && productData) {
            this.goodsList = productData
            this.total = total || productData.length
          } else {
            console.log("新品接口返回空数据，使用默认数据:", msg)
            // 保持原有的 mock 数据
          }
        } catch (error) {
          console.log("获取新品列表失败，使用默认数据:", error)
          // 保持原有的 mock 数据
        }
      },

      changeGoodsType(val) {
        this.goodsTypeList.forEach((item) => {
          if (item.name === val.name) {
            item.checked = true
            this._getSecondProducts(val.name)
          } else {
            item.checked = false
          }
        })
      },

      // Refresh functionality
      onRefresh() {
        this._getNewProducts()
        this.goodsTypeList.forEach((item) => {
          item.checked = false
        })
        this.goodsTypeList[0].checked = true
        this._getSecondProducts(this.goodsTypeList[0].name)
      },

      onRestore() {
        // Handle refresh restore if needed
      },

      _getFixHeaderHeight() {
        uni
          .createSelectorQuery()
          .select("#header-block")
          .boundingClientRect((rect) => {
            if (rect) {
              this.headerBlockHeight = rect.height
              console.log("headerBlockHeight", this.headerBlockHeight)
            } else {
              console.warn("无法获取搜索栏高度")
            }
          })
          .exec()
      }
    }
  }
</script>

<style lang="scss" scoped>
  // 固定头部区域样式
  .header-block {
    background: rgb(194, 152, 92);
    border-bottom-left-radius: 30rpx;
    border-bottom-right-radius: 30rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(194, 152, 92, 0.3);
  }

  .header-title {
    display: flex;
    align-items: center;
    margin: 30rpx 0;
    padding: 0 30rpx;

    > text {
      font-size: 32rpx;
      flex: 1;
      color: #333;
      font-weight: 500;
    }

    > view {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      overflow: hidden;
      background: white;
      display: flex;
      justify-content: center;
      align-items: center;
      > image {
        width: 50rpx;
        height: 50rpx;
      }
    }
  }

  // 内容区域
  .content {
    display: flex;
    flex-direction: column;
    z-index: 10;

    .home-goods-view {
      margin: 20rpx 20rpx 0;
      background: #f4f6f9;
      border-radius: 20rpx;
      padding: 40rpx 30rpx 20rpx;
      position: relative;
      padding-top: 30rpx;
      margin-top: 0;

      .home-top-new {
        position: absolute;
        width: 100rpx;
        height: 100rpx;
        top: 15rpx;
        left: 10rpx;
        image {
          width: 100%;
          height: 100%;
        }
      }

      .home-goods-title-view {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 10rpx 0;
        padding-top: 0;

        > text {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
  }

  .home-type-goods-view {
    background: white;
    border-radius: 20rpx;
    margin: 30rpx 20rpx 0;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    padding: 0 30rpx 30rpx;

    .htg-scroll-view {
      white-space: nowrap;
      overflow-x: auto;
      display: flex;
    }

    .htg-title-border {
      height: 1rpx;
      border-bottom: 1rpx solid #ccc;
      margin-top: 20rpx;
    }

    .htg-item-view {
      margin-top: 20rpx;
      display: inline-block;
      height: 70rpx;
      line-height: 70rpx;
      padding: 0 40rpx;
      margin-right: 20rpx;
      border: 2rpx solid #ccc;
      border-radius: 40rpx;
    }

    .htg-checked {
      background: #c6a670 100%;
      color: white;
    }

    .htg-nocheck {
      background: white;
      color: black;
    }
  }

  .home-bottom-share {
    background: #363636 100%;
    margin: 30rpx 30rpx 0;
    padding: 50rpx;
    border-radius: 20rpx;

    .c-title {
      color: #b28d57;
      font-size: 40rpx;
    }

    .card {
      border-radius: 20rpx;
      background-color: #f5e6a1;
      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2);
      padding: 20px;
      font-family: Arial, sans-serif;
      margin-top: 30rpx;

      .header {
        display: flex;
        align-items: center;
        justify-content: center;

        .star {
          width: 50rpx;
          height: 50rpx;
          margin: 0 20rpx;
        }

        .title {
          font-size: 28rpx;
          flex: 1;
        }
      }

      .content {
        text-align: center;
        margin-top: 30rpx;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .qr-code {
          width: 230rpx;
          height: 230rpx;
        }
      }
    }
  }
</style>

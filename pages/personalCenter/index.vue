<template>
  <my-page
    :refresher-config="{
      enabled: true
    }"
    :load-more-config="{
      enabled: false
    }"
    :has-tab-bar="true"
    :show-background="true"
    :back-ground-style="bgStyle"
  >
    <template #nav>
      <my-nav title="个人中心" :bgColor="bgStyle.background" />
    </template>

    <view class="content">
      <view class="phb-login-view">
        <view class="header-block">
          <view class="avatar">
            <image v-if="!hasLogin" src="../../static/default-avatar.jpg" mode="aspectFill" />
            <image v-else src="../../static/logo.png" mode="aspectFill" />
          </view>
          <button v-if="!hasLogin" class="header-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
            <view class="header-name">请登录</view>
          </button>
          <view v-else class="header-content">
            <view class="content-bar">
              <view class="name-tips">{{ phoneNumber }}</view>
              <view class="user-role-block">
                <image src="/pages/personalCenter/image/vip.png" mode="aspectFill" />
                <text>{{ isInternalUser ? '内部' : '外部' }}用户</text>
              </view>
            </view>
            <view class="login-out" @tap="onLoginOut"> 退出</view>
          </view>
        </view>

        <view class="header-points" v-if="hasLogin">
          <view class="points-item">
            <image class="icon" src="/pages/personalCenter/image/points.png" mode="aspectFill" />
            <view class="text-tips">{{ integral }}积分</view>
          </view>
        </view>
      </view>

      <my-grid
        title="我的订单"
        moreTips="全部订单"
        :maxColumn="5"
        gridType="order"
        :gridList="orderList"
        @onShowMore="onOrderMore"
        @onItemClick="onOrderItemClick"
      />

      <my-grid title="活动" gridType="activity" :gridList="activityList" @onItemClick="onActivityItemClick" />

      <my-grid title="服务" gridType="service" :gridList="serviceList" @onItemClick="onServiceItemClick" />
    </view>
  </my-page>

  <my-tabbar tabName="user" />
</template>

<script>
  import { login, getPhoneNumber, getUserInfo, logout, getInfo } from "@/api/login.js"
  import { activityList, orderList, serviceList } from "@/constants/profile"
  import { auth_token } from "@/constants/storage"

  /**
   * 个人中心页面
   * 手机号授权登录
   */
  export default {
    data() {
      return {
        hasLogin: false,
        bgStyle: {
          height: "560rpx",
          background: "rgba(194, 152, 92, 1)"
        },

        phoneNumber: "",
				userName: '',
				integral: '',
				isInternalUser: '',

        orderList: orderList,
        activityList: activityList,
        serviceList: serviceList
      }
    },
    watch: {
      hasLogin(newVal) {
        if (newVal) {
          this.getProfile()
        }
      }
    },
    created() {
      console.log("created")
      this.isFirstShow = true // 用于控制首次加载时获取用户信息
    },

    onLoad(options = {}) {
      this.hasLogin = uni.getStorageSync("hasLogin") || false
      if (this.hasLogin) {
        // this.getProfile()
        this.isFirstShow = false // 确保只调用一次
      }
    },

    onShow() {
      this.hasLogin = uni.getStorageSync("hasLogin") || false
      if (this.hasLogin) {
				this.phoneNumber = uni.getStorageSync("phoneNumber")
				this.userName = uni.getStorageSync("userName")
				this.integral = uni.getStorageSync("integral")
				this.isInternalUser = uni.getStorageSync("isInternalUser")
				if(this.isFirstShow) {
					this.getProfile()
					this.isFirstShow = false // 确保只调用一次
				}
      }
    },

    onReady() {
      console.log("onReady")
    },

    methods: {
      async getPhoneNumber(e) {
        console.log("getPhoneNumber", e)
        if (e.detail.errMsg !== "getPhoneNumber:ok") {
          uni.showToast({
            title: "未授权手机号，已取消登录操作",
            icon: "none",
            duration: 2000
          })
          return
        }
        console.log("phoneNumber", e.detail.encryptedData)
        try {
          const { code } = await uni.login()
          console.log("获取登录凭证code成功！", code)
          const { data, code: resCode, message } = await login({ code })
          console.log("1 login:", data)
          if (resCode !== 200) {
            uni.showToast({
              title: message,
              icon: "none",
              duration: 2000
            })
            return
          }
          uni.setStorageSync("openid", data?.openid)
          const params = {
            openId: data?.openid,
            code: e.detail.code
          }

          const phoneResult = await getPhoneNumber(params)
          console.log("2 getPhoneNumber:", phoneResult)

          if (!phoneResult || phoneResult.code !== 200) {
            uni.hideLoading()
            uni.showToast({
              title: phoneResult?.message || "获取手机号失败",
              icon: "none",
              duration: 2000
            })
            return
          }

          const token = phoneResult.token || phoneResult.data?.token
          if (!token) {
            uni.hideLoading()
            uni.showToast({
              title: "登录失败：未获取到token",
              icon: "none",
              duration: 2000
            })
            return
          }

          uni.setStorageSync("token", token)
          this.hasLogin = true
          uni.setStorageSync("hasLogin", true)

          uni.hideLoading()
          await this.getProfile()
        } catch (err) {
          uni.hideLoading()
          console.error("登录流程出错:", err)

          const errorMessage = err.message || "登录失败，请重试"
          uni.showToast({
            title: errorMessage,
            icon: "none",
            duration: 2000
          })

          // 清理可能的错误状态
          this.hasLogin = false
          uni.setStorageSync("hasLogin", false)
        }
      },

      async getProfile() {
        console.log("开始获取用户信息")

        try {
          uni.showLoading({
            title: "获取用户信息中...",
            mask: true
          })

          // const userInfo = await getUserInfo()
          const userInfo = await getInfo()
          console.log("用户信息响应:", userInfo)

          if (!userInfo || userInfo.code !== 200) {
            throw new Error(userInfo?.message || "获取用户信息失败")
          }

          // 处理用户信息，比如设置手机号等
          if (userInfo.user?.phonenumber) {
            this.phoneNumber = userInfo.user.phonenumber
						this.userName = userInfo.user.userName
						this.integral = userInfo.user.integral
						this.isInternalUser = userInfo.user.isInternalUser
          }
					
					uni.setStorageSync("phoneNumber", userInfo.user.phonenumber)
					uni.setStorageSync("userId", userInfo.user.userId)
					uni.setStorageSync("userName", userInfo.user.userName)
					uni.setStorageSync("integral", userInfo.user.integral)
					uni.setStorageSync("isInternalUser", userInfo.user.isInternalUser)

          uni.hideLoading()
          console.log("用户信息获取成功")
        } catch (error) {
          uni.hideLoading()
          console.error("获取用户信息失败:", error)

          // 如果获取用户信息失败，可能是token过期，清理登录状态
          /*        uni.showModal({
    title: "提示",
    content: "获取用户信息失败，请重新登录",
    showCancel: false,
    success: () => {
      this.onLoginOut()
    }
  })*/
        }
      },

      onLoginOut() {
        console.log("onLoginOut")
        uni.showModal({
          title: "提示",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              this.hasLogin = false

              auth_token.forEach((item) => {
                uni.removeStorageSync(item)
              })
              uni.showToast({
                title: "退出登录成功",
                icon: "success"
              })
            }
          }
        })
      },

      onOrderItemClick(item) {
        console.log("item >>", item)
        const { type = "" } = item || {}
        if (type) {
          const url = type === "refund" ? "/packages/order/refund-list" : `/packages/order/list?type=${type}`
          uni.navigateTo({ url })
        }
      },

      onOrderMore(type = "order") {
        console.log("onOrderMore", type)
        if (type === "order") {
          uni.navigateTo({ url: "/packages/order/list" })
        }
      },

      onActivityItemClick(item) {
        console.log("item >>", item)
        const urlMap = {
          coupon: "/packages/profile/coupon",
          points: "/packages/profile/points"
        }
        const url = urlMap[item.type]
        if (url) {
          uni.navigateTo({ url })
        }
      },

      onServiceItemClick(item) {
        console.log("item >>", item)
        const urlMap = {
          address: "/packages/profile/address/index",
          couponCode: "/packages/profile/couponCode"
        }
        const url = urlMap[item.type]
        if (url) {
          uni.navigateTo({ url })
        }
      }
    }
  }
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    flex-direction: column;

    .phb-login-view {
      display: flex;
      flex-direction: column;
      margin: 20rpx;
      padding: 20rpx;
      background: rgba(54, 54, 54, 1);
      border-radius: 20rpx;

      .header-block {
        display: flex;
        align-items: center;

        .avatar {
          width: 140rpx;
          height: 140rpx;
          border-radius: 50%;
          overflow: hidden;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .header-btn {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex: 1;
          margin-left: 40rpx;
          border: 0 !important;
          border-radius: 0 !important;
          outline: none !important;
          box-shadow: none !important;
          background: transparent;
          padding: 0;

          .header-name {
            display: inline-block;
            color: white;
            font-size: 36rpx;
            white-space: nowrap;
            pointer-events: none;
          }
        }

        .header-btn::after {
          border: none !important;
        }

        .header-content {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          flex: 1;
          margin-left: 40rpx;

          .content-bar {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 120rpx;

            .name-tips {
              color: white;
              font-size: 36rpx;
            }

            .user-role-block {
              display: flex;
              align-items: center;

              image {
                width: 48rpx;
                height: 48rpx;
                overflow: hidden;
              }

              text {
                height: 48rpx;
                line-height: 48rpx;
                padding: 0 20rpx;
                color: white;
                background: #c2985c 100%;
                font-size: 24rpx;
                margin-left: 10rpx;
                border-radius: 20rpx;
              }
            }
          }

          .login-out {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: rgba(255, 255, 255, 1);
            font-size: 32rpx;
            min-width: 140rpx;
          }
        }
      }

      .header-points {
        display: flex;
        align-items: center;
        margin-top: 20rpx;

        .points-item {
          display: flex;
          align-items: center;

          .icon {
            width: 48rpx;
            height: 48rpx;
            margin-left: 20rpx;
          }

          .text-tips {
            color: rgba(194, 152, 92, 1);
            font-size: 32rpx;
            margin-left: 10rpx;
          }
        }

        .points-item:first-child {
          margin-left: 0;
        }
      }
    }
  }
</style>

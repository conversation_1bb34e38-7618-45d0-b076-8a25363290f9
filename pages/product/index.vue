<template>
	<my-page :refresher-config="{
      enabled: false
    }" :load-more-config="{
      enabled: false
    }" :show-fixed-bar="true" :fixed-bar-height="fixedBarHeight" :showBottomEmptyBlock="false">
		<template #nav>
			<my-nav title="产品" :backIcon="false" bgColor="#F8F8F8" />
		</template>

		<template #fixedBar>
			<view class="product-search" id="search-block">
				<search-bar @onSearch="search" />
			</view>
		</template>

		<view class="content-wrapper">
			<wd-sidebar v-model="active" @change="handleChange">
				<wd-sidebar-item v-for="(item, index) in categories" :key="index" :value="index" :label="item.label"
					:icon="item.icon" :disabled="item.disabled" />
			</wd-sidebar>
			<view class="content">
				<scroll-view v-for="(item, index) in categories" :key="index" class="category" scroll-y scroll-with-animation
					:show-scrollbar="false" :scroll-top="scrollTop" :throttle="false"
					:style="{ display: active === index ? 'block' : 'none' }">
					<view class="product-grid">
						<view v-for="(cell, index) in item.items" :key="index" class="product-item"
							@click="onAction('block', cell)">
							<image class="product-image" :src="cell.pic" mode="aspectFill"></image>
							<text class="product-title">{{ cell.cateName }}</text>
						</view>
					</view>
					<view class="bottom-empty-block"></view>
				</scroll-view>
			</view>
		</view>
	</my-page>
	<my-tabbar tabName="goods" />
</template>

<script>
	import {
		getProductCategories,
		getProductsByCategory,
		getProductCateList
	} from "@/api/product.js"
	import {
		categories
	} from "@/constants/product"

	/**
	 * 商品分类页面api
	 * /store/productCate/list
	 * /store/product/appList
	 */
	export default {
		data() {
			return {
				searchValue: "",
				activeKey: "",

				isFirstShow: false,
				scrollTop: 0,
				active: 0,
				fixedBarHeight: 80,
				categories: categories,
				allData: []
			}
		},

		created() {
			this._getFixHeaderHeight()
		},

		onLoad() {
			this.isFirstShow = true
		},

		onShow() {
			if (this.isFirstShow) {
				this._getProductCategories()
				this.isFirstShow = false
			}
		},

		methods: {
			search() {
			},

			// 获取商品分类
			async _getProductCategories() {
				try {
					const result = await getProductCateList()
					const {
						code,
						msg,
						data,
						rows
					} = result || {}
					this.allData = JSON.parse(JSON.stringify(data))
					let dat = []
					for (let i of data) {
						if (i.pid == '0') {
							dat.push(i)
						}
					}
					const categoryData = dat
					if (code === 200 && categoryData && categoryData.length > 0) {
						// 成功获取分类数据，更新分类列表
						this.categories = categoryData.map((category, index) => ({
							title: category.cateFullName,
							label: category.cateFullName,
							categoryId: category.cateId,
							innerCode: category.innerCode,
							items: [], // 初始为空，点击时加载
							disabled: false
						}))

						// 加载第一个分类的商品
						if (this.categories.length > 0) {
							this._getProductsByCategory(this.categories[0].categoryId, 0)
						}
					} else {
						// 保持原有的 mock 数据
						this.categories = categories
					}
				} catch (error) {
					// 保持原有的 mock 数据
					this.categories = categories
				}
			},

			// 根据分类获取商品
			async _getProductsByCategory(categoryId, categoryIndex) {
				let secArr = []
				for (let i of this.allData) {
					if (i.pid === categoryId) {
						secArr.push(i)
					}
				}
				this.categories[categoryIndex].items = secArr
			},

			handleChange({
				value
			}) {
				// allData
				this.active = value
				this.scrollTop = -1
				this.$nextTick(() => {
					this.scrollTop = 0
				})

				// 如果该分类还没有加载商品，则加载
				const category = this.categories[value]
				this._getProductsByCategory(category.categoryId, value)
				// if (category && category.items.length === 0 && category.categoryId) {
				// 	this._getProductsByCategory(category.innerCode, value)
				// }
			},

			onAction(type = "", item = {}) {
				switch (type) {
					case "block":
						uni.navigateTo({
							url: `/packages/shop/pShop?cateId=${item.cateId}&cateName=${item.cateName}`
						})
						break

					default:
						break
				}
			},

			_getFixHeaderHeight() {
				uni
					.createSelectorQuery()
					.select("#search-block")
					.boundingClientRect((rect) => {
						if (rect) {
							this.fixedBarHeight = rect.height
						} else {
						}
					})
					.exec()
			}
		}
	}
</script>

<style>
	.wd-sidebar-item {
		display: -webkit-box;
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		white-space: nowrap;
	}
</style>
<style lang="scss" scoped>
	.product-search {
		padding: 0 20rpx;
		background: #f8f8f8;
	}

	.content-wrapper {
		display: flex;
		height: 100%;
		overflow: hidden;

		.content {
			flex: 1;
			background: #fff;

			.category {
				height: 100%;
				box-sizing: border-box;
			}

			.product-grid {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 20rpx;
				padding: 20rpx;

				.product-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					background: #fff;
					border-radius: 12rpx;
					padding: 16rpx;
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;

					.product-image {
						height: 160rpx;
						width: 200rpx;
						border-radius: 8rpx;
						object-fit: cover;
					}

					.product-title {
						display: inline-block;
						margin-top: 12rpx;
						font-size: 28rpx;
						color: #333;
						text-align: center;
					}
				}
			}

			.bottom-empty-block {
				height: 150rpx;
			}
		}
	}
</style>
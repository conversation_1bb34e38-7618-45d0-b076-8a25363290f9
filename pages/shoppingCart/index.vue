<template>
	<view>
		<view class="footer-view">
			<view>
				<wd-checkbox v-model="checkedAllStatus" @change="caOptions" />
			</view>
			<view>
				已选{{checkedInShop.num}}件商品，合计: 
				<text>¥{{checkedInShop.price}}</text>
			</view>
			<view @tap="goPay">结算 ({{checkedInShop.num}})</view>
		</view>

		<my-page :refresher-config="{
      enabled: true
    }" :load-more-config="{
      enabled: false
    }" :has-tab-bar="true" :show-fixed-bar="true" :fixed-bar-height="fixedBarHeight" :show-back-to-top="true">
			<template #nav>
				<my-nav title="购物车" />
			</template>

			<template #fixedBar>
				<view class="fixed-bar">
					<view class="shop-top-number-view">全部（{{ topTotalNumber }}）</view>
				</view>
			</template>

			<view class="content">
				<view class="brand-view" v-for="item in goodsList" :key="item.accountUnit">
					<view class="cart-card">
						<view class="brand-title-view">
							<wd-checkbox v-model="item.isCheckedAll" @change="onCheckChange(item, item.isCheckedAll)" />
							<!-- <image src="@/static/logo.png" mode="widthFix" /> -->
							<text>{{ item.accountUnit }}</text>
						</view>

						<view class="goods-items-view" v-for="goods of item.items" :key="goods.cartId" @click.stop="closeOutside">
							<wd-swipe-action>
								<view class="goods-item">
									<view class="goods-check">
										<wd-checkbox v-model="goods.isChecked"
											@change="onItemCheckChange(item, goods, goods.isChecked, item.items.length)" />
									</view>
									<view class="goods-image">
										<image :src="goods.productVo.image" mode="widthFix" />
									</view>
									<view class="goods-option">
										<view class="option-title">{{ goods.productVo.productName }}</view>
										<!-- <view class="option-select" @tap="chooseSelect">
                    <view>
                      <text>
                        {{ goods.options.map((opt) => `${opt.value}`).join(",") }}
                      </text>
                    </view>
                    <view>赠品 x{{ goods.gift }}</view>
                  </view> -->
										<view class="option-step">
											<view class="os-price">
												<view class="os-top-price">
													<text>¥{{ goods.productVo.attrValues[0].insidePrice }}</text>
													<text>到手价</text>
												</view>
												<view class="os-bottom-price">¥{{ goods.productVo.attrValues[0].externalPrice }}</view>
											</view>
											<view class="os-step">
												<text @tap="onHandleNum('reduction', goods.cartId)">-</text>
												<text>{{ goods.cartNum }}</text>
												<text @tap="onHandleNum('plus', goods.cartId)">+</text>
											</view>
										</view>
									</view>
								</view>
								<view slot="right" class="wot-swipe-cell__right">
									<view class="del-cell-block" @tap="handleAction('delete', goods)">
										<wd-icon name="delete" size="22px" color="#ffffff"></wd-icon>
										<text>删除</text>
									</view>
								</view>
							</wd-swipe-action>
						</view>
					</view>
				</view>
			</view>
		</my-page>

		<my-tabbar tabName="cart" />
	</view>
</template>

<script>
	import {
		getCartList,
		removeFromCart,
		updateToCart
	} from "@/api/cart.js"

	/**
	 * 购物车页面api
	 * /store/cart/list
	 * /store/cart/add
	 * /store/cart/remove
	 */
	export default {
		data() {
			return {
				checkedInShop: {
					num: 0,
					price: 0
				},
				checkedAllStatus: false,
				fixedBarHeight: 80,
				goodsList: [],

				topTotalNumber: 0,
				checked: false,
				showSelectPop: false
			}
		},
		created() {
			// console.log("购物车数据", this.goodsList)
		},

		onShow() {
			this._getCartList()
		},
		methods: {
			goPay() {
				let list = JSON.parse(JSON.stringify(this.goodsList))
				for (let i of list) {
					let arr = []
					for (let j of i.items) {
						if(j.isChecked) {
							j = { ...j, ...j.productVo }
							arr.push(j)
						}
					}
					i.items = arr
				}
				let arr = []
				for(let i of list) {
					if(i.items.length>0) {
						arr.push(i)
					}
				}
				uni.setStorageSync('startPayData', JSON.stringify(arr))
				uni.navigateTo({
					url: '/packages/order/waitPay'
				})
				console.log(arr)
			},
			caOptions() {
				for (let i of this.goodsList) {
					i.isCheckedAll = this.checkedAllStatus
					for (let j of i.items) {
						j.isChecked = this.checkedAllStatus
					}
				}
				this.coumNumPrice()
			},
			coumNumPrice() {
				let num = 0
				let price = 0
				for (let i of this.goodsList) {
					for (let j of i.items) {
						if(j.isChecked) {
							num += 1;
							price += j.productVo.attrValues[0].insidePrice
						}
					}
				}
				this.checkedInShop = {
					num: num,
					price: price
				}
			},
			classifyByAccountUnit(arr) {
				const classificationMap = {};
				arr.forEach(item => {
					const accountUnit = item.productVo?.accountUnit || 'unknown';
					if (!classificationMap[accountUnit]) {
						classificationMap[accountUnit] = {
							accountUnit: accountUnit,
							isCheckedAll: false,
							items: []
						};
					}
					item.isChecked = false
					classificationMap[accountUnit].items.push(item);
				});
				return Object.values(classificationMap);
			},
			// 获取购物车列表
			async _getCartList() {
				try {
					const result = await getCartList({
						userId: uni.getStorageSync("userId")
					})
					const {
						code,
						msg,
						data,
						rows
					} = result || {}
					const cartData = rows

					if (code === 200 && cartData && cartData.length > 0) {
						// 成功获取购物车数据，转换数据格式
						this.goodsList = this.classifyByAccountUnit(cartData)
						console.log(this.classifyByAccountUnit(cartData))
					} else {
						console.log("购物车接口返回空数据，使用默认数据:", msg)
						// 保持原有的 mock 数据
					}
				} catch (error) {
					console.log("获取购物车数据失败，使用默认数据:", error)
					// 保持原有的 mock 数据
				}
			},

			// 转换购物车数据格式
			_transformCartData(cartData) {
				// 按店铺分组
				const storeMap = new Map()

				cartData.forEach((item) => {
					// const storeId = item.storeId || "default"
					// const storeName = item.storeName || "默认店铺"

					// if (!storeMap.has(storeId)) {
					//   storeMap.set(storeId, {
					//     isCheckedAll: false,
					//     storeName: storeName,
					//     storeIcon: "@/static/logo.png",
					//     goodsId: storeId,
					//     items: []
					//   })
					// }

					// storeMap.get(storeId).items.push({
					//   id: item.id || item.cartId,
					//   isChecked: false,
					//   title: item.productName || item.title || "商品名称",
					//   cover: item.productImage || item.cover || "../../static/logo.png",
					//   price: item.price || 0,
					//   originalPrice: item.originalPrice || item.price || 0,
					//   options: item.options || [{ name: "规格", value: item.spec || "默认规格" }],
					//   gift: item.gift || 0,
					//   quantity: item.quantity || 1,
					//   cartId: item.id || item.cartId
					// })
				})

				return Array.from(storeMap.values())
			},

			// 删除购物车商品
			async _removeCartItem(cartIds) {
				try {
					const {
						code,
						msg
					} = await removeFromCart({
						cartIds
					})
					if (code === 200) {
						uni.showToast({
							title: "删除成功",
							icon: "success"
						})
						// 重新加载购物车数据
						this._getCartList()
					} else {
						uni.showToast({
							title: msg || "删除失败",
							icon: "none"
						})
					}
				} catch (error) {
					console.log("删除购物车商品失败:", error)
					uni.showToast({
						title: "删除失败",
						icon: "none"
					})
				}
			},

			chooseSelect(val) {
				this.showSelectPop = true
			},

			onClose() {
				this.showSelectPop = false
			},
			closeOutside() {},
			handleAction(action, item) {
				if (action === "delete" && item && item.cartId) {
					// 删除购物车商品
					this._removeCartItem([item.cartId])
				} else {
					uni.showToast({
						title: "操作成功",
						icon: "none"
					})
				}
			},

			async onHandleNum(action, id) {
				console.log("onHandleNum action:", action, id)
				// 这里可以添加增加或减少商品数量的逻辑
				const item = this.goodsList.flatMap((store) => store.items).find((item) => item.cartId === id)
				if (item) {
					if (action === "plus") {
						item.cartNum += 1
					} else if (action === "reduction" && item.cartNum > 1) {
						item.cartNum -= 1
					}
				}
				console.log(item)
				const data = await updateToCart({
					cartId: id,
					num: item.cartNum
				})
				this._getCartList()
				this.updateTopTotalNumber()
				// this.coumNumPrice()
			},

			updateTopTotalNumber() {
				this.topTotalNumber = this.goodsList.reduce((total, store) => {
					return (
						total +
						store.items.reduce((sum, item) => {
							return sum + (item.isChecked ? item.quantity : 0)
						}, 0)
					)
				}, 0)
			},

			handleCheckChange({
				value
			}) {
				console.log("handleCheckChange", value)
			},

			onCheckChange(val, type) {
				for (let i of this.goodsList) {
					if (i.accountUnit === val.accountUnit) {
						for (let j of i.items) {
							j.isChecked = type
						}
					}
				}
				this.coumNumPrice()
			},

			onItemCheckChange(val, goods, type, len) {
				let checkedNum = 0
				for (let i of this.goodsList) {
					if (i.accountUnit === val.accountUnit) {
						for (let j of i.items) {
							if (j.cartId === goods.cartId) {
								j.isChecked = type
							}
							if (j.isChecked) {
								checkedNum += 1
							}
						}
					}
				}

				let aType = false
				if (len === checkedNum) {
					aType = true
				}
				for (let i of this.goodsList) {
					if (i.accountUnit === val.accountUnit) {
						i.isCheckedAll = aType
					}
				}
				this.coumNumPrice()
			}
		}
	}
</script>

<style scoped lang="scss">
	.footer-view {
		height: 120rpx;
		width: 100%;
		background: white;
		position: fixed;
		bottom: 160rpx;
		z-index: 999999;
		display: flex;
		align-items: center;
		justify-content: center;
		
		>view:nth-child(1) {
			width: 80rpx;
			text-align: center;
		}
		>view:nth-child(2) {
			flex: 1;
			>text {
				color: #D81E07;
				font-weight: bold;
			}
		}
		>view:nth-child(3) {
			background: #C6A671 100%;
			color: white;
			padding: 10rpx 20rpx;
			border-radius: 30rpx;
			margin: 0 20rpx;
		}
	}

	.fixed-bar {
		display: flex;
		height: 80rpx;
		width: 100%;
		border-bottom: 1rpx solid #f0f0f0;

		.shop-top-number-view {
			display: flex;
			align-items: center;
			flex: 1;
			padding: 0 30rpx;
			background: white;
			color: #dd524d;
			font-weight: bold;
			font-size: 28rpx;
		}
	}

	.content {
		display: flex;
		flex-direction: column;

		.brand-view {
			display: flex;
			flex-direction: column;

			.cart-card {
				display: flex;
				flex-direction: column;

				.brand-title-view {
					display: flex;
					align-items: center;
					padding: 20rpx 30rpx;
					background-color: white;

					image {
						width: 40rpx;
						height: 40rpx;
						margin-left: 10rpx;
						margin-right: 10rpx;
					}
				}

				.goods-items-view {
					display: flex;
					flex-direction: column;
					padding: 20rpx 30rpx;
					background-color: white;
					border-bottom: 1rpx solid #f0f0f0;

					.goods-item {
						display: flex;
						align-items: center;
						border-top-right-radius: 20rpx;
						border-bottom-right-radius: 20rpx;

						.goods-check {
							width: 50rpx;
							margin-left: 5rpx;
							margin-right: 10rpx;
						}

						.goods-image {
							width: 150rpx;
							height: 150rpx;
							display: flex;
							justify-content: center;
							align-items: center;
							padding: 30rpx;

							>image {
								width: 100%;
								max-height: 180rpx;
							}
						}

						.goods-option {
							flex: 1;
							margin-left: 20rpx;
							height: 210rpx;
							overflow: hidden;

							.option-title {
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
								color: rgba(51, 51, 51, 1);
								font-size: 28rpx;
							}

							.option-select {
								display: flex;
								align-items: center;
								margin-top: 30rpx;

								>view {
									font-size: 24rpx;
									color: rgba(16, 16, 16, 1);
								}

								>view:nth-child(1) {
									flex: 1;
									margin-right: 20rpx;

									>text {
										position: relative;
										padding: 5rpx 20rpx;
										background: #dddddd 100%;
										border-radius: 30rpx;

										.opt-icon {
											position: absolute;
											right: 0;
											color: black;
											height: 30rpx;
											width: 30rpx;
											background-color: red;
										}
									}
								}

								>view:nth-child(2) {
									width: 150rpx;
									text-align: center;
									background: #dddddd 100%;
									border-radius: 30rpx;
									padding: 5rpx 0;
								}
							}

							.option-step {
								display: flex;
								align-items: center;

								.os-price {
									flex: 1;

									.os-top-price {
										display: flex;
										align-items: center;
										margin-top: 20rpx;

										>text {
											height: 48rpx;
											line-height: 48rpx;
										}

										>text:nth-child(1) {
											color: rgba(189, 49, 36, 1);
											font-size: 28rpx;
										}

										>text:nth-child(2) {
											border-radius: 30rpx;
											background-color: rgba(252, 236, 180, 1);
											color: rgba(71, 51, 1, 1);
											font-size: 24rpx;
											padding: 0 20rpx;
											margin-left: 20rpx;
										}
									}

									.os-bottom-price {
										color: rgba(102, 102, 102, 1);
										font-size: 24rpx;
									}
								}

								.os-step {
									display: flex;
									width: 150rpx;
									height: 48rpx;
									line-height: 48rpx;
									border: 1rpx solid #c6a670;
									border-radius: 30rpx;
									overflow: hidden;
									text-align: center;
									font-size: 26rpx;

									>text:nth-child(1) {
										border-right: 1rpx solid #c6a670;
									}

									>text:nth-child(3) {
										border-left: 1rpx solid #c6a670;
									}

									>text:nth-child(1),
									>text:nth-child(3) {
										min-width: 46rpx;
									}

									>text:nth-child(2) {
										flex: 1;
										color: rgba(198, 166, 112, 1);
									}
								}
							}
						}
					}

					.wot-swipe-cell__right {
						display: flex;
						flex-direction: column;
						width: 25vw;
						height: 100%;
						background-color: #dd524d;
						border-left: white;

						.del-cell-block {
							display: flex;
							flex-direction: column;
							justify-content: center;
							align-items: center;
							width: 100%;
							height: 100%;

							>text {
								color: white;
								font-size: 28rpx;
							}
						}
					}
				}

				.goods-items-view:last-child {
					border-bottom: none;
					margin-bottom: 30rpx;
				}

				.goods-items-view:first-child {
					margin-top: 20rpx;
				}
			}
		}
	}
</style>
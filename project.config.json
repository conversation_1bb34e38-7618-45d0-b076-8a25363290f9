{"appid": "wx40eecadebffb84bf", "projectname": "internal-purchase", "compileType": "miniprogram", "libVersion": "3.6.6", "packOptions": {"ignore": [], "include": []}, "optimization": {"subPackages": true}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}
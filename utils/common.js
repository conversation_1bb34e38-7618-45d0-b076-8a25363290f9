/**
 * @param url
 * @returns {Promise<void>}
 */
export const openDocument = async ({ url = "" }) => {
  uni.showLoading({ title: "加载中...", mask: true })

  const fileType = url.split(".").pop()
  if (fileType !== "pdf") {
    uni.hideLoading()
    return uni.previewImage({ urls: [url], current: url })
  }

  try {
    const { statusCode, tempFilePath } = await uni.downloadFile({ url })

    if (statusCode !== 200) throw new Error("下载失败")

    await uni.openDocument({
      filePath: tempFilePath,
      fileType
    })
  } catch (err) {
    console.error("文档操作失败:", err)
    uni.showToast({
      title: err.message === "下载失败" ? "文件下载失败" : "打开文档失败",
      icon: "none",
      duration: 2000
    })
  } finally {
    uni.hideLoading()
  }
}

export const getDescByUrl = (url = "") => url.split(".").at(-2).split("/").at(-1)

const IMG_TYPES = ["jpeg", "jpg", "png", "gif", "bmp", "tiff", "tif", "svg", "heic", "heif", "raw"]

export const checkIsImage = (url = "") => IMG_TYPES.includes(url.split(".").pop())

export const deepDeCodeURI = (val = "") => {
  const res = decodeURIComponent(val)
  return res.includes("%") ? deepDeCodeURI(res) : res
}

export function objectToQuery(obj) {
  if (typeof obj !== "object") return ""
  return Object.entries(obj)
    .reduce((query, [key, value]) => `${query}&${key}=${value}`, "")
    .replace("&", "?")
}

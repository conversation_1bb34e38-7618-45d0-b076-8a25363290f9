import { objectToQuery } from "./common.js"

const LOADING_TIPS = {
  GET: "加载中...",
  POST: "提交中..."
}

const CONTENT_TYPE = {
  json: "application/json",
  form: "application/x-www-form-urlencoded",
  multipart: "multipart/form-data"
}

// 请求配置
const REQUEST_CONFIG = {
  timeout: 30000, // 30秒超时
  maxRetries: 2, // 最大重试次数
  retryDelay: 1000 // 重试延迟(ms)
}

/**
 * 验证响应数据格式
 * @param {*} data - 响应数据
 * @returns {boolean} 是否为有效响应
 */
const isValidResponse = (data) => {
  return data && typeof data === "object"
}

/**
 * 验证token是否失效
 * @param data
 * @returns {boolean}
 */
const isValidTokenInvalid = (data) => {
  return data && data.code === 401
}

/**
 * 获取错误信息
 * @param {*} error - 错误对象
 * @returns {string} 错误信息
 */
const getErrorMessage = (error) => {
  if (error?.errMsg) {
    if (error.errMsg.includes("request:fail timeout")) {
      return "请求超时，请检查网络连接"
    }
    if (error.errMsg.includes("request:fail")) {
      return "网络异常，请稍后重试"
    }
  }
  return error?.message || "未知异常"
}

/**
 * 执行单次请求
 * @param {Object} requestParams - 请求参数
 * @param {boolean} showLoading - 是否显示loading
 * @returns {Promise} 请求Promise
 */
const executeRequest = (requestParams, showLoading = true) => {
  return new Promise((resolve, reject) => {
    if (showLoading) {
      uni.showLoading({
        title: LOADING_TIPS[requestParams.method] || "加载中...",
        mask: true
      })
    }

    uni.request({
      ...requestParams,
      timeout: REQUEST_CONFIG.timeout,
      success: ({ data, statusCode }) => {
        if (showLoading) {
          uni.hideLoading()
        }

        console.log("%c [response success]", "background:#810081; color: #fff;", { data, statusCode })

        // 验证响应数据
        if (!isValidResponse(data)) {
          const error = new Error("响应数据格式错误")
          error.statusCode = statusCode
          reject(error)
          return
        }

        if (isValidTokenInvalid(data)) {
          uni.showToast({
            title: "登录已失效，请重新登录",
            icon: "none",
            duration: 2000
          })

          // 延迟1.5s 后跳转到登录页
          // setTimeout(() => {
          //   uni.removeStorageSync("token")
          //   uni.removeStorageSync("hasLogin")
          //   uni.reLaunch({
          //     url: "/pages/personalCenter/index"
          //   })
          // }, 1500)
          return
        }

        resolve(data)
      },
      fail: (err) => {
        if (showLoading) {
          uni.hideLoading()
        }
        console.log("%c [response fail]", "background:#ff4b28ff; color: #fff;", err)
        reject(err)
      }
    })
  })
}

/**
 * 带重试机制的请求函数
 * @param {Object} params - 请求参数
 * @param {number} retryCount - 当前重试次数
 * @returns {Promise} 请求结果
 */
export const request = async (params, retryCount = 0) => {
  const {
    url,
    data,
    method = "GET",
    queryData,
    queryParams,
    header: customHeader,
    showLoading = true,
    enableRetry = true
  } = params || {}

  const queryString = objectToQuery(queryData) || queryParams || ""
  const token = uni.getStorageSync("token") || ""
  const openid = uni.getStorageSync("openid") || data?.openid

	// 这里是需要使用application/json 的url
	const URL_JSON_HEADER_API = [
	  "/store/order/placeOrder",
	  "/store/apply/applyOpenBlueInvoice",
	  "/store/asService/apply",
	  "/store/asService/writeDeliveryNo",
	  "/system/address/add",
	  "/system/address/edit",
	  "/store/afterSale/apply"
	]
	
  // console.log("%c [token,openid]", "background:#00ffa0; color: #fff;", { token, openid })
	const returnIfJson = function(url) {
		let type = false;
		for(let i of URL_JSON_HEADER_API) {
			if(url.includes(i)){
				type = true
			}
		}
		return type
	}
  let header = {
    "Content-Type": returnIfJson(url) ? CONTENT_TYPE.json : CONTENT_TYPE.form
  }

  if (token) {
    // header["Authorization"] = header["X-Access-Token"] = header["token"] = token
    header["Authorization"] = token
  }
  if (openid) {
    header["openid"] = openid
  }
  if (customHeader) {
    header = { ...header, ...customHeader }
  }

  const requestParams = {
    url: `${url}${queryString}`,
    method,
    data,
    header
  }

  console.log("%c [request]", "background:#00a0ff; color: #fff;", {
    ...requestParams,
    retryCount,
    queryData,
    queryParams
  })

  try {
    const result = await executeRequest(requestParams, showLoading)
    return result
  } catch (error) {
    const errorMessage = getErrorMessage(error)

    // 判断是否需要重试
    const shouldRetry =
      enableRetry &&
      retryCount < REQUEST_CONFIG.maxRetries &&
      (error.errMsg?.includes("timeout") || error.errMsg?.includes("request:fail"))

    if (shouldRetry) {
      console.log(`%c [request retry ${retryCount + 1}]`, "background:#ff9500; color: #fff;", errorMessage)

      // 延迟后重试
      await new Promise((resolve) => setTimeout(resolve, REQUEST_CONFIG.retryDelay))
      return request(params, retryCount + 1)
    }

    // 显示错误提示
    if (showLoading) {
      uni.showToast({
        title: errorMessage,
        icon: "error",
        duration: 2000
      })
    }

    throw error
  }
}
